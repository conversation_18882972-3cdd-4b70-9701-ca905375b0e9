package com.example.gymbro.data.title

import com.example.gymbro.core.ai.prompt.builder.CoreChatMessage
import com.example.gymbro.core.conversation.ConversationIdManager
import com.example.gymbro.core.ml.embedding.EmbeddingEngine
import com.example.gymbro.domain.coach.repository.AiStreamRepository
import com.example.gymbro.domain.title.TitleGenerationException
import com.example.gymbro.domain.title.TitleGenerator
import timber.log.Timber
import javax.inject.Inject

/**
 * 🔥 BGE标题生成器实现 - Data层胶水代码
 *
 * 策略：
 * 1. 短消息(≤15字) → BGE语义分析 + AI生成
 * 2. 长消息(>15字) → 智能截取 + "..."
 *
 * 架构职责：
 * - 组合core-ml的EmbeddingEngine和AiStreamRepository
 * - 实现domain层的TitleGenerator接口
 * - 处理异常转换和日志记录
 * - 包含具体的业务逻辑实现
 *
 * @since 富文本功能 - Clean Architecture重构
 */
class BgeTitleGeneratorImpl
@Inject
constructor(
    private val embeddingEngine: EmbeddingEngine,
    private val aiStreamRepository: AiStreamRepository,
    private val conversationIdManager: ConversationIdManager,
) : TitleGenerator {
    companion object {
        private const val TAG = "BgeTitleGenerator"
        private const val MAX_TITLE_LENGTH = 15
    }

    override suspend fun generateTitle(firstUserMessage: String): String {
        Timber.tag(TAG).d("开始生成标题，消息长度: ${firstUserMessage.length}")

        return try {
            when {
                firstUserMessage.length <= MAX_TITLE_LENGTH -> {
                    // 短消息：BGE语义分析 + AI生成
                    generateWithBgeAndAI(firstUserMessage)
                }
                else -> {
                    // 长消息：智能截取
                    generateByTruncation(firstUserMessage)
                }
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "标题生成异常")
            throw TitleGenerationException.ProcessingError("标题生成失败", e)
        }
    }

    /**
     * BGE语义分析 + AI生成标题
     */
    private suspend fun generateWithBgeAndAI(message: String): String {
        Timber.tag(TAG).d("使用BGE+AI生成标题")

        // 1. BGE语义摘要
        val summary = embeddingEngine.summarize(message, MAX_TITLE_LENGTH)

        // 2. 提取关键词
        val keywords = extractKeywords(message)

        // 3. 构建AI提示
        val prompt = buildAiPrompt(message, summary, keywords)

        // 4. AI生成标题
        return generateTitleWithAI(prompt)
    }

    /**
     * 智能截取生成标题
     */
    private fun generateByTruncation(message: String): String {
        // 寻找合适的截取点（句号、问号、感叹号）
        val punctuations = listOf("。", "？", "！", ".", "?", "!")

        for (punct in punctuations) {
            val index = message.indexOf(punct)
            if (index in 5..MAX_TITLE_LENGTH) {
                val title = message.substring(0, index + 1)
                Timber.tag(TAG).d("智能截取标题: $title")
                return title
            }
        }

        // 没有合适标点，直接截取
        val title = message.take(MAX_TITLE_LENGTH) + "..."
        Timber.tag(TAG).d("直接截取标题: $title")
        return title
    }

    /**
     * 提取健身相关关键词
     */
    private fun extractKeywords(message: String): List<String> {
        val keywords = mutableListOf<String>()
        val text = message.lowercase()

        val keywordMap =
            mapOf(
                "训练" to listOf("训练", "锻炼", "运动"),
                "营养" to listOf("营养", "饮食", "食物"),
                "减脂" to listOf("减脂", "减肥", "瘦身"),
                "增肌" to listOf("增肌", "增重", "肌肉"),
                "健身" to listOf("健身", "gym"),
                "跑步" to listOf("跑步", "慢跑", "有氧"),
                "瑜伽" to listOf("瑜伽", "yoga"),
            )

        for ((category, terms) in keywordMap) {
            if (terms.any { text.contains(it) }) {
                keywords.add(category)
            }
        }

        return keywords
    }

    /**
     * 构建AI提示词
     */
    private fun buildAiPrompt(
        message: String,
        summary: String,
        keywords: List<String>,
    ): String =
        if (keywords.isNotEmpty()) {
            """
                请为以下健身咨询生成一个简洁的标题(4-8个字)：

                原文："$message"
                摘要："$summary"
                关键词：${keywords.joinToString(", ")}

                要求：
                1. 基于关键词和摘要生成标题
                2. 4-8个字，简洁明了
                3. 只返回标题，不要其他内容
            """.trimIndent()
        } else {
            """
                请为以下健身咨询生成一个简洁的标题(4-8个字)：

                "$message"

                要求：只返回标题，不要其他内容。
            """.trimIndent()
        }

    /**
     * 使用AI生成标题
     */
    private suspend fun generateTitleWithAI(prompt: String): String {
        val titleBuilder = StringBuilder()
        val sessionId = "title_generation_session"
        val userMessageId = "title_user_${System.currentTimeMillis()}"
        val aiResponseId = "title_ai_${System.currentTimeMillis()}"

        try {
            // 🔥 【阶段3重构】使用新的 streamAiResponse 方法
            val messages = listOf(
                CoreChatMessage(
                    role = "user",
                    content = prompt
                )
            )

            // 创建或获取消息上下文
            val messageContext = conversationIdManager.getMessageContext(aiResponseId)
                ?: conversationIdManager.createMessageContext(sessionId)

            aiStreamRepository
                .streamAiResponse(
                    messageContext = messageContext,
                    messages = messages,
                    taskType = com.example.gymbro.domain.coach.model.AiTaskType.TITLE_GENERATION,
                ).collect { streamEvent ->
                    when (streamEvent) {
                        is com.example.gymbro.domain.coach.model.StreamEvent.Chunk -> {
                            titleBuilder.append(streamEvent.content)
                        }
                        is com.example.gymbro.domain.coach.model.StreamEvent.Done -> {
                            // 流式完成，退出收集
                            return@collect
                        }
                        is com.example.gymbro.domain.coach.model.StreamEvent.Error -> {
                            throw TitleGenerationException.NetworkError(
                                Exception("AI生成失败: ${streamEvent.error}"),
                            )
                        }
                        is com.example.gymbro.domain.coach.model.StreamEvent.Thinking -> {
                            // 忽略思考事件，继续等待内容
                            Timber.tag(TAG).d("AI开始思考标题...")
                        }
                        else -> {
                            // 忽略其他事件类型
                        }
                    }
                }

            val title = titleBuilder.toString().trim()

            if (title.isBlank()) {
                throw TitleGenerationException.EmptyResponseError()
            }

            Timber.tag(TAG).d("AI生成成功: $title")
            return title
        } catch (e: TitleGenerationException) {
            // 重新抛出已知异常
            throw e
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "AI标题生成过程异常")
            throw TitleGenerationException.ProcessingError("AI标题生成失败", e)
        }
    }
}
