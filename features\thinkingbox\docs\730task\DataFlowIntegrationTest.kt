package com.example.gymbro.features.thinkingbox.docs.task730

import com.example.gymbro.core.logging.GymBroLogTags
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 数据流集成测试工具 - 验证完整的数据流路径
 *
 * 🎯 核心职责：
 * - 模拟完整的 Coach → ThinkingBox 数据流
 * - 验证每个环节的数据传递正确性
 * - 检测 JSON 解析改进效果
 * - 确认 perthink 阶段功能正常
 *
 * 🔥 测试流程：
 * 1. 模拟 Coach 生成 messageId 和发起 AI 请求
 * 2. 验证 core-network 接收和处理
 * 3. 检查 DirectOutputChannel 数据传输
 * 4. 确认 ThinkingBox 激活和解析
 * 5. 验证 perthink 阶段创建
 */
@Singleton
class DataFlowIntegrationTest @Inject constructor(
    private val dataFlowValidator: DataFlowValidator
) {

    companion object {
        private val TAG = GymBroLogTags.ThinkingBox.DEBUG
        private const val TEST_PREFIX = "🧪 [集成测试]"
    }

    /**
     * 执行完整的数据流集成测试
     */
    fun executeFullDataFlowTest(): TestResult {
        Timber.tag(TAG).i("$TEST_PREFIX 开始执行完整数据流集成测试")
        
        val testMessageId = generateTestMessageId()
        val testSessionId = generateTestSessionId()
        
        return try {
            // 阶段 1: 模拟 Coach 消息生成
            testCoachMessageGeneration(testMessageId, testSessionId)
            
            // 阶段 2: 模拟 AI 请求流程
            testAiRequestFlow(testMessageId)
            
            // 阶段 3: 验证 JSON 解析改进
            testJsonParsingImprovements(testMessageId)
            
            // 阶段 4: 验证 ThinkingBox 激活
            testThinkingBoxActivation(testMessageId, testSessionId)
            
            // 阶段 5: 验证 perthink 阶段
            testPerthinkStageCreation(testMessageId)
            
            // 生成测试报告
            generateTestReport(testMessageId)
            
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "$TEST_PREFIX 集成测试失败: messageId=$testMessageId")
            TestResult.Failed(testMessageId, e.message ?: "未知错误")
        }
    }

    /**
     * 测试 Coach 消息生成阶段
     */
    private fun testCoachMessageGeneration(messageId: String, sessionId: String) {
        Timber.tag(TAG).i("$TEST_PREFIX 阶段1: 测试 Coach 消息生成")
        
        // 模拟 Coach 生成 messageId
        dataFlowValidator.validateCoachMessageGeneration(messageId, sessionId)
        
        // 验证 messageId 格式
        require(messageId.isNotBlank()) { "messageId 不能为空" }
        require(messageId.length >= 10) { "messageId 长度不足" }
        
        Timber.tag(TAG).i("$TEST_PREFIX ✅ Coach 消息生成测试通过")
    }

    /**
     * 测试 AI 请求流程
     */
    private fun testAiRequestFlow(messageId: String) {
        Timber.tag(TAG).i("$TEST_PREFIX 阶段2: 测试 AI 请求流程")
        
        // 模拟 AiStreamRepository 调用
        dataFlowValidator.validateAiStreamRepositoryCall(messageId, "测试请求")
        
        // 模拟 Core-Network 接收
        dataFlowValidator.validateCoreNetworkReceived(messageId, 1024)
        
        // 模拟 DirectOutputChannel 发送
        dataFlowValidator.validateDirectOutputChannelSent(messageId, "测试token内容")
        
        Timber.tag(TAG).i("$TEST_PREFIX ✅ AI 请求流程测试通过")
    }

    /**
     * 测试 JSON 解析改进效果
     */
    private fun testJsonParsingImprovements(messageId: String) {
        Timber.tag(TAG).i("$TEST_PREFIX 阶段3: 测试 JSON 解析改进")
        
        // 测试 SSE 格式解析
        val testSseData = """data: {"choices":[{"delta":{"content":"测试内容"}}]}"""
        
        // 验证解析逻辑（这里只是模拟，实际需要调用真实的解析器）
        val isValidSse = testSseData.startsWith("data: ")
        require(isValidSse) { "SSE 格式验证失败" }
        
        // 验证 JSON 结构
        val jsonPart = testSseData.substring(6)
        require(jsonPart.contains("choices")) { "JSON 结构验证失败" }
        
        Timber.tag(TAG).i("$TEST_PREFIX ✅ JSON 解析改进测试通过")
    }

    /**
     * 测试 ThinkingBox 激活
     */
    private fun testThinkingBoxActivation(messageId: String, sessionId: String) {
        Timber.tag(TAG).i("$TEST_PREFIX 阶段4: 测试 ThinkingBox 激活")
        
        // 模拟 StreamAdapter 订阅
        dataFlowValidator.validateStreamAdapterSubscribed(messageId)
        
        // 模拟 ViewModel 激活
        dataFlowValidator.validateViewModelActivated(messageId, sessionId)
        
        // 模拟首个 Token 接收
        dataFlowValidator.validateFirstTokenReceived(messageId, "首个测试token")
        
        Timber.tag(TAG).i("$TEST_PREFIX ✅ ThinkingBox 激活测试通过")
    }

    /**
     * 测试 perthink 阶段创建
     */
    private fun testPerthinkStageCreation(messageId: String) {
        Timber.tag(TAG).i("$TEST_PREFIX 阶段5: 测试 perthink 阶段创建")
        
        // 模拟解析开始
        dataFlowValidator.validateParsingStarted(messageId, "DomainMapper")
        
        // 模拟 perthink Segment 创建
        dataFlowValidator.validateSegmentCreated(messageId, "perthink", "PERTHINK")
        
        // 验证数据流完整性
        val activeFlows = dataFlowValidator.getActiveFlows()
        val flowState = activeFlows[messageId]
        
        require(flowState != null) { "数据流状态不存在" }
        require(flowState.isComplete()) { "数据流未完成" }
        
        Timber.tag(TAG).i("$TEST_PREFIX ✅ perthink 阶段创建测试通过")
    }

    /**
     * 生成测试报告
     */
    private fun generateTestReport(messageId: String): TestResult {
        val activeFlows = dataFlowValidator.getActiveFlows()
        val flowState = activeFlows[messageId]
        
        if (flowState == null) {
            return TestResult.Failed(messageId, "数据流状态丢失")
        }
        
        val report = TestReport(
            messageId = messageId,
            totalTime = flowState.getElapsedTime(),
            stagesCompleted = countCompletedStages(flowState),
            totalStages = 9,
            errors = flowState.errors.toList(),
            isSuccess = flowState.isComplete() && flowState.errors.isEmpty()
        )
        
        Timber.tag(TAG).i("$TEST_PREFIX 测试报告生成完成:")
        Timber.tag(TAG).i("  - 消息ID: ${report.messageId}")
        Timber.tag(TAG).i("  - 总耗时: ${report.totalTime}ms")
        Timber.tag(TAG).i("  - 完成阶段: ${report.stagesCompleted}/${report.totalStages}")
        Timber.tag(TAG).i("  - 错误数量: ${report.errors.size}")
        Timber.tag(TAG).i("  - 测试结果: ${if (report.isSuccess) "✅ 成功" else "❌ 失败"}")
        
        // 清理测试数据
        dataFlowValidator.cleanupFlow(messageId)
        
        return if (report.isSuccess) {
            TestResult.Success(messageId, report)
        } else {
            TestResult.Failed(messageId, "测试未完全通过，错误: ${report.errors}")
        }
    }

    /**
     * 计算已完成的阶段数
     */
    private fun countCompletedStages(flowState: DataFlowValidator.DataFlowState): Int {
        var count = 0
        if (flowState.coachGenerated) count++
        if (flowState.aiStreamRepositoryCalled) count++
        if (flowState.coreNetworkReceived) count++
        if (flowState.directOutputChannelSent) count++
        if (flowState.streamAdapterSubscribed) count++
        if (flowState.viewModelActivated) count++
        if (flowState.firstTokenReceived) count++
        if (flowState.parsingStarted) count++
        if (flowState.segmentCreated) count++
        return count
    }

    /**
     * 生成测试用的 messageId
     */
    private fun generateTestMessageId(): String {
        return "test_msg_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }

    /**
     * 生成测试用的 sessionId
     */
    private fun generateTestSessionId(): String {
        return "test_session_${System.currentTimeMillis()}"
    }

    /**
     * 测试结果
     */
    sealed class TestResult {
        data class Success(val messageId: String, val report: TestReport) : TestResult()
        data class Failed(val messageId: String, val reason: String) : TestResult()
    }

    /**
     * 测试报告
     */
    data class TestReport(
        val messageId: String,
        val totalTime: Long,
        val stagesCompleted: Int,
        val totalStages: Int,
        val errors: List<String>,
        val isSuccess: Boolean
    )
}
