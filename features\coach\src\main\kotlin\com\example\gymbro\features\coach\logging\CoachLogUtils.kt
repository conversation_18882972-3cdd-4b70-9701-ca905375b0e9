package com.example.gymbro.features.coach.logging

import timber.log.Timber

/**
 * Coach模块专用日志工具类
 *
 * 🎯 参考Workout模块的WorkoutLogUtils标准
 * - 提供coach模块统一的日志标签和工具方法
 * - 使用COA前缀标识coach模块日志
 * - 现在作为静态工具类，配合CoachLogTree使用
 * - 统一Coach模块的日志输出格式
 *
 * 🔧 使用示例：
 * ```kotlin
 * Timber.tag(CoachLogUtils.TAG_CORE).i("AI对话开始")
 * Timber.tag(CoachLogUtils.TAG_AI_FLOW).d("Token处理: ${token}")
 * CoachLogUtils.logAiFlow("REQUEST", "用户提问", requestId)
 * ```
 */
object CoachLogUtils {

    // === 简化标签定义（每个文件1-3个核心标签）===

    /** 核心业务流程 */
    const val TAG_CORE = "COA-CORE"

    /** 错误相关 */
    const val TAG_ERROR = "COA-ERROR"

    /** 调试相关 (仅关键调试信息) */
    const val TAG_DEBUG = "COA-DEBUG"

    // === 向后兼容的旧标签（保持现有代码工作）===
    /** @deprecated 使用 TAG_ERROR 替代 */
    const val TAG_COACH_ERROR = "COACH-ERROR"

    /** @deprecated 使用 TAG_CORE 替代 */
    const val TAG_COACH_NEW = "COACH-NEW"

    /** @deprecated 使用 TAG_CORE 替代 */
    const val TAG_PROMPT_BUILDER = "PROMPT-BUILDER"

    /** @deprecated 使用 COA-HISTORY 替代 */
    const val TAG_HISTORY_ACTOR = "HISTORY-ACTOR"

    // === 统一日志方法 - 消除重复信息 ===

    /**
     * 🔥 Coach核心流程跟踪 - 简化版本
     */
    fun logCoachFlow(step: String, description: String, requestId: String = "") {
        val message = buildString {
            append("🔥 [COA-$step] $description")
            if (requestId.isNotEmpty()) append(" - requestId=$requestId")
        }
        Timber.tag(TAG_CORE).i(message)
    }

    /**
     * 🔥 Coach错误跟踪
     */
    fun logCoachError(step: String, description: String, error: String, requestId: String = "") {
        val message = buildString {
            append("❌ [COA-$step] $description - ERROR: $error")
            if (requestId.isNotEmpty()) append(" - requestId=$requestId")
        }
        Timber.tag(TAG_ERROR).e(message)
    }

    /**
     * 统一记录对话信息
     */
    fun logConversationInfo(action: String, messageCount: Int, context: String) {
        Timber.tag(TAG_CORE).i("🔥 [$context] $action (${messageCount}条消息)")
    }

    /**
     * 🔥 快速日志方法 - 核心业务
     */
    object Core {
        fun info(message: String) = Timber.tag(TAG_CORE).i(message)
        fun debug(message: String) = Timber.tag(TAG_DEBUG).d(message)
        fun error(message: String, throwable: Throwable? = null) = Timber.tag(TAG_ERROR).e(throwable, message)
        fun warn(message: String) = Timber.tag(TAG_CORE).w(message)
    }

    /**
     * 🔥 性能测量工具
     */
    object PerformanceTracker {
        fun measureTime(operation: String, block: () -> Unit) {
            val startTime = System.currentTimeMillis()
            try {
                block()
                val duration = System.currentTimeMillis() - startTime
                Performance.info("⏱️ [$operation] 执行时间: ${duration}ms")
            } catch (e: Exception) {
                val duration = System.currentTimeMillis() - startTime
                Performance.error("⏱️ [$operation] 执行失败: ${duration}ms", e)
                throw e
            }
        }
    }

    /**
     * 🔥 快速日志方法 - History相关
     * 🎯 统一接入core的timber log动态管理
     */
    object History {
        fun info(message: String) = Timber.tag("COA-HISTORY-CORE").i(message)
        fun debug(message: String) = Timber.tag("COA-HISTORY-CORE").d(message)
        fun error(message: String, throwable: Throwable? = null) = Timber.tag("COA-HISTORY-ERROR").e(throwable, message)
        fun warn(message: String) = Timber.tag("COA-HISTORY-CORE").w(message)

        // 专门的History子功能日志
        fun paging(message: String) = Timber.tag("COA-HISTORY-PAGING").d(message)
        fun database(message: String) = Timber.tag("COA-HISTORY-DB").d(message)
        fun search(message: String) = Timber.tag("COA-HISTORY-SEARCH").d(message)
        fun cache(message: String) = Timber.tag("COA-HISTORY-CACHE").d(message)
        fun ui(message: String) = Timber.tag("COA-HISTORY-UI").d(message)
        fun sync(message: String) = Timber.tag("COA-HISTORY-SYNC").d(message)

        // MVI相关
        fun intent(message: String) = Timber.tag("COA-HISTORY-MVI-INTENT").d(message)
        fun reducer(message: String) = Timber.tag("COA-HISTORY-MVI-REDUCER").d(message)
        fun effect(message: String) = Timber.tag("COA-HISTORY-MVI-EFFECT").d(message)
        fun state(message: String) = Timber.tag("COA-HISTORY-MVI-STATE").d(message)

        // 数据层相关
        fun repo(message: String) = Timber.tag("COA-HISTORY-DATA-REPO").d(message)
        fun usecase(message: String) = Timber.tag("COA-HISTORY-DATA-USECASE").d(message)
        fun mapper(message: String) = Timber.tag("COA-HISTORY-DATA-MAPPER").d(message)
    }
}
