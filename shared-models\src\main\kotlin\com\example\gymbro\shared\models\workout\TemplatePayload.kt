package com.example.gymbro.shared.models.workout

import kotlinx.serialization.Serializable
import java.util.*

/**
 * 模板动态总结信息
 * 🔥 核心数据结构调整：每次保存时自动计算的总结信息
 *
 * @property totalExercises 动作总数（统计 exerciseId 数量）
 * @property totalSets 总组数（统计所有动作的 sets 总和）
 * @property totalVolume 总训练量（计算所有组的 reps × weight 总和）
 * @property estimatedDuration 预估训练时长（基于组数和休息时间计算）
 */
@Serializable
data class TemplateSummary(
    /**
     * 动作总数 - 统计 exerciseId 数量
     */
    val totalExercises: Int,

    /**
     * 总组数 - 统计所有动作的 sets 总和
     */
    val totalSets: Int,

    /**
     * 总训练量 - 计算所有组的 reps × weight 总和
     */
    val totalVolume: Float,

    /**
     * 预估训练时长（分钟）- 基于组数和休息时间计算
     */
    val estimatedDuration: Int,
)

/**
 * 训练模板Payload - 统一JSON表头规范
 *
 * 从WorkoutTemplate重构而来，专注核心业务字段
 * 配合EntityWrapper实现"实体头 + 版本锁"设计
 *
 * 核心变更：
 * - 新增versionTag乐观锁 (并发写入保护)
 * - 新增ownerId所有权字段 (权限控制)
 * - 保留核心模板功能字段
 * - 为AI Function Call优化
 *
 * @property id 模板唯一标识符 (格式: tmpl_userId_uuid)
 * @property name 模板名称
 * @property description 模板描述
 * @property versionTag 乐观锁版本号 (并发写入保护)
 * @property ownerId 所有者ID (用户创建模板的所有者)
 * @property exercises 模板动作列表
 * @property metadata 模板元数据信息
 * @property createdAt 创建时间戳
 * @property updatedAt 更新时间戳
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (JSON表头统一升级)
 */
@Serializable
data class TemplatePayload(
    val id: String,

    /**
     * 模板名称
     */
    val name: String,

    /**
     * 模板描述
     */
    val description: String? = null,

    /**
     * 乐观锁版本号 (新增字段)
     * 每次更新时必须递增，防止并发写入冲突
     */
    val versionTag: Int = 1,

    /**
     * 所有者ID (新增字段)
     * 用于权限控制和数据隔离
     */
    val ownerId: String,

    /**
     * 模板动作列表
     */
    val exercises: List<TemplateExercisePayload>,

    /**
     * 模板元数据
     */
    val metadata: TemplateMetadataPayload,

    /**
     * 创建时间戳
     */
    val createdAt: Long = System.currentTimeMillis(),

    /**
     * 更新时间戳
     */
    val updatedAt: Long = System.currentTimeMillis(),

    /**
     * 🔥 新增：动态总结信息 - 每次保存时自动计算
     */
    val summary: TemplateSummary? = null,
) {
    companion object {
        /**
         * 生成用户模板ID（统一JSON数据中心ID规则）
         * 🎯 使用压缩ID提高可读性
         */
        fun generateUserTemplateId(userId: String): String {
            return "tmpl_${userId}_${java.util.UUID.randomUUID().toString().take(8)}"
        }

        /**
         * 从旧WorkoutTemplate迁移到新TemplatePayload
         */
        fun fromWorkoutTemplate(template: WorkoutTemplate, ownerId: String): TemplatePayload {
            return TemplatePayload(
                id = template.id,
                name = template.name,
                description = template.description,
                versionTag = 1, // 初始版本
                ownerId = ownerId,
                exercises = template.exercises.map { TemplateExercisePayload.fromTemplateExercise(it) },
                metadata = TemplateMetadataPayload.fromTemplateMetadata(template.metadata),
                createdAt = template.createdAt,
                updatedAt = System.currentTimeMillis(),
            )
        }

        /**
         * 创建空白模板Payload
         */
        fun createEmpty(userId: String, name: String): TemplatePayload {
            return TemplatePayload(
                id = generateUserTemplateId(userId),
                name = name,
                ownerId = userId,
                exercises = emptyList(),
                metadata = TemplateMetadataPayload.createDefault(),
            )
        }
    }

    /**
     * 检查所有权 (用于权限控制)
     */
    fun isOwnedBy(userId: String): Boolean = ownerId == userId

    /**
     * 检查是否可以被指定用户修改
     */
    fun canBeModifiedBy(userId: String): Boolean = isOwnedBy(userId)

    /**
     * 获取预计训练时长（分钟）
     */
    fun getEstimatedDuration(): Int {
        return exercises.sumOf { exercise ->
            val setTime = exercise.customSets.size * 30 // 假设每组30秒
            val restTime = (exercise.customSets.size - 1) * exercise.restTimeSeconds
            setTime + restTime
        } / 60 // 转换为分钟
    }

    /**
     * 获取目标肌肉群
     */
    fun getTargetMuscleGroups(): List<String> {
        return metadata.targetMuscleGroups
    }

    /**
     * 获取所需器材
     */
    fun getRequiredEquipment(): List<String> {
        return metadata.equipment
    }

    /**
     * 创建下一个版本（乐观锁）
     */
    fun nextVersion(): TemplatePayload {
        return copy(
            versionTag = versionTag + 1,
            updatedAt = System.currentTimeMillis(),
        )
    }
}

/**
 * 模板动作Payload
 */
@Serializable
data class TemplateExercisePayload(
    val id: String,
    val exerciseId: String, // 动作库关联
    val exerciseName: String, // 从动作库获取
    val imageUrl: String? = null, // 从动作库获取
    val videoUrl: String? = null, // 从动作库获取
    val customSets: List<TemplateSetPayload>, // 用户自定义参数
    val restTimeSeconds: Int = 60,
    val notes: String? = null,
) {
    companion object {
        fun fromTemplateExercise(exercise: TemplateExercise): TemplateExercisePayload {
            return TemplateExercisePayload(
                id = exercise.id,
                exerciseId = exercise.exerciseId,
                exerciseName = exercise.exerciseName,
                imageUrl = exercise.imageUrl,
                videoUrl = exercise.videoUrl,
                customSets = exercise.customSets.map { TemplateSetPayload.fromTemplateSet(it) },
                restTimeSeconds = exercise.restTimeSeconds,
                notes = exercise.notes,
            )
        }
    }

    /**
     * 获取总组数
     */
    fun getTotalSets(): Int = customSets.size

    /**
     * 获取预计执行时间（秒）
     */
    fun getEstimatedTime(): Int {
        val setTime = customSets.size * 30 // 假设每组30秒
        val restTime = (customSets.size - 1) * restTimeSeconds
        return setTime + restTime
    }
}

/**
 * 模板组数配置Payload
 */
@Serializable
data class TemplateSetPayload(
    val setNumber: Int,
    val targetWeight: Float = 0f,
    val targetReps: Int = 0,
    val restTimeSeconds: Int = 60, // 🔥 新增：每组独立的休息时间
    val targetDuration: Int? = null, // 目标时长（秒，用于计时类动作）
    val rpe: Float? = null, // 目标RPE
) {
    companion object {
        fun fromTemplateSet(set: TemplateSet): TemplateSetPayload {
            return TemplateSetPayload(
                setNumber = set.setNumber,
                targetWeight = set.targetWeight,
                targetReps = set.targetReps,
                restTimeSeconds = set.restTimeSeconds, // 🔥 新增：每组独立的休息时间
                targetDuration = set.targetDuration,
                rpe = set.rpe,
            )
        }
    }
}

/**
 * 模板元数据Payload
 */
@Serializable
data class TemplateMetadataPayload(
    val estimatedDuration: Int = 0, // 预计训练时长（分钟）
    val targetMuscleGroups: List<String> = emptyList(), // 目标肌群列表
    val equipment: List<String> = emptyList(), // 所需器材列表
    val tags: List<String> = emptyList(), // 模板标签
) {
    companion object {
        fun fromTemplateMetadata(metadata: TemplateMetadata): TemplateMetadataPayload {
            return TemplateMetadataPayload(
                estimatedDuration = metadata.estimatedDuration,
                targetMuscleGroups = metadata.targetMuscleGroups,
                equipment = metadata.equipment,
                tags = metadata.tags,
            )
        }

        fun createDefault(): TemplateMetadataPayload {
            return TemplateMetadataPayload()
        }
    }
}

/**
 * 🔥 核心功能：TemplatePayload 动态总结计算扩展函数
 */
fun TemplatePayload.calculateSummary(): TemplateSummary {
    val totalExercises = exercises.size
    val totalSets = exercises.sumOf { it.customSets.size }

    // 计算总训练量：所有组的 reps × weight 总和
    val totalVolume = exercises.sumOf { exercise ->
        exercise.customSets.sumOf { set ->
            (set.targetReps * set.targetWeight).toDouble()
        }
    }.toFloat()

    // 预估训练时长：基于组数和平均休息时间计算
    val averageRestTime = if (totalSets > 0) {
        exercises.flatMap { it.customSets }
            .map { it.restTimeSeconds }
            .average()
    } else {
        0.0
    }

    val estimatedDuration = ((totalSets * 2.5) + (totalSets * averageRestTime / 60.0)).toInt() // 分钟

    return TemplateSummary(
        totalExercises = totalExercises,
        totalSets = totalSets,
        totalVolume = totalVolume,
        estimatedDuration = estimatedDuration,
    )
}

/**
 * 🔥 便捷方法：获取带有最新总结信息的模板副本
 */
fun TemplatePayload.withUpdatedSummary(): TemplatePayload {
    return this.copy(summary = calculateSummary())
}
