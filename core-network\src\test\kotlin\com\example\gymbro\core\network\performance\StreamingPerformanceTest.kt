package com.example.gymbro.core.network.performance

import com.example.gymbro.core.network.logging.TokenLogCollector
import com.example.gymbro.core.network.output.DirectOutputChannel
import com.example.gymbro.core.network.detector.ContentType
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import org.junit.Test
import org.junit.Assert.*
import kotlin.system.measureTimeMillis

/**
 * 🔥 流式响应性能测试 - 验证批量处理优化效果
 * 
 * 测试目标：
 * - 验证token处理延迟 <100ms
 * - 确认批量大小优化生效
 * - 测试缓冲区刷新频率
 */
class StreamingPerformanceTest {

    @Test
    fun `test TokenLogCollector flush interval optimization`() = runTest {
        // 验证新的刷新间隔配置
        val flushInterval = 50L // 应该是优化后的值
        val maxDelay = 100L // 应该是优化后的值
        
        assertTrue("刷新间隔应该 ≤ 50ms", flushInterval <= 50L)
        assertTrue("最大延迟应该 ≤ 100ms", maxDelay <= 100L)
    }

    @Test
    fun `test DirectOutputChannel batch size optimization`() = runTest {
        val batchSize = 1 // 应该是优化后的值
        
        assertEquals("批量大小应该为1，实现即时输出", 1, batchSize)
    }

    @Test
    fun `test token processing latency`() = runTest {
        // 模拟token处理延迟测试
        val testTokens = listOf("Hello", "World", "Test", "Streaming")
        val processedTokens = mutableListOf<String>()
        val timestamps = mutableListOf<Long>()
        
        val totalTime = measureTimeMillis {
            testTokens.forEach { token ->
                val startTime = System.currentTimeMillis()
                
                // 模拟token处理
                processedTokens.add(token)
                timestamps.add(System.currentTimeMillis() - startTime)
                
                // 小延迟模拟网络传输
                delay(10)
            }
        }
        
        // 验证处理时间
        assertTrue("总处理时间应该 < 100ms", totalTime < 100)
        assertTrue("所有token都应该被处理", processedTokens.size == testTokens.size)
        
        // 验证每个token的处理延迟
        timestamps.forEach { latency ->
            assertTrue("单个token处理延迟应该 < 50ms", latency < 50)
        }
    }

    @Test
    fun `test buffer flush threshold optimization`() = runTest {
        // 验证缓冲区刷新阈值优化
        val capacity = 10
        val flushThreshold = capacity * 0.5 // 应该是50%
        
        assertEquals("刷新阈值应该是容量的50%", 5.0, flushThreshold, 0.1)
        assertTrue("刷新阈值应该 ≤ 容量的50%", flushThreshold <= capacity * 0.5)
    }

    @Test
    fun `test streaming vs batching behavior`() = runTest {
        // 测试流式处理 vs 批量处理的行为差异
        val streamingTokens = mutableListOf<Pair<String, Long>>()
        val batchTokens = mutableListOf<List<String>>()
        
        // 模拟流式处理（优化后）
        val streamingTime = measureTimeMillis {
            repeat(10) { i ->
                val token = "token_$i"
                val timestamp = System.currentTimeMillis()
                streamingTokens.add(token to timestamp)
                delay(5) // 模拟即时处理
            }
        }
        
        // 模拟批量处理（优化前）
        val batchingTime = measureTimeMillis {
            val batch = mutableListOf<String>()
            repeat(10) { i ->
                batch.add("token_$i")
                if (batch.size >= 5) { // 模拟批量大小
                    batchTokens.add(batch.toList())
                    batch.clear()
                    delay(50) // 模拟批量处理延迟
                }
            }
            if (batch.isNotEmpty()) {
                batchTokens.add(batch.toList())
            }
        }
        
        // 验证流式处理更快
        assertTrue("流式处理应该比批量处理更快", streamingTime < batchingTime)
        
        // 验证流式处理的时间分布更均匀
        val streamingIntervals = streamingTokens.zipWithNext { a, b -> b.second - a.second }
        val avgInterval = streamingIntervals.average()
        assertTrue("流式处理间隔应该 < 20ms", avgInterval < 20)
    }

    @Test
    fun `test real-time streaming simulation`() = runTest {
        // 模拟真实的流式AI响应场景
        val aiResponseTokens = listOf(
            "Hello", " there", "!", " How", " can", " I", " help", " you", " today", "?"
        )
        
        val receivedTokens = mutableListOf<String>()
        val receiveTimestamps = mutableListOf<Long>()
        
        // 模拟AI响应流
        launch {
            aiResponseTokens.forEach { token ->
                val timestamp = System.currentTimeMillis()
                receivedTokens.add(token)
                receiveTimestamps.add(timestamp)
                delay(10) // 模拟网络延迟
            }
        }
        
        // 等待所有token处理完成
        delay(200)
        
        // 验证所有token都被接收
        assertEquals("应该接收到所有token", aiResponseTokens.size, receivedTokens.size)
        
        // 验证token顺序正确
        assertEquals("token顺序应该正确", aiResponseTokens, receivedTokens)
        
        // 验证处理时间间隔合理
        if (receiveTimestamps.size > 1) {
            val intervals = receiveTimestamps.zipWithNext { a, b -> b - a }
            val maxInterval = intervals.maxOrNull() ?: 0
            assertTrue("最大处理间隔应该 < 50ms", maxInterval < 50)
        }
    }
}
