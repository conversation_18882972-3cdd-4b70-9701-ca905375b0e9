package com.example.gymbro.di.core

import com.example.gymbro.core.conversation.ConversationIdManager
import com.example.gymbro.core.util.CompactIdGenerator
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 🔥 【PLAN B 重构】对话ID管理和监控模块
 *
 * 提供统一ID管理和性能监控相关的依赖注入配置：
 * 1. CompactIdGenerator - 压缩ID生成器
 * 2. ConversationIdManager - 统一ID管理器（已通过构造函数注入自动提供）
 * 3. ArchitecturePerformanceMonitor - 架构性能监控器（已通过构造函数注入自动提供）
 *
 * 设计原则：
 * - 单例模式：确保ID管理和监控的一致性
 * - 线程安全：支持并发访问
 * - 性能优化：减少对象创建开销
 * - 低开销监控：监控本身不影响系统性能
 */
@Module
@InstallIn(SingletonComponent::class)
object ConversationModule {

    /**
     * 提供 CompactIdGenerator 实例
     *
     * 将 object 包装为可注入的实例，支持依赖注入
     * 使用单例确保压缩ID映射的一致性
     */
    @Provides
    @Singleton
    fun provideCompactIdGenerator(): CompactIdGenerator {
        return CompactIdGenerator
    }

    // ConversationIdManager 通过 @Inject constructor 自动提供
    // ArchitecturePerformanceMonitor 通过 @Inject constructor 自动提供
    // 不需要手动配置，Hilt 会自动解析构造函数依赖
}
