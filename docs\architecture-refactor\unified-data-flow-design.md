# 🔄 统一数据流架构设计 v2.0

## 📊 架构概述

基于 PLAN B 的统一数据流设计，消除中间层，建立 Coach → Core-Network → ThinkingBox 的直接通信链路。

## 🎯 核心设计原则

### 1. **单一数据路径**
```
用户输入 → Coach → ConversationIdManager → Core-Network → DirectOutputChannel → ThinkingBox
```

### 2. **统一ID管理**
- 消除 `conversationId` 概念
- 统一使用 `messageId` 作为全链路标识
- `sessionId` 仅用于多轮对话分组

### 3. **直接通信**
- 移除所有中间件层（AiResponseReceiver 等）
- Repository 直接调用 Core-Network 服务
- 基于事件的松耦合通信

## 🏗️ 详细架构设计

### 数据流时序图

```mermaid
sequenceDiagram
    participant U as User
    participant C as Coach
    participant CIM as ConversationIdManager
    participant CN as Core-Network
    participant DOC as DirectOutputChannel
    participant TB as ThinkingBox

    U->>C: 发送消息
    C->>CIM: createMessageContext(sessionId)
    CIM-->>C: MessageContext
    
    C->>CN: processAiStreamingResponse(messageContext, prompt)
    CN->>DOC: sendToken(token, messageId)
    
    par 并行处理
        DOC->>TB: 流式token推送
        TB->>TB: 实时渲染
    and
        CN->>C: 处理状态更新
        C->>C: 更新UI状态
    end
    
    TB->>C: MessageProcessingCompleted(messageContext, content)
    C->>CIM: 更新消息历史
```

### 核心组件职责

#### 1. **ConversationIdManager**
```kotlin
// 统一ID管理的单一真实来源
class ConversationIdManager {
    fun createMessageContext(sessionId: String): MessageContext
    fun validateMessageId(messageId: String): Boolean
    fun findBestMatchingMessage(partialId: String): MessageContext?
}
```

#### 2. **Coach Module**
```kotlin
// 简化的Effect处理
sealed interface Effect {
    data class StartAiStream(
        val messageContext: MessageContext,
        val prompt: String
    ) : Effect
    
    data class LaunchThinkingBoxDisplay(
        val messageId: String
    ) : Effect
}
```

#### 3. **Core-Network Module**
```kotlin
// 统一的AI响应处理
class UnifiedAiResponseService {
    suspend fun processAiStreamingResponse(
        messageContext: MessageContext,
        prompt: String
    ): Flow<ProcessingResult>
}
```

#### 4. **ThinkingBox Module**
```kotlin
// 简化的订阅机制
class ThinkingBoxViewModel {
    fun subscribeToMessage(messageId: String)
    fun handleTokenReceived(token: OutputToken)
}
```

## 🔧 实现细节

### 1. **消息上下文传递**

#### 当前方式（复杂）：
```kotlin
// 多个ID参数，容易混淆
fun streamAiResponse(
    sessionId: String,
    userMessageId: String,
    aiResponseId: String,
    conversationId: String,  // 冗余
    prompt: String
)
```

#### 新方式（简化）：
```kotlin
// 单一MessageContext，包含所有必要信息
fun streamAiResponse(
    messageContext: MessageContext,
    prompt: String
)
```

### 2. **错误处理统一化**

```kotlin
// 统一的错误处理模式
sealed class ProcessingResult {
    data class Success(val content: String) : ProcessingResult()
    data class Streaming(val token: String) : ProcessingResult()
    data class Error(val error: UiText) : ProcessingResult()
    data object Completed : ProcessingResult()
}
```

### 3. **性能优化策略**

#### 内存管理：
- ConversationIdManager 定期清理过期消息
- ThinkingBox 使用虚拟化列表渲染
- Coach 历史记录分页加载

#### 网络优化：
- Core-Network 连接池复用
- 流式响应的背压处理
- 智能重试机制

## 📋 迁移检查清单

### 阶段一：基础设施
- [ ] ConversationIdManager 实现完成
- [ ] 统一错误处理模式定义
- [ ] 日志和监控体系建立

### 阶段二：Coach 模块重构
- [ ] Contract 简化完成
- [ ] EffectHandler 重构
- [ ] Repository 直接调用 Core-Network

### 阶段三：中间层移除
- [ ] AiResponseReceiver 删除
- [ ] AiStreamRepositoryImpl 简化
- [ ] 冗余接口清理

### 阶段四：ThinkingBox 集成
- [ ] 订阅机制简化
- [ ] 直接监听 DirectOutputChannel
- [ ] 状态同步优化

### 阶段五：测试验证
- [ ] 端到端流程测试
- [ ] 性能基准测试
- [ ] 错误场景验证

## 🎯 预期收益

### 架构简化：
- 减少 40% 的代码复杂度
- 消除 3 个中间件层
- 统一 ID 管理降低 60% 的传递错误

### 性能提升：
- 减少 2-3 次不必要的数据转换
- 降低 30% 的内存使用
- 提升 50% 的响应速度

### 维护性：
- 单一数据流路径，易于调试
- 统一错误处理，减少异常情况
- 清晰的模块职责，便于扩展

## 🚨 风险评估

### 高风险：
- 大规模重构可能引入新的 bug
- 多模块同时修改，集成复杂度高

### 中风险：
- 现有功能的向后兼容性
- 性能优化可能需要多次迭代

### 低风险：
- 日志和监控的完整性
- 文档和测试的同步更新

## 🛡️ 回滚策略

1. **分阶段部署**：每个阶段都有独立的回滚点
2. **功能开关**：使用 feature flag 控制新旧架构切换
3. **数据备份**：重要数据的完整备份机制
4. **监控告警**：实时监控关键指标，异常时自动告警
