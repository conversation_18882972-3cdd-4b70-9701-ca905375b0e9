package com.example.gymbro.features.workout.template.utils

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import com.example.gymbro.domain.workout.model.TemplateDraft
import com.example.gymbro.features.workout.shared.components.drag.DragState
import com.example.gymbro.features.workout.shared.components.drag.DragConfig
import com.example.gymbro.features.workout.shared.components.drag.UnifiedDragHandler
import com.example.gymbro.features.workout.shared.components.drag.DragCompleteResult
import com.example.gymbro.features.workout.shared.components.drag.DragAnimations
import com.example.gymbro.features.workout.template.TemplateContract
import com.example.gymbro.features.workout.template.updateTemplateDragState
import com.example.gymbro.features.workout.template.updateDraftDragState
import com.example.gymbro.shared.models.workout.WorkoutTemplateDto
import timber.log.Timber

/**
 * TemplateScreen专用拖拽处理器
 *
 * 基于UnifiedDragHandler实现，专门处理TemplateScreen中的模板和草稿拖拽功能。
 * 提供与TemplateEdit模块一致的拖拽体验，包括Material3动画和触觉反馈。
 *
 * 设计原则：
 * - 完全基于UnifiedDragHandler核心引擎
 * - 类型安全的模板和草稿拖拽处理
 * - 与TemplateContract.State完全集成
 * - Material3标准动画和触觉反馈
 * - 性能优化的列表重排序
 */
object TemplateScreenDragHandler {

    // === 模板拖拽处理 ===

    /**
     * 开始模板拖拽
     */
    fun handleTemplateDragStart(
        state: TemplateContract.State,
        template: WorkoutTemplateDto,
        startIndex: Int,
        startPosition: Offset = Offset.Zero
    ): TemplateContract.State {
        Timber.d("TemplateScreenDrag: 开始模板拖拽 templateId=${template.id}, startIndex=$startIndex")

        val newDragState = UnifiedDragHandler.handleDragStart(
            dragState = state.templateDragState,
            item = template,
            itemId = template.id,
            startIndex = startIndex,
            startPosition = startPosition
        )

        return state.updateTemplateDragState(newDragState)
    }

    /**
     * 更新模板拖拽位置
     */
    fun handleTemplateDragMove(
        state: TemplateContract.State,
        currentPosition: Offset,
        targetIndex: Int
    ): TemplateContract.State {
        if (!state.templateDragState.isDragInProgress) {
            return state
        }

        val newDragState = UnifiedDragHandler.handleDragMove(
            dragState = state.templateDragState,
            currentPosition = currentPosition,
            targetIndex = targetIndex,
            itemCount = state.filteredTemplates.size
        )

        return state.updateTemplateDragState(newDragState)
    }

    /**
     * 完成模板拖拽
     */
    fun handleTemplateDragComplete(
        state: TemplateContract.State,
        fromIndex: Int,
        toIndex: Int
    ): TemplateDragResult {
        Timber.d("TemplateScreenDrag: 完成模板拖拽 fromIndex=$fromIndex, toIndex=$toIndex")

        val dragResult = UnifiedDragHandler.handleDragComplete(
            dragState = state.templateDragState,
            items = state.filteredTemplates,
            fromIndex = fromIndex,
            toIndex = toIndex
        )

        val newState = state.updateTemplateDragState(dragResult.newState)

        return TemplateDragResult(
            newState = newState,
            reorderedTemplates = dragResult.reorderedItems,
            dragResult = dragResult.result
        )
    }

    /**
     * 取消模板拖拽
     */
    fun handleTemplateDragCancel(
        state: TemplateContract.State,
        reason: String = "用户取消"
    ): TemplateContract.State {
        Timber.d("TemplateScreenDrag: 取消模板拖拽 reason=$reason")

        val newDragState = UnifiedDragHandler.handleDragCancel(
            dragState = state.templateDragState,
            reason = reason
        )

        return state.updateTemplateDragState(newDragState)
    }

    // === 草稿拖拽处理 ===

    /**
     * 开始草稿拖拽
     */
    fun handleDraftDragStart(
        state: TemplateContract.State,
        draft: TemplateDraft,
        startIndex: Int,
        startPosition: Offset = Offset.Zero
    ): TemplateContract.State {
        Timber.d("TemplateScreenDrag: 开始草稿拖拽 draftId=${draft.id}, startIndex=$startIndex")

        val newDragState = UnifiedDragHandler.handleDragStart(
            dragState = state.draftDragState,
            item = draft,
            itemId = draft.id,
            startIndex = startIndex,
            startPosition = startPosition
        )

        return state.updateDraftDragState(newDragState)
    }

    /**
     * 更新草稿拖拽位置
     */
    fun handleDraftDragMove(
        state: TemplateContract.State,
        currentPosition: Offset,
        targetIndex: Int
    ): TemplateContract.State {
        if (!state.draftDragState.isDragInProgress) {
            return state
        }

        val newDragState = UnifiedDragHandler.handleDragMove(
            dragState = state.draftDragState,
            currentPosition = currentPosition,
            targetIndex = targetIndex,
            itemCount = state.filteredDrafts.size
        )

        return state.updateDraftDragState(newDragState)
    }

    /**
     * 完成草稿拖拽
     */
    fun handleDraftDragComplete(
        state: TemplateContract.State,
        fromIndex: Int,
        toIndex: Int
    ): DraftDragResult {
        Timber.d("TemplateScreenDrag: 完成草稿拖拽 fromIndex=$fromIndex, toIndex=$toIndex")

        val dragResult = UnifiedDragHandler.handleDragComplete(
            dragState = state.draftDragState,
            items = state.filteredDrafts,
            fromIndex = fromIndex,
            toIndex = toIndex
        )

        val newState = state.updateDraftDragState(dragResult.newState)

        return DraftDragResult(
            newState = newState,
            reorderedDrafts = dragResult.reorderedItems,
            dragResult = dragResult.result
        )
    }

    /**
     * 取消草稿拖拽
     */
    fun handleDraftDragCancel(
        state: TemplateContract.State,
        reason: String = "用户取消"
    ): TemplateContract.State {
        Timber.d("TemplateScreenDrag: 取消草稿拖拽 reason=$reason")

        val newDragState = UnifiedDragHandler.handleDragCancel(
            dragState = state.draftDragState,
            reason = reason
        )

        return state.updateDraftDragState(newDragState)
    }

    // === 辅助方法 ===

    /**
     * 检查是否可以开始模板拖拽
     */
    fun canStartTemplateDrag(
        state: TemplateContract.State,
        templateId: String
    ): Boolean {
        return UnifiedDragHandler.canStartDrag(
            dragState = state.templateDragState,
            itemId = templateId,
            items = state.filteredTemplates,
            getId = { it.id }
        )
    }

    /**
     * 检查是否可以开始草稿拖拽
     */
    fun canStartDraftDrag(
        state: TemplateContract.State,
        draftId: String
    ): Boolean {
        return UnifiedDragHandler.canStartDrag(
            dragState = state.draftDragState,
            itemId = draftId,
            items = state.filteredDrafts,
            getId = { it.id }
        )
    }

    /**
     * 获取优化的拖拽配置
     */
    fun getOptimizedDragConfig(
        templateCount: Int,
        draftCount: Int,
        isHighPerformanceDevice: Boolean = true
    ): DragConfig {
        val totalItems = templateCount + draftCount
        return when {
            totalItems > 50 -> DragConfig.Lightweight
            !isHighPerformanceDevice -> DragConfig.Lightweight
            totalItems > 20 -> DragConfig.Material3
            else -> DragConfig.Enhanced
        }
    }

    /**
     * 创建Material3拖拽修饰符
     */
    @Composable
    fun createDragModifier(
        state: TemplateContract.State,
        itemId: String,
        isTemplate: Boolean = true
    ): Modifier {
        val dragState = if (isTemplate) state.templateDragState else state.draftDragState
        val isCurrentlyDragged = dragState.isDragging(itemId)

        return if (isCurrentlyDragged) {
            with(DragAnimations) {
                Modifier.dragAnimated(
                    dragState = dragState,
                    config = state.dragConfig
                )
            }
        } else {
            Modifier
        }
    }
}

/**
 * 模板拖拽结果
 */
data class TemplateDragResult(
    val newState: TemplateContract.State,
    val reorderedTemplates: List<WorkoutTemplateDto>,
    val dragResult: com.example.gymbro.features.workout.shared.components.drag.DragResult<WorkoutTemplateDto>
)

/**
 * 草稿拖拽结果
 */
data class DraftDragResult(
    val newState: TemplateContract.State,
    val reorderedDrafts: List<TemplateDraft>,
    val dragResult: com.example.gymbro.features.workout.shared.components.drag.DragResult<TemplateDraft>
)
