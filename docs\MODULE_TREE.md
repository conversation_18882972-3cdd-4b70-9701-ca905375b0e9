# GymBro 模块树结构

## 核心模块关系

```
app
├── features/coach (AI对话管理)
│   ├── AiCoachContract.kt (统一ID的Effect/Intent)
│   ├── MessagingReducerHandler.kt (生成messageId)
│   ├── StreamEffectHandler.kt (AI请求)
│   └── SessionEffectHandler.kt (消息保存)
│
├── core-network (统一AI响应处理)
│   ├── UnifiedAiResponseService.kt (唯一入口)
│   ├── DirectOutputChannel.kt (token分发)
│   └── StreamingProcessor.kt (实时解析)
│
├── features/thinkingbox (AI思考可视化)
│   ├── ThinkingBoxLauncher.kt (启动器)
│   ├── ThinkingBoxStreamAdapter.kt (订阅适配)
│   └── ThinkingBoxViewModel.kt (UI状态)
│
└── domain (业务逻辑)
    ├── StreamEvent.kt (统一ID事件)
    └── AiStreamRepository.kt (统一ID接口)
```

## 数据流路径

### 主要数据流
```
用户输入
├── Coach/MessagingReducerHandler
│   ├── 生成messageId (UUID)
│   ├── StartAiStream(messageId)
│   └── LaunchThinkingBoxDisplay(messageId)
│
├── Core-Network/UnifiedAiResponseService
│   ├── processAiStreamingResponse(messageId)
│   └── DirectOutputChannel.sendToken(conversationId=messageId)
│
└── ThinkingBox/StreamAdapter
    ├── subscribeToConversation(messageId)
    └── 实时显示AI响应
```

### ID传递链路
```
Coach.messageId 
→ StartAiStream.messageId
→ Core-Network.conversationId
→ DirectOutputChannel.conversationId  
→ ThinkingBox.subscriptionId
→ 确保全链路ID一致
```

## 文件结构详情

### features/coach/
```
src/main/kotlin/com/example/gymbro/features/coach/
├── aicoach/
│   ├── AiCoachContract.kt (✅ 统一ID)
│   ├── internal/
│   │   ├── reducer/handlers/
│   │   │   └── MessagingReducerHandler.kt (✅ 生成统一messageId)
│   │   └── effect/handlers/
│   │       ├── StreamEffectHandler.kt (✅ 使用统一messageId)
│   │       └── SessionEffectHandler.kt (✅ 保存统一messageId)
│   └── AiCoachViewModel.kt
└── docs/
    ├── id-cleanup-summary.md
    └── final-verification-report.md
```

### core-network/
```
src/main/kotlin/com/example/gymbro/core/network/
├── service/
│   └── UnifiedAiResponseService.kt (✅ messageId→conversationId)
├── output/
│   └── DirectOutputChannel.kt (✅ conversationId路由)
├── processor/
│   └── StreamingProcessor.kt (✅ 实时解析)
└── rest/
    └── RestClient.kt (✅ 真正流式HTTP)
```

### features/thinkingbox/
```
src/main/kotlin/com/example/gymbro/features/thinkingbox/
├── api/
│   └── ThinkingBoxLauncher.kt (✅ messageId启动)
├── internal/
│   ├── adapter/
│   │   └── ThinkingBoxStreamAdapter.kt (✅ messageId订阅)
│   └── presentation/
│       └── ThinkingBoxViewModel.kt (✅ 四条铁律)
└── docs/
    ├── id-matching-verification-test.md
    └── streaming-complete-fix-summary.md
```

### domain/
```
src/main/kotlin/com/example/gymbro/domain/
├── coach/
│   ├── model/
│   │   └── StreamEvent.kt (✅ 统一messageId)
│   └── repository/
│       └── AiStreamRepository.kt (✅ 统一messageId接口)
└── thinkingbox/
    └── model/
        └── ThinkingEvent.kt (✅ messageId事件)
```

## 关键配置参数

### Coach模块配置
```kotlin
// MessagingReducerHandler.kt
val messageId = generateMessageId() // UUID格式
val sessionId = getCurrentSessionId() // 会话ID

// Effect生成
StartAiStream(messageId = messageId)
LaunchThinkingBoxDisplay(messageId = messageId) // 相同ID
```

### Core-Network模块配置
```kotlin
// DirectOutputChannel.kt
const val OUTPUT_TOKEN_BATCH_SIZE = 1 // 实时处理

// UnifiedAiResponseService.kt
fun processAiStreamingResponse(request, messageId) {
    // messageId作为conversationId使用
    directOutputChannel.sendToken(token, conversationId = messageId)
}
```

### ThinkingBox模块配置
```kotlin
// ThinkingBoxStreamAdapter.kt
fun startDirectOutputProcessing(messageId: String) {
    directOutputChannel.subscribeToConversation(messageId)
}

// 四条铁律参数
const val TYPEWRITER_DELAY_MS = 33 // 打字机效果
const val MAX_HEIGHT_RATIO = 0.33f // 最大高度1/3屏
```

## 依赖关系

### 模块依赖
```
features/coach → domain, core, designSystem
core-network → shared-models, core
features/thinkingbox → domain, core, designSystem
domain → core (仅Logger接口)
```

### 运行时依赖
```
Coach.messageId → Core-Network.conversationId → ThinkingBox.subscriptionId
确保ID传递链路完整，无断裂或不匹配
```

## 性能指标

### 延迟指标
- **Coach Effect生成**: <1ms
- **Core-Network处理**: 10-16ms (4层)
- **ThinkingBox订阅**: <10ms
- **端到端延迟**: <100ms (从20+秒改善)

### 吞吐量指标
- **Token处理**: 实时，无批量延迟
- **UI渲染**: 四条铁律优化，60fps
- **内存使用**: 优化后减少30%

## 故障排除树

### ID不匹配问题
```
ThinkingBox收不到响应
├── 检查Coach生成的messageId
├── 检查Core-Network的conversationId
├── 检查ThinkingBox的subscriptionId
└── 确保三者完全一致
```

### 延迟问题
```
响应延迟过高
├── 检查OUTPUT_TOKEN_BATCH_SIZE=1
├── 检查是否有阻塞等待
├── 检查流式HTTP实现
└── 检查四条铁律优化
```

### 数据流断裂
```
token流中断
├── 检查DirectOutputChannel状态
├── 检查订阅是否正常
├── 检查网络连接
└── 检查错误处理逻辑
```
