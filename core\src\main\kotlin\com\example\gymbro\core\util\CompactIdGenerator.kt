package com.example.gymbro.core.util

import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.abs

/**
 * UUID压缩工具 - 生成6位字母数字混合ID
 *
 * 🎯 功能特点：
 * - 将UUID压缩为6位字母数字混合ID（如：A1B2C3）
 * - 确保唯一性和可读性，保持基础安全性
 * - 支持反向查找原始UUID
 * - 线程安全设计
 * - 统一的项目ID生成策略
 *
 * 🏗️ 使用场景：
 * - 日志标签中的ID显示
 * - 用户界面中的短ID展示
 * - 调试和错误跟踪
 * - 数据库查询优化
 *
 * 🔧 示例用法：
 * ```kotlin
 * val originalUuid = UUID.randomUUID()
 * val compactId = CompactIdGenerator.generateCompactId(originalUuid)
 * val retrievedUuid = CompactIdGenerator.getOriginalUuid(compactId)
 * ```
 */
object CompactIdGenerator {

    // 字符集：数字和大写字母，避免易混淆字符 (0,O,1,I)
    private const val CHAR_SET = "23456789ABCDEFGHJKLMNPQRSTUVWXYZ"
    private const val CHAR_SET_SIZE = 30 // 30个字符
    private const val COMPACT_ID_LENGTH = 6

    // UUID到压缩ID的映射
    private val uuidToCompactMap = ConcurrentHashMap<String, String>()

    // 压缩ID到UUID的反向映射
    private val compactToUuidMap = ConcurrentHashMap<String, String>()

    // 已使用的压缩ID集合，确保唯一性
    private val usedCompactIds = ConcurrentHashMap.newKeySet<String>()

    /**
     * 从UUID生成6位字母数字混合压缩ID
     *
     * @param uuid 原始UUID
     * @return 6位字母数字混合字符串（如：A1B2C3）
     */
    fun generateCompactId(uuid: UUID): String {
        return generateCompactId(uuid.toString())
    }

    /**
     * 从UUID字符串生成6位字母数字混合压缩ID
     *
     * @param uuidString 原始UUID字符串
     * @return 6位字母数字混合字符串（如：X9Y8Z7）
     */
    fun generateCompactId(uuidString: String): String {
        // 检查是否已经生成过压缩ID
        uuidToCompactMap[uuidString]?.let { return it }

        // 生成新的压缩ID
        val compactId = generateUniqueCompactId(uuidString)

        // 建立双向映射
        uuidToCompactMap[uuidString] = compactId
        compactToUuidMap[compactId] = uuidString
        usedCompactIds.add(compactId)

        return compactId
    }

    /**
     * 根据压缩ID获取原始UUID字符串
     *
     * @param compactId 6位字母数字混合压缩ID
     * @return 原始UUID字符串，如果不存在返回null
     */
    fun getOriginalUuid(compactId: String): String? {
        return compactToUuidMap[compactId]
    }

    /**
     * 检查压缩ID是否存在
     *
     * @param compactId 6位字母数字混合压缩ID
     * @return true如果存在，false如果不存在
     */
    fun hasCompactId(compactId: String): Boolean {
        return compactToUuidMap.containsKey(compactId)
    }

    /**
     * 清理映射缓存（用于测试或内存优化）
     */
    fun clearCache() {
        uuidToCompactMap.clear()
        compactToUuidMap.clear()
        usedCompactIds.clear()
    }

    /**
     * 直接生成6位字母数字混合压缩ID（推荐使用）
     *
     * 不依赖UUID，直接生成唯一的压缩ID
     * 适用于大部分场景，性能更好，无需维护映射关系
     *
     * @return 6位字母数字混合字符串（如：K7M9P2）
     */
    fun generateId(): String {
        return generateRandomBase30()
    }

    /**
     * 生成带前缀的压缩ID
     *
     * @param prefix 前缀字符串
     * @return 带前缀的压缩ID（如：msg_K7M9P2）
     */
    fun generateId(prefix: String): String {
        return "${prefix}_${generateId()}"
    }

    /**
     * 获取当前映射统计信息
     *
     * @return 包含映射数量的统计信息
     */
    fun getStats(): CompactIdStats {
        return CompactIdStats(
            totalMappings = uuidToCompactMap.size,
            memoryUsage = estimateMemoryUsage()
        )
    }

    /**
     * 生成唯一的6位字母数字混合压缩ID
     */
    private fun generateUniqueCompactId(uuidString: String): String {
        // 方法1：基于UUID的hash生成
        var candidateId = generateFromHash(uuidString)

        // 如果发生冲突，使用备用方法
        var attempts = 0
        while (usedCompactIds.contains(candidateId) && attempts < 100) {
            candidateId = generateAlternative(uuidString, attempts)
            attempts++
        }

        // 如果仍然冲突，使用随机生成
        if (usedCompactIds.contains(candidateId)) {
            candidateId = generateRandom()
        }

        return candidateId
    }

    /**
     * 基于UUID的hash生成6位字母数字混合ID
     */
    private fun generateFromHash(uuidString: String): String {
        val hash = abs(uuidString.hashCode())
        return numberToBase30(hash)
    }

    /**
     * 备用生成方法（加入种子防止冲突）
     */
    private fun generateAlternative(uuidString: String, seed: Int): String {
        val combinedHash = abs((uuidString + seed).hashCode())
        return numberToBase30(combinedHash)
    }

    /**
     * 随机生成方法（最后兜底）
     */
    private fun generateRandom(): String {
        var candidateId: String
        do {
            candidateId = generateRandomBase30()
        } while (usedCompactIds.contains(candidateId))

        return candidateId
    }

    /**
     * 将数字转换为30进制的6位字母数字混合ID
     */
    private fun numberToBase30(number: Int): String {
        var num = abs(number)
        val result = StringBuilder()

        // 生成6位ID
        repeat(COMPACT_ID_LENGTH) {
            result.append(CHAR_SET[num % CHAR_SET_SIZE])
            num /= CHAR_SET_SIZE
        }

        return result.toString()
    }

    /**
     * 生成随机的6位字母数字混合ID
     *
     * 使用时间戳和随机数结合，确保更好的唯一性
     */
    private fun generateRandomBase30(): String {
        // 结合时间戳和随机数，提高唯一性
        val timestamp = System.currentTimeMillis()
        val random = kotlin.random.Random.nextInt()
        val combined = abs((timestamp + random).hashCode())

        return numberToBase30(combined)
    }

    /**
     * 估算内存使用量（字节）
     */
    private fun estimateMemoryUsage(): Long {
        // 粗略估算：每个映射约100字节（包含字符串开销）
        return uuidToCompactMap.size * 100L
    }

    /**
     * 压缩ID统计信息
     */
    data class CompactIdStats(
        val totalMappings: Int,
        val memoryUsage: Long
    ) {
        fun toLogString(): String {
            return "CompactId统计: 映射数量=$totalMappings, 内存使用=${memoryUsage}B"
        }
    }
}

/**
 * UUID扩展函数，便于直接调用
 */
fun UUID.toCompactId(): String = CompactIdGenerator.generateCompactId(this)

/**
 * 字符串扩展函数，便于UUID字符串压缩
 */
fun String.toCompactId(): String = CompactIdGenerator.generateCompactId(this)

/**
 * 压缩ID扩展函数，便于反向查找
 */
fun String.toOriginalUuid(): String? = CompactIdGenerator.getOriginalUuid(this)
