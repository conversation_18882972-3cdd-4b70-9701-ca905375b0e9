package com.example.gymbro.domain.coach.usecase

import com.example.gymbro.core.ai.prompt.builder.CoreChatMessage
import com.example.gymbro.core.conversation.ConversationIdManager
import com.example.gymbro.domain.coach.model.AiTaskType
import com.example.gymbro.domain.coach.model.StreamEvent
import com.example.gymbro.domain.coach.repository.AiStreamRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 【阶段3重构】获取AI流式响应用例 - 适配新接口
 *
 * 重构内容：
 * 1. 使用 MessageContext 统一ID管理
 * 2. 适配简化后的 AiStreamRepository 接口
 * 3. 支持消息列表构建，避免重复prompt构建
 *
 * 设计原则：
 * 1. 单一职责 - 只负责获取AI流式响应
 * 2. 依赖倒置 - EffectHandler通过UseCase调用Repository
 * 3. 简洁明了 - 简单包装Repository调用，不添加额外逻辑
 *
 * @since 阶段3 - Repository接口简化
 */
@Singleton
class GetAiStreamUseCase
@Inject
constructor(
    private val aiStreamRepository: AiStreamRepository,
    private val conversationIdManager: ConversationIdManager,
) {
    /**
     * 🔥 【阶段3重构】获取AI流式响应 - 兼容性实现
     *
     * 临时实现：创建简单的消息列表来适配新接口
     * TODO: 后续应该接收已构建的消息列表，避免重复构建
     *
     * @param prompt 用户输入的提示词
     * @param sessionId 会话ID
     * @param userMessageId 用户消息ID
     * @param aiResponseId AI响应ID
     * @return StreamEvent流，包含解析后的哨兵标签事件
     */
    operator fun invoke(
        prompt: String,
        sessionId: String,
        userMessageId: String,
        aiResponseId: String,
    ): Flow<StreamEvent> = kotlinx.coroutines.flow.flow {
        // 创建或获取消息上下文
        val messageContext = conversationIdManager.getMessageContext(aiResponseId)
            ?: conversationIdManager.createMessageContext(sessionId)

        // 创建简单的消息列表
        val messages = listOf(
            CoreChatMessage(
                role = "user",
                content = prompt
            )
        )

        // 调用 suspend 函数并发射结果
        aiStreamRepository.streamAiResponse(
            messageContext = messageContext,
            messages = messages,
            taskType = AiTaskType.CHAT
        ).collect { event ->
            emit(event)
        }
    }
}
