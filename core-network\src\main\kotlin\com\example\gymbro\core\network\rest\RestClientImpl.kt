package com.example.gymbro.core.network.rest

import com.example.gymbro.core.logging.GymBroLogTags
import com.example.gymbro.core.network.config.NetworkConfig
import com.example.gymbro.core.network.config.NetworkConfigManager
import com.example.gymbro.core.network.config.NetworkConnectionLogger
import com.example.gymbro.core.network.monitor.NetworkMonitor
import com.example.gymbro.core.network.rest.interceptors.AuthInterceptor
import com.example.gymbro.core.network.rest.interceptors.NetworkStatusInterceptor
import com.example.gymbro.core.network.rest.interceptors.RetryInterceptor
import com.example.gymbro.core.network.rest.interceptors.SafeLoggingInterceptor
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import timber.log.Timber
import java.io.IOException
import java.util.concurrent.TimeUnit
import kotlin.time.Duration.Companion.milliseconds

/**
 * 🔥 工业级REST客户端 - 动态配置热切换版本
 *
 * 核心改进：
 * 1. 🔥 监听NetworkConfigManager配置变更，自动重建客户端
 * 2. 🔗 记录实际连接日志，确保配置生效溯源
 * 3. ⚡ 热切换：Provider变更时立即更新拦截器链
 * 4. 🛡️ 禁止配置缓存：始终使用最新配置
 */
class RestClientImpl(
    private val baseOkHttpClient: OkHttpClient,
    private val configManager: NetworkConfigManager, // 🔥 注入配置管理器
    private val networkMonitor: NetworkMonitor? = null,
) : RestClient {

    // 🔥 协程作用域用于监听配置变更
    private val configScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    // 🔥 当前生效配置（从configManager获取）
    private var currentConfig: NetworkConfig = configManager.getCurrentConfig()

    // 🔥 动态OkHttpClient - 配置变更时重建
    private var okHttpClient: OkHttpClient = buildOkHttpClient(currentConfig)

    // 🏷️ 【日志统计】请求统计
    @Volatile
    private var totalRequests = 0L
    @Volatile
    private var totalErrors = 0L

    init {
        Timber.tag(GymBroLogTags.CoreNetwork.REST_CLIENT).d("🔥 RestClient初始化 (工业级动态配置版本):")
        Timber.d("  - 配置管理器: ${configManager::class.simpleName}")
        Timber.d("  - 初始配置: ${currentConfig.restBase}")

        // 🔥 启动配置监听
        startConfigMonitoring()
    }

    /**
     * 🔥 监听配置变更并热切换
     */
    private fun startConfigMonitoring() {
        configScope.launch {
            configManager.config.collect { newConfig ->
                Timber.d("🔄 检测到NetworkConfig变更 (REST)")
                handleConfigChange(newConfig)
            }
        }
    }

    /**
     * 🔥 处理配置变更 - 热切换核心逻辑
     */
    private suspend fun handleConfigChange(newConfig: NetworkConfig) {
        val oldConfig = currentConfig

        // 检查是否真的变更了
        if (oldConfig.restBase == newConfig.restBase &&
            oldConfig.apiKey == newConfig.apiKey &&
            oldConfig.connectTimeoutSec == newConfig.connectTimeoutSec &&
            oldConfig.readTimeoutSec == newConfig.readTimeoutSec
        ) {
            Timber.v("📋 REST配置无实质变更，跳过热切换")
            return
        }

        Timber.d("🔄 开始REST配置热切换:")
        Timber.d("  - 旧配置: ${oldConfig.restBase}")
        Timber.d("  - 新配置: ${newConfig.restBase}")

        // 记录配置变更
        NetworkConnectionLogger.logConnectionStateChange(
            type = "REST",
            fromState = "Config(${oldConfig.restBase})",
            toState = "Config(${newConfig.restBase})",
            reason = "NetworkConfigManager配置变更",
        )

        // 🔥 更新当前配置并重建客户端
        currentConfig = newConfig
        okHttpClient = buildOkHttpClient(newConfig)

        Timber.d("✅ REST配置热切换完成，下次请求将使用新配置")
    }

    /**
     * 🔥 构建OkHttpClient - 使用动态配置
     */
    private fun buildOkHttpClient(config: NetworkConfig): OkHttpClient {
        return baseOkHttpClient.newBuilder()
            .connectTimeout(config.connectTimeoutSec.toLong(), TimeUnit.SECONDS)
            .readTimeout(config.readTimeoutSec.toLong(), TimeUnit.SECONDS)
            .writeTimeout(config.writeTimeoutSec.toLong(), TimeUnit.SECONDS)
            .apply {
                // 添加拦截器链：Auth → NetworkStatus → Logging → Retry
                if (config.apiKey.isNotEmpty()) {
                    addInterceptor(AuthInterceptor(config.apiKey))
                }

                networkMonitor?.let { monitor ->
                    addInterceptor(NetworkStatusInterceptor(monitor))
                }

                if (config.enableDebugLogging) {
                    addInterceptor(SafeLoggingInterceptor(config.enableDebugLogging))
                }

                if (config.enableRetry) {
                    addInterceptor(
                        RetryInterceptor(
                            max = config.maxRetries,
                            base = config.retryDelayMs.milliseconds,
                        ),
                    )
                }
            }
            .build()
    }

    override suspend fun get(url: String, headers: Map<String, String>): ApiResult<String> {
        Timber.d("🌐 GET请求: $url")

        // 🔥 记录实际连接日志 - 工业级溯源
        NetworkConnectionLogger.logActualConnection(
            type = "REST",
            actualUrl = url,
            actualApiKey = currentConfig.apiKey,
            provider = "NetworkConfig",
            user = "current_user",
        )

        return safeApiCallString(networkMonitor) {
            val request = Request.Builder()
                .url(url)
                .apply {
                    headers.forEach { (key, value) ->
                        addHeader(key, value)
                        Timber.v("📋 请求头: $key = $value")
                    }
                }
                .build()

            okHttpClient.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    val errorMsg = "HTTP ${response.code}: ${response.message}"
                    Timber.e("🚨 GET请求失败: $errorMsg")
                    throw IOException(errorMsg)
                }

                val responseBody = response.body?.string() ?: ""
                Timber.d("✅ GET请求成功: ${responseBody.length} 字符")
                responseBody
            }
        }
    }

    override suspend fun post(url: String, body: String, headers: Map<String, String>): ApiResult<String> {
        val startTime = System.currentTimeMillis()
        totalRequests++

        Timber.tag(GymBroLogTags.CoreNetwork.REST_CLIENT).i("🌐 [请求开始] POST $url, 总请求数=$totalRequests")

        return safeApiCallString(networkMonitor) {
            val request = Request.Builder()
                .url(url)
                .post(body.toRequestBody("application/json".toMediaType()))
                .apply {
                    headers.forEach { (key, value) ->
                        addHeader(key, value)
                        Timber.v("📋 请求头: $key = $value")
                    }
                }
                .build()

            okHttpClient.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    val errorMsg = "HTTP ${response.code}: ${response.message}"
                    Timber.e("🚨 POST请求失败: $errorMsg")
                    throw IOException(errorMsg)
                }

                val responseBody = response.body?.string() ?: ""
                val processingTime = System.currentTimeMillis() - startTime
                Timber.tag(GymBroLogTags.CoreNetwork.REST_CLIENT).i(
                    "✅ [请求成功] POST 响应=${responseBody.length}字符, 耗时=${processingTime}ms"
                )
                responseBody
            }
        }.also { result ->
            if (result is ApiResult.Error) {
                totalErrors++
                val processingTime = System.currentTimeMillis() - startTime
                Timber.tag(GymBroLogTags.CoreNetwork.REST_CLIENT).e(
                    "❌ [请求失败] POST 错误=${result.error}, 耗时=${processingTime}ms, 错误率=${(totalErrors * 100 / totalRequests)}%"
                )
            }
        }
    }

    override suspend fun put(url: String, body: String, headers: Map<String, String>): ApiResult<String> {
        Timber.d("🌐 PUT请求: $url")
        Timber.v("📝 请求体: ${body.take(200)}${if (body.length > 200) "..." else ""}")

        return safeApiCallString(networkMonitor) {
            val request = Request.Builder()
                .url(url)
                .put(body.toRequestBody("application/json".toMediaType()))
                .apply {
                    headers.forEach { (key, value) ->
                        addHeader(key, value)
                        Timber.v("📋 请求头: $key = $value")
                    }
                }
                .build()

            okHttpClient.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    val errorMsg = "HTTP ${response.code}: ${response.message}"
                    Timber.e("🚨 PUT请求失败: $errorMsg")
                    throw IOException(errorMsg)
                }

                val responseBody = response.body?.string() ?: ""
                Timber.d("✅ PUT请求成功: ${responseBody.length} 字符")
                responseBody
            }
        }
    }

    override suspend fun delete(url: String, headers: Map<String, String>): ApiResult<String> {
        Timber.d("🌐 DELETE请求: $url")

        return safeApiCallString(networkMonitor) {
            val request = Request.Builder()
                .url(url)
                .delete()
                .apply {
                    headers.forEach { (key, value) ->
                        addHeader(key, value)
                        Timber.v("📋 请求头: $key = $value")
                    }
                }
                .build()

            okHttpClient.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    val errorMsg = "HTTP ${response.code}: ${response.message}"
                    Timber.e("🚨 DELETE请求失败: $errorMsg")
                    throw IOException(errorMsg)
                }

                val responseBody = response.body?.string() ?: ""
                Timber.d("✅ DELETE请求成功: ${responseBody.length} 字符")
                responseBody
            }
        }
    }

    override suspend fun postStreamingFlow(url: String, body: String, headers: Map<String, String>): Flow<String> = flow {
        val startTime = System.currentTimeMillis()
        totalRequests++

        Timber.tag(GymBroLogTags.CoreNetwork.REST_CLIENT).i("🌊 [真实流式请求开始] POST $url, 总请求数=$totalRequests")

        // 🔥 记录实际连接日志 - 真实流式请求专用
        NetworkConnectionLogger.logActualConnection(
            type = "REST_STREAMING_FLOW",
            actualUrl = url,
            actualApiKey = currentConfig.apiKey,
            provider = "NetworkConfig",
            user = "current_user",
        )

        try {
            val request = Request.Builder()
                .url(url)
                .post(body.toRequestBody("application/json".toMediaType()))
                .apply {
                    headers.forEach { (key, value) ->
                        addHeader(key, value)
                        Timber.v("📋 流式请求头: $key = $value")
                    }
                }
                .build()

            // 🔥 【真正的流式处理】实时读取每一行SSE数据
            okHttpClient.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    val errorMsg = "HTTP ${response.code}: ${response.message}"
                    Timber.e("🚨 流式POST请求失败: $errorMsg")
                    throw IOException(errorMsg)
                }

                val responseBody = response.body
                if (responseBody == null) {
                    throw IOException("响应体为空")
                }

                // 🔥 【关键修复】实时逐行读取并立即发送，不等待完整响应
                responseBody.source().use { source ->
                    var lineCount = 0
                    while (!source.exhausted()) {
                        val line = source.readUtf8Line()
                        if (line != null) {
                            lineCount++
                            // 实时发送每一行
                            emit(line)

                            // 实时日志记录
                            if (line.startsWith("data: ")) {
                                Timber.v("📡 [实时SSE行] 第${lineCount}行: $line")
                            }

                            // 检查结束标记
                            if (line.contains("[DONE]")) {
                                Timber.d("🏁 [SSE完成] 检测到结束标记，总行数=$lineCount")
                                break
                            }
                        }
                    }

                    val processingTime = System.currentTimeMillis() - startTime
                    Timber.tag(GymBroLogTags.CoreNetwork.REST_CLIENT).i(
                        "✅ [真实流式请求完成] POST 总行数=$lineCount, 耗时=${processingTime}ms"
                    )
                }
            }
        } catch (e: Exception) {
            totalErrors++
            val processingTime = System.currentTimeMillis() - startTime
            Timber.tag(GymBroLogTags.CoreNetwork.REST_CLIENT).e(
                e, "❌ [真实流式请求失败] POST 错误=${e.message}, 耗时=${processingTime}ms"
            )
            throw e
        }
    }.flowOn(Dispatchers.IO)

    override suspend fun postStreaming(url: String, body: String, headers: Map<String, String>): ApiResult<String> {
        val startTime = System.currentTimeMillis()
        totalRequests++

        Timber.tag(GymBroLogTags.CoreNetwork.REST_CLIENT).i("🌊 [流式请求开始] POST $url, 总请求数=$totalRequests")

        // 🔥 记录实际连接日志 - 流式请求专用
        NetworkConnectionLogger.logActualConnection(
            type = "REST_STREAMING",
            actualUrl = url,
            actualApiKey = currentConfig.apiKey,
            provider = "NetworkConfig",
            user = "current_user",
        )

        return safeApiCallString(networkMonitor) {
            val request = Request.Builder()
                .url(url)
                .post(body.toRequestBody("application/json".toMediaType()))
                .apply {
                    headers.forEach { (key, value) ->
                        addHeader(key, value)
                        Timber.v("📋 流式请求头: $key = $value")
                    }
                }
                .build()

            // 🔥 【修复】真正的流式处理 - 逐行读取SSE响应
            okHttpClient.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    val errorMsg = "HTTP ${response.code}: ${response.message}"
                    Timber.e("🚨 流式POST请求失败: $errorMsg")
                    throw IOException(errorMsg)
                }

                val responseBody = response.body
                if (responseBody == null) {
                    throw IOException("响应体为空")
                }

                // 🔥 【关键修复】逐行读取SSE流，而不是等待完整响应
                val sseLines = mutableListOf<String>()
                responseBody.source().use { source ->
                    while (!source.exhausted()) {
                        val line = source.readUtf8Line()
                        if (line != null) {
                            sseLines.add(line)
                            // 实时日志记录每个SSE行
                            if (line.startsWith("data: ")) {
                                Timber.v("📡 [SSE行] $line")
                            }
                        }
                    }
                }

                val fullResponse = sseLines.joinToString("\n")
                val processingTime = System.currentTimeMillis() - startTime
                Timber.tag(GymBroLogTags.CoreNetwork.REST_CLIENT).i(
                    "✅ [流式请求成功] POST SSE行数=${sseLines.size}, 总字符=${fullResponse.length}, 耗时=${processingTime}ms"
                )
                fullResponse
            }
        }.also { result ->
            if (result is ApiResult.Error) {
                totalErrors++
                val processingTime = System.currentTimeMillis() - startTime
                Timber.tag(GymBroLogTags.CoreNetwork.REST_CLIENT).e(
                    "❌ [流式请求失败] POST 错误=${result.error}, 耗时=${processingTime}ms, 错误率=${(totalErrors * 100 / totalRequests)}%"
                )
            }
        }
    }

    // 🗑️ 已清理：所有Legacy方法实现已删除
}
