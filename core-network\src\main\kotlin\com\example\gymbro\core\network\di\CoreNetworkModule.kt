package com.example.gymbro.core.network.di

import android.content.Context
import com.example.gymbro.core.network.config.NetworkConfig
import com.example.gymbro.core.network.config.NetworkConfigManager
import com.example.gymbro.core.network.config.NetworkConfigProvider
import com.example.gymbro.core.network.security.StringXmlEscaper
// 🚀 【730task重构】添加缓冲系统导入（保留有用组件）
import com.example.gymbro.core.network.buffer.PerformanceMonitor
import com.example.gymbro.core.network.buffer.PerformanceMonitorImpl
// 🗑️ SlidingWindowBufferFactory已删除
// 🗑️ 已删除：AdaptiveBufferManager, ProgressiveProtocolDetector, FeatureMatcher, UnifiedTokenReceiver
// 🚀 【730task重构】添加流式处理器导入
import com.example.gymbro.core.network.processor.StreamingProcessor
import com.example.gymbro.core.network.processor.StreamingProcessorImpl
import com.example.gymbro.core.network.processor.ContentExtractor
import com.example.gymbro.core.network.processor.ContentExtractorImpl
import com.example.gymbro.core.network.processor.OutputSanitizer
import com.example.gymbro.core.network.processor.OutputSanitizerImpl
// 🚀 【730task重构】添加直接输出通道导入
import com.example.gymbro.core.network.output.DirectOutputChannel
// 🔥 【RAW TOKEN日志采集】添加Token日志采集系统导入
import com.example.gymbro.core.network.logging.TokenLogCollector
// 🔥 【架构重构】添加统一AI响应服务导入
import com.example.gymbro.core.logging.GymBroLogTags
import com.example.gymbro.core.network.service.UnifiedAiResponseService
import com.example.gymbro.core.network.logging.TokenBuffer
import com.example.gymbro.core.network.logging.NetworkLogTree
// 🚀 【730task重构】添加兼容性实现导入
// JsonContentExtractorCompat 已移除 - 冗余组件，AiResponseReceiver 已完成 JSON 解析
// ThinkingBoxAdapter 已移除 - 简化架构，直接输出到DirectOutputChannel
import com.example.gymbro.core.di.qualifiers.ApplicationScope
import com.example.gymbro.core.network.monitor.AndroidNetworkMonitor
import com.example.gymbro.core.network.monitor.NetworkMonitor
import com.example.gymbro.core.network.monitor.NetworkWatchdog
import com.example.gymbro.core.network.monitor.NetworkWatchdogImpl
// 旧的StreamClientStateProvider已删除

import com.example.gymbro.core.network.rest.RestClient
import com.example.gymbro.core.network.rest.RestClientImpl
import com.example.gymbro.core.network.retry.ExponentialBackoffRetryStrategy
import com.example.gymbro.core.network.retry.NetworkRetryStrategy
import com.example.gymbro.core.network.state.NetworkStateMonitor
import com.example.gymbro.core.network.state.NetworkStateMonitorImpl
// LlmStreamClient已删除，新架构使用UnifiedTokenReceiver

import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Named
import javax.inject.Singleton

/**
 * Core Network模块DI配置 - 专注于HTTP+SSE实现 + 726task Token流系统
 *
 * 🎯 核心功能：
 * 1. 配置OkHttp客户端支持SSE
 * 2. 提供AdaptiveStreamClient作为主要流式客户端
 * 3. 🔥 【726task修复】提供简化的token流处理系统
 * 4. 移除复杂的WebSocket依赖
 * 5. 简化协议检测配置
 */
@Module
@InstallIn(SingletonComponent::class)
object CoreNetworkModule {

    // ==================== 730task RAW TOKEN日志采集系统 ====================

    /**
     * 🔥 【RAW TOKEN日志采集】提供Token缓冲器 - 高性能批量缓存
     */
    @Provides
    @Singleton
    fun provideTokenBuffer(): TokenBuffer {
        Timber.d("🔧 [RAW-TOKEN] 创建TokenBuffer - 高性能Ring Buffer")
        return TokenBuffer(
            capacity = 100,
            maxSizeBytes = 1024 * 1024, // 1MB
        )
    }

    /**
     * 🔥 【RAW TOKEN日志采集】提供Token日志采集器 - 批量采集核心
     */
    @Provides
    @Singleton
    fun provideTokenLogCollector(
        @ApplicationScope applicationScope: CoroutineScope,
    ): TokenLogCollector {
        Timber.d("🔧 [RAW-TOKEN] 创建TokenLogCollector - 批量日志采集器")
        return TokenLogCollector(applicationScope)
    }

    /**
     * 🔥 【RAW TOKEN日志采集】提供网络日志树 - CNET-*标签统一管理
     */
    @Provides
    @Singleton
    fun provideNetworkLogTree(
        loggingConfig: com.example.gymbro.core.logging.LoggingConfig,
    ): NetworkLogTree {
        Timber.d("🔧 [RAW-TOKEN] 创建NetworkLogTree - CNET标签日志管理")
        return NetworkLogTree(loggingConfig)
    }

    // ==================== 730task 智能缓冲系统 ====================

    /**
     * 🚀 【730task重构】提供性能监控器 - 实时性能指标收集
     */
    @Provides
    @Singleton
    fun providePerformanceMonitor(): PerformanceMonitor {
        Timber.d("🔧 [730task] 创建PerformanceMonitor")
        return PerformanceMonitorImpl()
    }

    // 🗑️ AdaptiveBufferManager已删除 - 过度工程，使用Kotlin Flow原生缓冲

    // 🗑️ SlidingWindowBufferFactory已删除 - 与SlidingWindowBuffer一起移除

    // 🗑️ FeatureMatcher和ProgressiveProtocolDetector已删除 - 不必要的协议检测

    /**
     * 🚀 【730task重构】提供内容提取器 - 简化版
     */
    @Provides
    @Singleton
    fun provideContentExtractor(
        @Named("sse_json") json: Json,
    ): ContentExtractor {
        Timber.d("🔧 [730task] 创建ContentExtractor - 简化版")
        return ContentExtractorImpl(json)
    }

    /**
     * 🚀 【730task重构】提供PII数据脱敏器
     */
    @Provides
    @Singleton
    fun providePiiSanitizer(): com.example.gymbro.core.network.security.PiiSanitizer {
        Timber.d("🔧 [730task] 提供PiiSanitizer")
        return com.example.gymbro.core.network.security.PiiSanitizer
    }

    /**
     * 🚀 【730task重构】提供输出净化器
     */
    @Provides
    @Singleton
    fun provideOutputSanitizer(
        piiSanitizer: com.example.gymbro.core.network.security.PiiSanitizer,
    ): OutputSanitizer {
        Timber.d("🔧 [730task] 创建OutputSanitizer")
        return OutputSanitizerImpl(piiSanitizer)
    }

    /**
     * 🚀 【730task重构】提供流式处理器
     */
    @Provides
    @Singleton
    fun provideStreamingProcessor(
        contentExtractor: ContentExtractor,
        outputSanitizer: OutputSanitizer,
    ): StreamingProcessor {
        Timber.d("🔧 [730task] 创建StreamingProcessor")
        return StreamingProcessorImpl(contentExtractor, outputSanitizer)
    }

    /**
     * 🚀 【730task重构】提供直接输出通道
     */
    @Provides
    @Singleton
    fun provideDirectOutputChannel(
        tokenLogCollector: TokenLogCollector, // 🔥 【RAW TOKEN日志采集】注入Token采集器
    ): DirectOutputChannel {
        Timber.d("🔧 [730task] 创建DirectOutputChannel - 集成RAW TOKEN日志采集")
        return DirectOutputChannel(tokenLogCollector)
    }

    // 🗑️ UnifiedTokenReceiver已删除 - 与UnifiedAiResponseService重复，造成双重处理路径

    // 🗑️ ThinkingBoxAdapter已删除 - 造成重复处理路径，ThinkingBox直接使用DirectOutputChannel

    /**
     * 🔥 【架构重构】提供统一AI响应服务 - 集成现有Core-Network组件
     */
    @Provides
    @Singleton
    fun provideUnifiedAiResponseService(
        restClient: RestClient,
        networkConfigManager: NetworkConfigManager,
        directOutputChannel: DirectOutputChannel,
        streamingProcessor: StreamingProcessor,
    ): UnifiedAiResponseService {
        Timber.tag(GymBroLogTags.CoreNetwork.DI_MODULE).d("🔧 [组件创建] UnifiedAiResponseService - 统一AI响应服务")
        return UnifiedAiResponseService(
            restClient,
            networkConfigManager,
            directOutputChannel,
            streamingProcessor,
        )
    }

    // ==================== 730task 新架构专用组件 ====================

    /**
     * 🔥 【726task方案修复】提供StringXmlEscaper - XML安全处理
     */
    @Provides
    @Singleton
    fun provideStringXmlEscaper(): StringXmlEscaper {
        Timber.d("🔧 [726task] 创建StringXmlEscaper")
        return StringXmlEscaper()
    }

    // ==================== 原有网络配置 ====================

    /**
     * 🔥 简化配置管理器 - 减少重复同步
     */
    @Provides
    @Singleton
    fun provideNetworkConfigManager(
        configProvider: NetworkConfigProvider,
    ): NetworkConfigManager {
        Timber.d("🔧 创建简化NetworkConfigManager")
        val manager = NetworkConfigManager()

        // 使用Provider的初始配置初始化Manager
        val initialConfig = configProvider.getConfig()
        manager.switchProvider(initialConfig, "系统初始化")

        // 🔥 简化动态配置同步 - 减少频繁触发
        CoroutineScope(Dispatchers.IO).launch {
            configProvider.observeConfig()
                .distinctUntilChanged() // 🎯 只有配置真正变化时才触发
                .collect { newConfig ->
                    Timber.d("🔄 配置变更同步")
                    manager.switchProvider(newConfig, "配置变更")
                }
        }

        Timber.d("🔧 NetworkConfigManager配置完成")
        return manager
    }

    /**
     * 🔥 提供当前NetworkConfig - 兼容需要直接注入NetworkConfig的地方
     */
    @Provides
    @Singleton
    fun provideCurrentNetworkConfig(
        configManager: NetworkConfigManager,
    ): NetworkConfig {
        return configManager.getCurrentConfig()
    }

    /**
     * 提供专用于SSE的OkHttp客户端
     */
    @Provides
    @Singleton
    @Named("sse_client")
    fun provideSSEOkHttpClient(): OkHttpClient {
        Timber.tag(GymBroLogTags.CoreNetwork.DI_MODULE).d("🔧 [组件创建] SSE专用OkHttpClient")

        return OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS) // 🔥 SSE需要较长的读取超时
            .writeTimeout(30, TimeUnit.SECONDS)
            .callTimeout(120, TimeUnit.SECONDS) // 🔥 SSE流式传输需要更长的调用超时
            .retryOnConnectionFailure(true)
            // 🔥 【HTTP协议限制】强制使用HTTP/1.1，避免HTTP/3协议问题
            .protocols(listOf(okhttp3.Protocol.HTTP_1_1))
            .addInterceptor(
                HttpLoggingInterceptor().apply {
                    level = HttpLoggingInterceptor.Level.HEADERS // 🔥 只记录头部，避免SSE数据污染日志
                },
            )
            // 🔥 【SSL调试】添加网络事件监听器以诊断连接问题
            .eventListener(
                object : okhttp3.EventListener() {
                    override fun connectStart(
                        call: okhttp3.Call,
                        inetSocketAddress: java.net.InetSocketAddress,
                        proxy: java.net.Proxy,
                    ) {
                        Timber.d("🔌 开始连接: ${inetSocketAddress.hostName}:${inetSocketAddress.port}")
                    }

                    override fun secureConnectStart(call: okhttp3.Call) {
                        Timber.d("🔒 开始SSL握手")
                    }

                    override fun secureConnectEnd(
                        call: okhttp3.Call,
                        handshake: okhttp3.Handshake?,
                    ) {
                        Timber.d("🔒 SSL握手完成: ${handshake?.tlsVersion} ${handshake?.cipherSuite}")
                    }

                    override fun connectFailed(
                        call: okhttp3.Call,
                        inetSocketAddress: java.net.InetSocketAddress,
                        proxy: java.net.Proxy,
                        protocol: okhttp3.Protocol?,
                        ioe: java.io.IOException,
                    ) {
                        Timber.e(ioe, "❌ 连接失败: ${inetSocketAddress.hostName}:${inetSocketAddress.port}")
                    }

                    override fun callStart(call: okhttp3.Call) {
                        Timber.d("📞 开始HTTP调用: ${call.request().url}")
                    }

                    override fun responseHeadersEnd(
                        call: okhttp3.Call,
                        response: okhttp3.Response,
                    ) {
                        Timber.d("📡 响应头接收完成: ${response.code} ${response.message}")
                        Timber.d("🔗 使用协议: ${response.protocol}")
                        // 🔥 特别关注alt-svc头
                        response.header("alt-svc")?.let { altSvc ->
                            Timber.w("⚠️ 服务器支持协议升级: $altSvc")
                        }
                    }
                },
            ).build()
    }

    /**
     * 提供REST专用OkHttpClient - 保持现有REST功能
     */
    @Provides
    @Singleton
    @Named("rest_client")
    fun provideRestOkHttpClient(configManager: NetworkConfigManager): OkHttpClient {
        Timber.d("🔧 配置REST专用OkHttpClient (动态配置)")

        // 获取当前配置
        val config = configManager.getCurrentConfig()

        return OkHttpClient.Builder()
            .connectTimeout(config.connectTimeoutSec.toLong(), TimeUnit.SECONDS)
            .readTimeout(config.readTimeoutSec.toLong(), TimeUnit.SECONDS)
            .writeTimeout(config.writeTimeoutSec.toLong(), TimeUnit.SECONDS)
            .retryOnConnectionFailure(config.enableRetry)
            .build()
    }

    /**
     * 提供JSON序列化器
     */
    @Provides
    @Singleton
    @Named("core_network_json")
    fun provideCoreNetworkJson(): Json {
        return Json {
            ignoreUnknownKeys = true
            isLenient = true
            encodeDefaults = true
            prettyPrint = false
            coerceInputValues = true
        }
    }

    /**
     * 提供SSE专用JSON序列化器
     */
    @Provides
    @Singleton
    @Named("sse_json")
    fun provideSSEJson(): Json {
        return Json {
            ignoreUnknownKeys = true
            isLenient = true
            encodeDefaults = true
            prettyPrint = false // 🔥 网络传输不需要格式化
        }
    }

    /**
     * 提供网络监控器
     */
    @Provides
    @Singleton
    fun provideNetworkMonitor(
        @ApplicationContext context: Context,
    ): NetworkMonitor {
        return AndroidNetworkMonitor(context)
    }

    /**
     * 提供网络监控狗
     */
    @Provides
    @Singleton
    fun provideNetworkWatchdog(
        networkMonitor: NetworkMonitor,
    ): NetworkWatchdog {
        return NetworkWatchdogImpl(
            networkMonitor = networkMonitor,
            debounceTimeMs = 2000L,
        )
    }

    // 旧实现已删除，新架构使用UnifiedTokenReceiver作为唯一入口

    /**
     * 🔥 提供RestClient实例 - 使用NetworkConfigManager
     */
    @Provides
    @Singleton
    fun provideRestClientImpl(
        @Named("rest_client") okHttpClient: OkHttpClient,
        @Named("core_network_json") json: Json,
        configManager: NetworkConfigManager,
        networkMonitor: NetworkMonitor,
    ): RestClientImpl {
        Timber.d("🔧 创建RestClient (动态配置版本)")

        return RestClientImpl(
            baseOkHttpClient = okHttpClient,
            configManager = configManager,
            networkMonitor = networkMonitor,
        )
    }

    /**
     * 🔥 提供网络状态监控器
     */
    @Provides
    @Singleton
    fun provideNetworkStateMonitor(
        @ApplicationContext context: Context,
    ): NetworkStateMonitor {
        return NetworkStateMonitorImpl(context)
    }

    /**
     * 🔥 提供网络重试策略
     */
    @Provides
    @Singleton
    fun provideNetworkRetryStrategy(): NetworkRetryStrategy {
        return ExponentialBackoffRetryStrategy(
            initialDelayMs = 1000L,
            maxDelayMs = 30000L,
            maxRetries = 5,
        )
    }

    // StreamClientStateProvider已删除，新架构不需要状态提供者
}

/**
 * Core Network绑定模块
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class CoreNetworkBindingModule {

    @Binds
    @Singleton
    abstract fun bindRestClient(impl: RestClientImpl): RestClient
}
