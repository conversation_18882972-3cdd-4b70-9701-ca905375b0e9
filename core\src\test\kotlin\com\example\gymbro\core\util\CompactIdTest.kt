package com.example.gymbro.core.util

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID

/**
 * CompactIdGenerator测试
 * 验证UUID到字母数字混合压缩ID的转换功能
 */
class CompactIdTest {

    @BeforeEach
    fun setUp() {
        // 清理缓存确保测试独立性
        CompactIdGenerator.clearCache()
    }

    @Test
    fun `generateCompactId should return 6-character alphanumeric ID`() {
        val uuid = UUID.fromString("db63dda7-2ea7-4186-b643-942b48ee99ef")
        val compactId = CompactIdGenerator.generateCompactId(uuid)
        
        // 验证长度
        assertEquals(6, compactId.length)
        
        // 验证字符集（只包含数字和大写字母，不包含易混淆字符）
        val validCharPattern = Regex("^[23456789ABCDEFGHJKLMNPQRSTUVWXYZ]{6}$")
        assertTrue(validCharPattern.matches(compactId), "压缩ID应该只包含有效字符: $compactId")
        
        println("UUID: $uuid 压缩为: $compactId")
    }

    @Test
    fun `generateCompactId should be consistent for same UUID`() {
        val uuid = UUID.fromString("db63dda7-2ea7-4186-b643-942b48ee99ef")
        
        val compactId1 = CompactIdGenerator.generateCompactId(uuid)
        val compactId2 = CompactIdGenerator.generateCompactId(uuid)
        
        assertEquals(compactId1, compactId2, "同一UUID应该生成相同的压缩ID")
    }

    @Test
    fun `generateCompactId should be unique for different UUIDs`() {
        val uuid1 = UUID.fromString("db63dda7-2ea7-4186-b643-942b48ee99ef")
        val uuid2 = UUID.fromString("a1b2c3d4-e5f6-7890-abcd-1234567890ab")
        
        val compactId1 = CompactIdGenerator.generateCompactId(uuid1)
        val compactId2 = CompactIdGenerator.generateCompactId(uuid2)
        
        assertNotEquals(compactId1, compactId2, "不同UUID应该生成不同的压缩ID")
    }

    @Test
    fun `getOriginalUuid should return correct UUID`() {
        val originalUuid = UUID.fromString("db63dda7-2ea7-4186-b643-942b48ee99ef")
        val compactId = CompactIdGenerator.generateCompactId(originalUuid)
        
        val retrievedUuid = CompactIdGenerator.getOriginalUuid(compactId)
        
        assertEquals(originalUuid.toString(), retrievedUuid, "应该能够从压缩ID反向查找到原始UUID")
    }

    @Test
    fun `getOriginalUuid should return null for non-existent compact ID`() {
        val nonExistentId = "XXXXXX"
        val retrievedUuid = CompactIdGenerator.getOriginalUuid(nonExistentId)
        
        assertNull(retrievedUuid, "不存在的压缩ID应该返回null")
    }

    @Test
    fun `hasCompactId should return true for existing ID`() {
        val uuid = UUID.fromString("db63dda7-2ea7-4186-b643-942b48ee99ef")
        val compactId = CompactIdGenerator.generateCompactId(uuid)
        
        assertTrue(CompactIdGenerator.hasCompactId(compactId), "已生成的压缩ID应该存在")
    }

    @Test
    fun `hasCompactId should return false for non-existent ID`() {
        val nonExistentId = "XXXXXX"
        
        assertFalse(CompactIdGenerator.hasCompactId(nonExistentId), "不存在的压缩ID应该返回false")
    }

    @Test
    fun `extension functions should work correctly`() {
        val uuid = UUID.fromString("db63dda7-2ea7-4186-b643-942b48ee99ef")
        val uuidString = uuid.toString()
        
        // 测试UUID扩展函数
        val compactId1 = uuid.toCompactId()
        assertEquals(6, compactId1.length)
        
        // 测试字符串扩展函数
        val compactId2 = uuidString.toCompactId()
        assertEquals(compactId1, compactId2)
        
        // 测试反向查找扩展函数
        val retrievedUuid = compactId1.toOriginalUuid()
        assertEquals(uuidString, retrievedUuid)
    }

    @Test
    fun `stress test - multiple UUIDs should generate unique compact IDs`() {
        val compactIds = mutableSetOf<String>()
        val testCount = 1000
        
        repeat(testCount) {
            val uuid = UUID.randomUUID()
            val compactId = CompactIdGenerator.generateCompactId(uuid)
            
            // 验证格式
            assertEquals(6, compactId.length)
            val validCharPattern = Regex("^[23456789ABCDEFGHJKLMNPQRSTUVWXYZ]{6}$")
            assertTrue(validCharPattern.matches(compactId))
            
            compactIds.add(compactId)
        }
        
        // 验证唯一性（允许少量冲突，但应该很少）
        val uniqueRatio = compactIds.size.toDouble() / testCount
        assertTrue(uniqueRatio > 0.95, "唯一性比例应该大于95%, 实际: ${uniqueRatio * 100}%")
        
        println("生成了 $testCount 个UUID，唯一压缩ID数量: ${compactIds.size}，唯一性: ${String.format("%.2f", uniqueRatio * 100)}%")
    }

    @Test
    fun `getStats should return correct statistics`() {
        // 生成一些映射
        repeat(10) {
            val uuid = UUID.randomUUID()
            CompactIdGenerator.generateCompactId(uuid)
        }
        
        val stats = CompactIdGenerator.getStats()
        
        assertEquals(10, stats.totalMappings)
        assertTrue(stats.memoryUsage > 0)
        
        println(stats.toLogString())
    }

    @Test
    fun `clearCache should remove all mappings`() {
        // 生成一些映射
        val uuid = UUID.randomUUID()
        val compactId = CompactIdGenerator.generateCompactId(uuid)
        
        assertTrue(CompactIdGenerator.hasCompactId(compactId))
        
        // 清理缓存
        CompactIdGenerator.clearCache()
        
        assertFalse(CompactIdGenerator.hasCompactId(compactId))
        assertEquals(0, CompactIdGenerator.getStats().totalMappings)
    }
}