package com.example.gymbro.features.thinkingbox.docs.task730

import com.example.gymbro.core.logging.GymBroLogTags
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 数据流验证器 - 追踪从 core-network 到 ThinkingBox ViewModel 的完整数据流
 *
 * 🎯 核心职责：
 * - 验证 messageId 在整个数据流中的一致性
 * - 追踪数据传输的每个环节
 * - 检测 ID 不一致导致的问题
 * - 提供详细的数据流诊断信息
 *
 * 🔥 验证流程：
 * 1. Coach 生成 messageId
 * 2. AiStreamRepository 传递 messageId 到 core-network
 * 3. UnifiedAiResponseService 处理并发送到 DirectOutputChannel
 * 4. ThinkingBoxStreamAdapter 订阅 DirectOutputChannel
 * 5. ThinkingBoxViewModel 接收并处理数据
 */
@Singleton
class DataFlowValidator @Inject constructor() {

    companion object {
        private val TAG = GymBroLogTags.ThinkingBox.DEBUG
        private const val VALIDATION_PREFIX = "🔍 [数据流验证]"
    }

    // 数据流追踪状态
    private val activeFlows = mutableMapOf<String, DataFlowState>()

    /**
     * 数据流状态
     */
    data class DataFlowState(
        val messageId: String,
        val sessionId: String? = null,
        val startTime: Long = System.currentTimeMillis(),
        var coachGenerated: Boolean = false,
        var aiStreamRepositoryCalled: Boolean = false,
        var coreNetworkReceived: Boolean = false,
        var directOutputChannelSent: Boolean = false,
        var streamAdapterSubscribed: Boolean = false,
        var viewModelActivated: Boolean = false,
        var firstTokenReceived: Boolean = false,
        var parsingStarted: Boolean = false,
        var segmentCreated: Boolean = false,
        var errors: MutableList<String> = mutableListOf()
    ) {
        fun isComplete(): Boolean = viewModelActivated && firstTokenReceived && parsingStarted
        
        fun getElapsedTime(): Long = System.currentTimeMillis() - startTime
        
        fun addError(error: String) {
            errors.add("${System.currentTimeMillis() - startTime}ms: $error")
        }
    }

    /**
     * 1. Coach 生成 messageId
     */
    fun validateCoachMessageGeneration(messageId: String, sessionId: String? = null) {
        val state = activeFlows.getOrPut(messageId) { DataFlowState(messageId, sessionId) }
        state.coachGenerated = true
        
        Timber.tag(TAG).i("$VALIDATION_PREFIX Coach生成messageId: $messageId, sessionId: $sessionId")
        logFlowState(state)
    }

    /**
     * 2. AiStreamRepository 调用
     */
    fun validateAiStreamRepositoryCall(messageId: String, requestDetails: String) {
        val state = activeFlows[messageId]
        if (state == null) {
            Timber.tag(TAG).e("$VALIDATION_PREFIX ❌ AiStreamRepository调用但messageId未在Coach中生成: $messageId")
            return
        }
        
        state.aiStreamRepositoryCalled = true
        Timber.tag(TAG).i("$VALIDATION_PREFIX AiStreamRepository调用: $messageId, 请求详情: $requestDetails")
        logFlowState(state)
    }

    /**
     * 3. Core-Network 接收
     */
    fun validateCoreNetworkReceived(messageId: String, requestSize: Int) {
        val state = activeFlows[messageId]
        if (state == null) {
            Timber.tag(TAG).e("$VALIDATION_PREFIX ❌ Core-Network接收但messageId未追踪: $messageId")
            return
        }
        
        if (!state.aiStreamRepositoryCalled) {
            state.addError("Core-Network接收但AiStreamRepository未调用")
        }
        
        state.coreNetworkReceived = true
        Timber.tag(TAG).i("$VALIDATION_PREFIX Core-Network接收: $messageId, 请求大小: ${requestSize}字节")
        logFlowState(state)
    }

    /**
     * 4. DirectOutputChannel 发送
     */
    fun validateDirectOutputChannelSent(messageId: String, tokenContent: String) {
        val state = activeFlows[messageId]
        if (state == null) {
            Timber.tag(TAG).e("$VALIDATION_PREFIX ❌ DirectOutputChannel发送但messageId未追踪: $messageId")
            return
        }
        
        if (!state.coreNetworkReceived) {
            state.addError("DirectOutputChannel发送但Core-Network未接收")
        }
        
        state.directOutputChannelSent = true
        Timber.tag(TAG).i("$VALIDATION_PREFIX DirectOutputChannel发送: $messageId, token长度: ${tokenContent.length}")
        logFlowState(state)
    }

    /**
     * 5. StreamAdapter 订阅
     */
    fun validateStreamAdapterSubscribed(messageId: String) {
        val state = activeFlows[messageId]
        if (state == null) {
            Timber.tag(TAG).e("$VALIDATION_PREFIX ❌ StreamAdapter订阅但messageId未追踪: $messageId")
            return
        }
        
        state.streamAdapterSubscribed = true
        Timber.tag(TAG).i("$VALIDATION_PREFIX StreamAdapter订阅: $messageId")
        logFlowState(state)
    }

    /**
     * 6. ViewModel 激活
     */
    fun validateViewModelActivated(messageId: String, sessionId: String? = null) {
        val state = activeFlows[messageId]
        if (state == null) {
            Timber.tag(TAG).e("$VALIDATION_PREFIX ❌ ViewModel激活但messageId未追踪: $messageId")
            return
        }
        
        // 验证 sessionId 一致性
        if (state.sessionId != null && sessionId != null && state.sessionId != sessionId) {
            state.addError("SessionId不一致: 期望=${state.sessionId}, 实际=$sessionId")
        }
        
        state.viewModelActivated = true
        Timber.tag(TAG).i("$VALIDATION_PREFIX ViewModel激活: $messageId, sessionId: $sessionId")
        logFlowState(state)
    }

    /**
     * 7. 首个 Token 接收
     */
    fun validateFirstTokenReceived(messageId: String, tokenContent: String) {
        val state = activeFlows[messageId]
        if (state == null) {
            Timber.tag(TAG).e("$VALIDATION_PREFIX ❌ 首个Token接收但messageId未追踪: $messageId")
            return
        }
        
        if (!state.viewModelActivated) {
            state.addError("首个Token接收但ViewModel未激活")
        }
        
        state.firstTokenReceived = true
        Timber.tag(TAG).i("$VALIDATION_PREFIX 首个Token接收: $messageId, 内容: '${tokenContent.take(50)}...'")
        logFlowState(state)
    }

    /**
     * 8. 解析开始
     */
    fun validateParsingStarted(messageId: String, parserType: String) {
        val state = activeFlows[messageId]
        if (state == null) {
            Timber.tag(TAG).e("$VALIDATION_PREFIX ❌ 解析开始但messageId未追踪: $messageId")
            return
        }
        
        state.parsingStarted = true
        Timber.tag(TAG).i("$VALIDATION_PREFIX 解析开始: $messageId, 解析器: $parserType")
        logFlowState(state)
    }

    /**
     * 9. Segment 创建
     */
    fun validateSegmentCreated(messageId: String, segmentId: String, segmentType: String) {
        val state = activeFlows[messageId]
        if (state == null) {
            Timber.tag(TAG).e("$VALIDATION_PREFIX ❌ Segment创建但messageId未追踪: $messageId")
            return
        }
        
        state.segmentCreated = true
        Timber.tag(TAG).i("$VALIDATION_PREFIX Segment创建: $messageId, segmentId: $segmentId, 类型: $segmentType")
        logFlowState(state)
        
        // 检查是否完成
        if (state.isComplete()) {
            Timber.tag(TAG).i("$VALIDATION_PREFIX ✅ 数据流验证完成: $messageId, 总耗时: ${state.getElapsedTime()}ms")
        }
    }

    /**
     * 记录错误
     */
    fun validateError(messageId: String, error: String, component: String) {
        val state = activeFlows[messageId]
        if (state == null) {
            Timber.tag(TAG).e("$VALIDATION_PREFIX ❌ 错误记录但messageId未追踪: $messageId")
            return
        }
        
        state.addError("$component: $error")
        Timber.tag(TAG).e("$VALIDATION_PREFIX 错误: $messageId, 组件: $component, 错误: $error")
        logFlowState(state)
    }

    /**
     * 清理完成的数据流
     */
    fun cleanupFlow(messageId: String) {
        val state = activeFlows.remove(messageId)
        if (state != null) {
            Timber.tag(TAG).i("$VALIDATION_PREFIX 清理数据流: $messageId, 总耗时: ${state.getElapsedTime()}ms")
            
            if (state.errors.isNotEmpty()) {
                Timber.tag(TAG).w("$VALIDATION_PREFIX 数据流存在错误: $messageId")
                state.errors.forEach { error ->
                    Timber.tag(TAG).w("  - $error")
                }
            }
        }
    }

    /**
     * 获取所有活跃的数据流状态
     */
    fun getActiveFlows(): Map<String, DataFlowState> = activeFlows.toMap()

    /**
     * 记录数据流状态
     */
    private fun logFlowState(state: DataFlowState) {
        val progress = buildString {
            append("进度: ")
            append(if (state.coachGenerated) "✅Coach" else "❌Coach")
            append(" → ")
            append(if (state.aiStreamRepositoryCalled) "✅AiRepo" else "❌AiRepo")
            append(" → ")
            append(if (state.coreNetworkReceived) "✅CoreNet" else "❌CoreNet")
            append(" → ")
            append(if (state.directOutputChannelSent) "✅DirectOut" else "❌DirectOut")
            append(" → ")
            append(if (state.streamAdapterSubscribed) "✅Adapter" else "❌Adapter")
            append(" → ")
            append(if (state.viewModelActivated) "✅ViewModel" else "❌ViewModel")
            append(" → ")
            append(if (state.firstTokenReceived) "✅Token" else "❌Token")
            append(" → ")
            append(if (state.parsingStarted) "✅Parse" else "❌Parse")
        }
        
        Timber.tag(TAG).d("$VALIDATION_PREFIX ${state.messageId}: $progress (${state.getElapsedTime()}ms)")
    }
}
