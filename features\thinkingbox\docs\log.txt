2025-08-04 11:47:31.406  3989-4178  CNET-PROCESSOR-Stream   com.example.gymbro                   I  📊 [批量统计] 已处理tokens=1, 解析错误=0, 错误率=0%
2025-08-04 11:47:31.421  3989-3989  TB-COACH                com.example.gymbro                   I  🔍 AwaitingFirstToken状态: 实际AI消息ID=35fe5e23-35b8-4173-adea-f160bcf122ae
2025-08-04 11:47:31.422  3989-4178  CNET-PROCESSOR-Stream   com.example.gymbro                   D  ✅ [处理结果] 长度变化=49->5
2025-08-04 11:47:31.427  3989-4178  TB-E2E-TRACE            com.example.gymbro                   E  🔍 [输出通道接收] messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, contentType=JSON_SSE, token='Hello...'
2025-08-04 11:47:31.433  3989-4178  CNET-OUTPUT-Direct      com.example.gymbro                   D  🔍 [数据流] DirectOutputChannel发送: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, token长度=5
2025-08-04 11:47:31.433  3989-4178  CNET-OUTPUT-Direct      com.example.gymbro                   I  📊 [输出统计] 已输出tokens=1, 活跃订阅者=0, 总订阅者=0
2025-08-04 11:47:31.452  3989-3989  TB-VM                   com.example.gymbro                   D  🚀 [ViewModel初始化] ThinkingBoxViewModel启动
2025-08-04 11:47:31.454  3989-3994  .example.gymbro         com.example.gymbro                   I  Compiler allocated 7381KB to compile void androidx.compose.material3.internal.TextFieldImplKt.CommonDecorationBox(androidx.compose.material3.internal.TextFieldType, java.lang.String, kotlin.jvm.functions.Function2, androidx.compose.ui.text.input.VisualTransformation, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, boolean, boolean, boolean, androidx.compose.foundation.interaction.InteractionSource, androidx.compose.foundation.layout.PaddingValues, androidx.compose.material3.TextFieldColors, kotlin.jvm.functions.Function2, androidx.compose.runtime.Composer, int, int, int)
2025-08-04 11:47:31.489  3989-4179  CNET-PROCESSOR-Stream   com.example.gymbro                   D  ✅ [处理结果] 长度变化=50->6
2025-08-04 11:47:31.490  3989-4179  TB-E2E-TRACE            com.example.gymbro                   E  🔍 [输出通道接收] messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, contentType=JSON_SSE, token=' there...'
2025-08-04 11:47:31.490  3989-4179  CNET-OUTPUT-Direct      com.example.gymbro                   D  🔍 [数据流] DirectOutputChannel发送: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, token长度=6
2025-08-04 11:47:31.499  3989-4168  CNET-TOKEN-OUT          com.example.gymbro                   I  📦 [35fe5e23-35b8-4173-adea-f160bcf122ae] ThinkingBox ← Batch[2 tokens, 22B, 53ms]
2025-08-04 11:47:31.502  3989-4168  CNET-TOKEN-BATCH        com.example.gymbro                   D  📊 批量刷新完成: received=0, output=2
2025-08-04 11:47:31.533  3989-3989  TB-COACH                com.example.gymbro                   I  🚀 ThinkingBox激活: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, state=AwaitingFirstToken
2025-08-04 11:47:31.534  3989-3989  TB-Thinkin...thCallback com.example.gymbro                   I  TB-API: 🔗 设置ThinkingBox完成回调: sessionId=6f7bfbb0-1b47-4a75-81f7-1be5e133f3ff, messageId=35fe5e23-35b8-4173-adea-f160bcf122ae
2025-08-04 11:47:31.536  3989-3989  TB-Thinkin...thCallback com.example.gymbro                   D  TB-API: 🚀 [初始化] messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, sessionId=6f7bfbb0-1b47-4a75-81f7-1be5e133f3ff
2025-08-04 11:47:31.538  3989-3989  TB-REDUCER-Segment      com.example.gymbro                   D  🔄 [Intent处理] Initialize
2025-08-04 11:47:31.538  3989-3989  TB-VM-Main              com.example.gymbro                   D  🔍 [数据流] ViewModel激活: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae
2025-08-04 11:47:31.538  3989-3989  TB-INIT                 com.example.gymbro                   I  🚀 [初始化] ThinkingBox: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae
2025-08-04 11:47:31.546  3989-4168  CNET-PROCESSOR-Stream   com.example.gymbro                   D  ✅ [处理结果] 长度变化=45->1
2025-08-04 11:47:31.546  3989-4168  TB-E2E-TRACE            com.example.gymbro                   E  🔍 [输出通道接收] messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, contentType=JSON_SSE, token='!...'
2025-08-04 11:47:31.549  3989-4168  CNET-OUTPUT-Direct      com.example.gymbro                   D  🔍 [数据流] DirectOutputChannel发送: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, token长度=1
2025-08-04 11:47:31.587  3989-3989  TB-EFFECT               com.example.gymbro                   D  ⚡ [Effect处理] StartTokenStreamListening
2025-08-04 11:47:31.587  3989-3989  TB-STREAM               com.example.gymbro                   I  🎯 [Token流启动] messageId=35fe5e23-35b8-4173-adea-f160bcf122ae
2025-08-04 11:47:31.589  3989-3989  TB-STREAM               com.example.gymbro                   I  ✅ [直接订阅] DirectOutputChannel: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae
2025-08-04 11:47:31.589  3989-3989  CNET-OUTPUT-Direct      com.example.gymbro                   D  🔗 [订阅] messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, 活跃订阅者=1
2025-08-04 11:47:31.595  3989-3989  TB-PARSER-Stream        com.example.gymbro                   I  🚀 [解析启动] 开始解析token流: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae
2025-08-04 11:47:31.600  3989-3989  TB-Thinkin...oxInternal com.example.gymbro                   D  TB-VM:  🚀 初始化ThinkingBox: 35fe5e23-35b8-4173-adea-f160bcf122ae, hasTokenFlow=false
2025-08-04 11:47:31.600  3989-3989  TB-Thinkin...elProvider com.example.gymbro                   D  TB-Provider:TB-Provider: 🔗 [ThinkingBoxViewModelProvider] 注册ViewModel: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae
2025-08-04 11:47:31.600  3989-3989  TB-REDUCER-Segment      com.example.gymbro                   D  🔄 [Intent处理] Initialize
2025-08-04 11:47:31.600  3989-3989  TB-VM-Main              com.example.gymbro                   D  🔍 [数据流] ViewModel激活: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae
2025-08-04 11:47:31.600  3989-3989  TB-INIT                 com.example.gymbro                   I  🚀 [初始化] ThinkingBox: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae
2025-08-04 11:47:31.600  3989-3989  TB-INIT                 com.example.gymbro                   D  ⏭️ [跳过重复] 初始化: 35fe5e23-35b8-4173-adea-f160bcf122ae
2025-08-04 11:47:31.601  3989-3989  TB-Thinkin...oxInternal com.example.gymbro                   D  TB-VM:  🔗 连接HistoryActor到Effect流: 35fe5e23-35b8-4173-adea-f160bcf122ae
2025-08-04 11:47:31.602  3989-3989  TB-HistoryActor         com.example.gymbro                   I  TB-History: 🚀 [初始化] 开始监听ThinkingBox Effect流
2025-08-04 11:47:31.606  3989-4168  CNET-PROCESSOR-Stream   com.example.gymbro                   D  ✅ [处理结果] 长度变化=48->4
2025-08-04 11:47:31.610  3989-4168  TB-E2E-TRACE            com.example.gymbro                   E  🔍 [输出通道接收] messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, contentType=JSON_SSE, token=' How...'
2025-08-04 11:47:31.610  3989-4168  CNET-OUTPUT-Direct      com.example.gymbro                   D  🔍 [数据流] DirectOutputChannel发送: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, token长度=4
2025-08-04 11:47:31.614  3989-4168  CNET-TOKEN-OUT          com.example.gymbro                   I  📦 [35fe5e23-35b8-4173-adea-f160bcf122ae] ThinkingBox ← Batch[2 tokens, 10B, 59ms]
2025-08-04 11:47:31.617  3989-4168  CNET-TOKEN-BATCH        com.example.gymbro                   D  📊 批量刷新完成: received=0, output=2
2025-08-04 11:47:31.618  3989-3989  Coach-ThinkingBox       com.example.gymbro                   D  🔍 [显示决策] isStreaming=true
2025-08-04 11:47:31.622  3989-3989  TB-COACH                com.example.gymbro                   I  🔍 AwaitingFirstToken状态: 实际AI消息ID=35fe5e23-35b8-4173-adea-f160bcf122ae
2025-08-04 11:47:31.651  3989-3989  TB-E2E-TRACE            com.example.gymbro                   E  📨 [输出通道分发] messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, token=' How...'
2025-08-04 11:47:31.651  3989-3989  TB-STREAM               com.example.gymbro                   D  📥 [Token接收] content= How, type=JSON_SSE
2025-08-04 11:47:31.653  3989-3989  TB-PARSER-Stream        com.example.gymbro                   D  🔍 [数据流] 首个Token接收: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, 内容=' How...'
2025-08-04 11:47:31.653  3989-3989  TB-PARSER-Stream        com.example.gymbro                   I  🚀 [解析首Token] 开始处理token流: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae
2025-08-04 11:47:31.660  3989-3989  TB-MAPPER-TRACE         com.example.gymbro                   E  🔍 [事件映射输入] event=TextChunk, context.parseState=PRE_THINK
2025-08-04 11:47:31.661  3989-3989  TB-MAPPER-Domain        com.example.gymbro                   D  🔍 [数据流] 解析开始: 解析器=DomainMapper
2025-08-04 11:47:31.661  3989-3989  TB-MAPPER-Domain        com.example.gymbro                   I  📝 [finalmermaid规范] PRE_THINK状态，自动创建perthink段
2025-08-04 11:47:31.664  3989-3989  TB-MAPPER-TRACE         com.example.gymbro                   E  ✅ [事件映射输出] 生成2个ThinkingEvent: [SegmentStarted, SegmentText]
2025-08-04 11:47:31.664  3989-3989  TB-REDUCER-Segment      com.example.gymbro                   D  🔄 处理事件: SegmentStarted
2025-08-04 11:47:31.664  3989-3989  TB-SEGMENT-Queue        com.example.gymbro                   D  🔍 [数据流] Segment创建: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, segmentId=perthink, 类型=PERTHINK
2025-08-04 11:47:31.664  3989-3989  TB-SEGMENT-Queue        com.example.gymbro                   D  🎯 创建段: perthink (PERTHINK)
2025-08-04 11:47:31.665  3989-4168  CNET-PROCESSOR-Stream   com.example.gymbro                   D  ✅ [处理结果] 长度变化=48->4
2025-08-04 11:47:31.665  3989-4168  TB-E2E-TRACE            com.example.gymbro                   E  🔍 [输出通道接收] messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, contentType=JSON_SSE, token=' can...'
2025-08-04 11:47:31.665  3989-4168  CNET-OUTPUT-Direct      com.example.gymbro                   D  🔍 [数据流] DirectOutputChannel发送: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, token长度=4
2025-08-04 11:47:31.667  3989-3989  TB-SegmentQueueReducer  com.example.gymbro                   D  TB-Reducer: 📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
2025-08-04 11:47:31.667  3989-3989  TB-REDUCER-Segment      com.example.gymbro                   D  🔄 处理事件: SegmentText
2025-08-04 11:47:31.668  3989-3989  TB-SegmentQueueReducer  com.example.gymbro                   D  TB-Reducer: 📝 追加文本到段[perthink]:  How...
2025-08-04 11:47:31.669  3989-3989  TB-SegmentQueueReducer  com.example.gymbro                   D  TB-Reducer: 📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
2025-08-04 11:47:31.670  3989-3989  TB-E2E-TRACE            com.example.gymbro                   E  📨 [输出通道分发] messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, token=' can...'
2025-08-04 11:47:31.670  3989-3989  TB-STREAM               com.example.gymbro                   D  📥 [Token接收] content= can, type=JSON_SSE
2025-08-04 11:47:31.671  3989-3989  TB-MAPPER-TRACE         com.example.gymbro                   E  🔍 [事件映射输入] event=TextChunk, context.parseState=PRE_THINK
2025-08-04 11:47:31.671  3989-3989  TB-MAPPER-TRACE         com.example.gymbro                   E  ✅ [事件映射输出] 生成1个ThinkingEvent: [SegmentText]
2025-08-04 11:47:31.672  3989-3989  TB-REDUCER-Segment      com.example.gymbro                   D  🔄 处理事件: SegmentText
2025-08-04 11:47:31.672  3989-3989  TB-SegmentQueueReducer  com.example.gymbro                   D  TB-Reducer: 📝 追加文本到段[perthink]:  can...
2025-08-04 11:47:31.673  3989-3989  TB-SegmentQueueReducer  com.example.gymbro                   D  TB-Reducer: 📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
2025-08-04 11:47:31.694  3989-3989  Coach-ThinkingBox       com.example.gymbro                   D  🔍 [显示决策] isStreaming=true
2025-08-04 11:47:31.718  3989-4168  CNET-PROCESSOR-Stream   com.example.gymbro                   D  ✅ [处理结果] 长度变化=46->2
2025-08-04 11:47:31.722  3989-4168  TB-E2E-TRACE            com.example.gymbro                   E  🔍 [输出通道接收] messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, contentType=JSON_SSE, token=' I...'
2025-08-04 11:47:31.722  3989-4168  CNET-OUTPUT-Direct      com.example.gymbro                   D  🔍 [数据流] DirectOutputChannel发送: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, token长度=2
2025-08-04 11:47:31.724  3989-3989  TB-E2E-TRACE            com.example.gymbro                   E  📨 [输出通道分发] messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, token=' I...'
2025-08-04 11:47:31.725  3989-3989  TB-STREAM               com.example.gymbro                   D  📥 [Token接收] content= I, type=JSON_SSE
2025-08-04 11:47:31.725  3989-4178  CNET-TOKEN-OUT          com.example.gymbro                   I  📦 [35fe5e23-35b8-4173-adea-f160bcf122ae] ThinkingBox ← Batch[2 tokens, 12B, 57ms]
2025-08-04 11:47:31.726  3989-3989  TB-MAPPER-TRACE         com.example.gymbro                   E  🔍 [事件映射输入] event=TextChunk, context.parseState=PRE_THINK
2025-08-04 11:47:31.727  3989-3989  TB-MAPPER-TRACE         com.example.gymbro                   E  ✅ [事件映射输出] 生成1个ThinkingEvent: [SegmentText]
2025-08-04 11:47:31.727  3989-4178  CNET-TOKEN-BATCH        com.example.gymbro                   D  📊 批量刷新完成: received=0, output=2
2025-08-04 11:47:31.728  3989-3989  TB-REDUCER-Segment      com.example.gymbro                   D  🔄 处理事件: SegmentText
2025-08-04 11:47:31.728  3989-3989  TB-SegmentQueueReducer  com.example.gymbro                   D  TB-Reducer: 📝 追加文本到段[perthink]:  I...
2025-08-04 11:47:31.728  3989-3989  TB-SegmentQueueReducer  com.example.gymbro                   D  TB-Reducer: 📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
2025-08-04 11:47:31.776  3989-4178  CNET-PROCESSOR-Stream   com.example.gymbro                   D  ✅ [处理结果] 长度变化=49->5
2025-08-04 11:47:31.778  3989-4178  TB-E2E-TRACE            com.example.gymbro                   E  🔍 [输出通道接收] messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, contentType=JSON_SSE, token=' help...'
2025-08-04 11:47:31.778  3989-4178  CNET-OUTPUT-Direct      com.example.gymbro                   D  🔍 [数据流] DirectOutputChannel发送: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, token长度=5
2025-08-04 11:47:31.782  3989-3989  TB-E2E-TRACE            com.example.gymbro                   E  📨 [输出通道分发] messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, token=' help...'
2025-08-04 11:47:31.782  3989-3989  TB-STREAM               com.example.gymbro                   D  📥 [Token接收] content= help, type=JSON_SSE
2025-08-04 11:47:31.782  3989-3989  TB-MAPPER-TRACE         com.example.gymbro                   E  🔍 [事件映射输入] event=TextChunk, context.parseState=PRE_THINK
2025-08-04 11:47:31.782  3989-3989  TB-MAPPER-TRACE         com.example.gymbro                   E  ✅ [事件映射输出] 生成1个ThinkingEvent: [SegmentText]
2025-08-04 11:47:31.782  3989-3989  TB-REDUCER-Segment      com.example.gymbro                   D  🔄 处理事件: SegmentText
2025-08-04 11:47:31.784  3989-3989  TB-SegmentQueueReducer  com.example.gymbro                   D  TB-Reducer: 📝 追加文本到段[perthink]:  help...
2025-08-04 11:47:31.785  3989-3989  TB-SegmentQueueReducer  com.example.gymbro                   D  TB-Reducer: 📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
2025-08-04 11:47:31.832  3989-4178  CNET-PROCESSOR-Stream   com.example.gymbro                   D  ✅ [处理结果] 长度变化=48->4
2025-08-04 11:47:31.833  3989-4178  TB-E2E-TRACE            com.example.gymbro                   E  🔍 [输出通道接收] messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, contentType=JSON_SSE, token=' you...'
2025-08-04 11:47:31.833  3989-4178  CNET-OUTPUT-Direct      com.example.gymbro                   D  🔍 [数据流] DirectOutputChannel发送: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, token长度=4
2025-08-04 11:47:31.833  3989-3989  TB-E2E-TRACE            com.example.gymbro                   E  📨 [输出通道分发] messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, token=' you...'
2025-08-04 11:47:31.833  3989-3989  TB-STREAM               com.example.gymbro                   D  📥 [Token接收] content= you, type=JSON_SSE
2025-08-04 11:47:31.833  3989-3989  TB-MAPPER-TRACE         com.example.gymbro                   E  🔍 [事件映射输入] event=TextChunk, context.parseState=PRE_THINK
2025-08-04 11:47:31.834  3989-3989  TB-MAPPER-TRACE         com.example.gymbro                   E  ✅ [事件映射输出] 生成1个ThinkingEvent: [SegmentText]
2025-08-04 11:47:31.834  3989-3989  TB-REDUCER-Segment      com.example.gymbro                   D  🔄 处理事件: SegmentText
2025-08-04 11:47:31.834  3989-3989  TB-SegmentQueueReducer  com.example.gymbro                   D  TB-Reducer: 📝 追加文本到段[perthink]:  you...
2025-08-04 11:47:31.834  3989-3989  TB-SegmentQueueReducer  com.example.gymbro                   D  TB-Reducer: 📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
2025-08-04 11:47:31.834  3989-4178  CNET-TOKEN-OUT          com.example.gymbro                   I  📦 [35fe5e23-35b8-4173-adea-f160bcf122ae] ThinkingBox ← Batch[2 tokens, 18B, 54ms]
2025-08-04 11:47:31.836  3989-4178  CNET-TOKEN-BATCH        com.example.gymbro                   D  📊 批量刷新完成: received=0, output=2
2025-08-04 11:47:31.886  3989-4178  CNET-PROCESSOR-Stream   com.example.gymbro                   D  ✅ [处理结果] 长度变化=45->1
2025-08-04 11:47:31.887  3989-4178  TB-E2E-TRACE            com.example.gymbro                   E  🔍 [输出通道接收] messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, contentType=JSON_SSE, token='?...'
2025-08-04 11:47:31.887  3989-4178  CNET-OUTPUT-Direct      com.example.gymbro                   D  🔍 [数据流] DirectOutputChannel发送: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, token长度=1
2025-08-04 11:47:31.889  3989-3989  TB-E2E-TRACE            com.example.gymbro                   E  📨 [输出通道分发] messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, token='?...'
2025-08-04 11:47:31.890  3989-3989  TB-STREAM               com.example.gymbro                   D  📥 [Token接收] content=?, type=JSON_SSE
2025-08-04 11:47:31.890  3989-3989  TB-MAPPER-TRACE         com.example.gymbro                   E  🔍 [事件映射输入] event=TextChunk, context.parseState=PRE_THINK
2025-08-04 11:47:31.891  3989-3989  TB-MAPPER-TRACE         com.example.gymbro                   E  ✅ [事件映射输出] 生成1个ThinkingEvent: [SegmentText]
2025-08-04 11:47:31.891  3989-3989  TB-REDUCER-Segment      com.example.gymbro                   D  🔄 处理事件: SegmentText
2025-08-04 11:47:31.892  3989-3989  TB-SegmentQueueReducer  com.example.gymbro                   D  TB-Reducer: 📝 追加文本到段[perthink]: ?...
2025-08-04 11:47:31.892  3989-3989  TB-SegmentQueueReducer  com.example.gymbro                   D  TB-Reducer: 📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
2025-08-04 11:47:31.940  3989-4178  CNET-PROCESSOR-Stream   com.example.gymbro                   D  ✅ [处理结果] 长度变化=12->0
2025-08-04 11:47:31.995  3989-4168  CNET-TOKEN-OUT          com.example.gymbro                   I  📦 [35fe5e23-35b8-4173-adea-f160bcf122ae] ThinkingBox ← Batch[1 tokens, 2B, 0ms]
2025-08-04 11:47:31.996  3989-4178  CNET-SERVICE-UnifiedAi  com.example.gymbro                   I  ✅ [请求完成] messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, tokens=9, 耗时=609ms
2025-08-04 11:47:31.997  3989-4168  CNET-TOKEN-BATCH        com.example.gymbro                   D  📊 批量刷新完成: received=0, output=1
2025-08-04 11:47:32.391  3989-3989  TB-Thinkin...rStateFlow com.example.gymbro                   D  TB-Display: 🔄 [DisplaySession] 状态更新: messageId=35fe5e23-35b8-4173-adea-f160bcf122ae, finalReady=false, thinkingClosed=false
