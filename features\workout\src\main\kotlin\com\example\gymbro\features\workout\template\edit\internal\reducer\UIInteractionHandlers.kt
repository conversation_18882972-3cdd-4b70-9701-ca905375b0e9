package com.example.gymbro.features.workout.template.edit.internal.reducer

import androidx.compose.ui.geometry.Offset
import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.features.workout.template.TemplateContract
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import com.example.gymbro.features.workout.template.edit.utils.DragDropHandler
import timber.log.Timber
import javax.inject.Inject

/**
 * UI 交互处理器
 *
 * 🎯 职责：
 * - 处理 UI 交互相关的 Intent
 * - 管理对话框状态
 * - 处理导航和错误状态
 * - 处理拖拽和快速操作
 *
 * 📋 遵循标准：
 * - 单一职责原则
 * - 纯函数式编程
 * - 不可变状态管理
 */
class UIInteractionHandlers @Inject constructor() {

    // === 导航处理 ===

    fun handleNavigateBack(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.withEffect(
            state,
            TemplateEditContract.Effect.PrepareToExit,
        )
    }

    fun handleShowExerciseSelector(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> =
        ReduceResult.withEffect(
            state,
            TemplateEditContract.Effect.NavigateToExerciseLibrary,
        )

    fun handleResetNavigationState(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(
                isSaving = false,
                isCreatingVersion = false,
            ),
        )
    }

    // === 错误处理 ===

    fun handleError(
        intent: TemplateEditContract.Intent.HandleError,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(
                error = intent.error,
                isLoading = false,
                isSaving = false,
                isCreatingVersion = false,
                isRestoringVersion = false,
                autoSaveState = TemplateContract.AutoSaveState.Failed,
            ),
        )
    }

    fun handleClearError(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(error = null),
        )
    }

    // === 对话框管理 ===

    fun handleShowTemplateNameDialog(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        // 🔥 修复：使用状态中的 templateName 而不是 template?.name
        val currentName = state.templateName.takeIf { it.isNotBlank() } ?: "训练模版"
        Timber.d("🔧 [DEBUG-DIALOG] handleShowTemplateNameDialog 被调用")
        Timber.d("🔧 [DEBUG-DIALOG] 当前模板名称: '$currentName'")

        return ReduceResult.stateOnly(
            state.copy(
                showTemplateNameDialog = true,
                tempTemplateName = currentName,
            ),
        )
    }

    fun handleShowTemplateDescriptionDialog(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(
                showTemplateDescriptionDialog = true,
                tempTemplateDescription = state.template?.description ?: "",
            ),
        )
    }

    fun handleDismissDialog(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(
                showTemplateNameDialog = false,
                showTemplateDescriptionDialog = false,
                tempTemplateName = null,
                tempTemplateDescription = null,
            ),
        )
    }

    fun handleUpdateTempTemplateName(
        intent: TemplateEditContract.Intent.UpdateTempTemplateName,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(tempTemplateName = intent.name),
        )
    }

    fun handleUpdateTempTemplateDescription(
        intent: TemplateEditContract.Intent.UpdateTempTemplateDescription,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(tempTemplateDescription = intent.description),
        )
    }

    fun handleConfirmTemplateName(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        val newName = state.tempTemplateName ?: state.template?.name ?: ""
        val newDescription = state.template?.description ?: ""

        println("🔧 [DEBUG] handleConfirmTemplateName: newName='$newName', newDescription='$newDescription'")

        return ReduceResult.withEffect(
            state.copy(
                templateName = newName,
                hasUnsavedChanges = true,
                showTemplateNameDialog = false,
                tempTemplateName = null,
                autoSaveState = TemplateContract.AutoSaveState.Inactive,
            ),
            TemplateEditContract.Effect.SaveTemplateBasicInfo(newName, newDescription),
        )
    }

    fun handleConfirmTemplateDescription(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        val newName = state.template?.name ?: ""
        val newDescription = state.tempTemplateDescription ?: state.template?.description ?: ""

        println(
            "🔧 [DEBUG] handleConfirmTemplateDescription: newName='$newName', newDescription='$newDescription'",
        )

        return ReduceResult.withEffect(
            state.copy(
                templateDescription = newDescription,
                hasUnsavedChanges = true,
                showTemplateDescriptionDialog = false,
                tempTemplateDescription = null,
                autoSaveState = TemplateContract.AutoSaveState.Inactive,
            ),
            TemplateEditContract.Effect.SaveTemplateBasicInfo(newName, newDescription),
        )
    }

    // === 拖拽排序处理 ===

    fun handleStartDrag(
        intent: TemplateEditContract.Intent.StartDrag,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        if (!DragDropHandler.canStartDrag(state, intent.exerciseId)) {
            return ReduceResult.noChange(state)
        }

        return ReduceResult.stateOnly(
            DragDropHandler.handleDragStart(state, intent.exerciseId, intent.startIndex),
        )
    }

    fun handleUpdateDragPosition(
        intent: TemplateEditContract.Intent.UpdateDragPosition,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> =
        ReduceResult.stateOnly(
            DragDropHandler.handleDragMove(state, intent.targetIndex, Offset(0f, intent.offset)),
        )

    fun handleCompleteDrag(
        intent: TemplateEditContract.Intent.CompleteDrag,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> =
        ReduceResult.stateOnly(
            DragDropHandler.handleDragComplete(state, intent.fromIndex, intent.toIndex),
        )

    fun handleCancelDrag(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> =
        ReduceResult.stateOnly(
            DragDropHandler.handleDragCancel(state),
        )

    // === 增强拖拽处理 (Material3集成) ===

    fun handleStartDragWithPosition(
        intent: TemplateEditContract.Intent.StartDragWithPosition,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        if (!DragDropHandler.canStartDrag(state, intent.exerciseId)) {
            return ReduceResult.noChange(state)
        }

        // 使用增强的handleDragStart方法，支持位置参数
        val newState = DragDropHandler.handleDragStart(
            state = state,
            exerciseId = intent.exerciseId,
            startIndex = intent.startIndex,
            startPosition = intent.startPosition
        )

        return ReduceResult.withEffect(
            newState,
            TemplateEditContract.Effect.AnimateDragStart(intent.exerciseId, state.dragConfig)
        )
    }

    fun handleUpdateDragPositionWithCoordinates(
        intent: TemplateEditContract.Intent.UpdateDragPositionWithCoordinates,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        if (!state.exerciseDragState.isDragInProgress) {
            return ReduceResult.noChange(state)
        }

        // 使用增强的handleDragMove方法，支持2D位置
        val newState = DragDropHandler.handleDragMove(
            state = state,
            targetIndex = intent.targetIndex,
            currentPosition = intent.currentPosition
        )

        return ReduceResult.stateOnly(newState)
    }

    fun handleTriggerDragHaptic(
        intent: TemplateEditContract.Intent.TriggerDragHaptic,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.withEffect(
            state.copy(shouldTriggerHaptic = true),
            TemplateEditContract.Effect.TriggerDragHapticFeedback(intent.hapticType, "user_triggered")
        )
    }

    fun handleUpdateDragPreview(
        intent: TemplateEditContract.Intent.UpdateDragPreview,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(
                showDropPreview = intent.showPreview,
                dropPreviewIndex = intent.previewIndex
            )
        )
    }

    fun handleSetDragConfig(
        intent: TemplateEditContract.Intent.SetDragConfig,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(dragConfig = intent.config)
        )
    }

    // === 快速操作 ===

    fun handleShowQuickActions(
        intent: TemplateEditContract.Intent.ShowQuickActions,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> =
        ReduceResult.stateOnly(
            state.copy(
                showQuickActions = true,
                quickActionTargetId = intent.exerciseId,
            ),
        )

    fun handleHideQuickActions(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> =
        ReduceResult.stateOnly(
            state.copy(
                showQuickActions = false,
                quickActionTargetId = null,
            ),
        )
}
