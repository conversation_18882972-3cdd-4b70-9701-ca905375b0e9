package com.example.gymbro.core.network

import com.example.gymbro.core.network.buffer.AdaptiveBufferManager
import com.example.gymbro.core.network.buffer.PerformanceMonitor
import com.example.gymbro.core.network.buffer.ProcessingMetrics
import com.example.gymbro.core.network.buffer.SlidingWindowBuffer
import com.example.gymbro.core.network.detector.ContentType
import com.example.gymbro.core.network.detector.DetectionResult
import com.example.gymbro.core.network.detector.FeatureMatcher
import com.example.gymbro.core.network.detector.ProgressiveProtocolDetector
import com.example.gymbro.core.network.processor.ContentExtractorImpl
import com.example.gymbro.core.network.processor.OutputSanitizerImpl
import com.example.gymbro.core.network.processor.StreamingProcessor
import com.example.gymbro.core.network.processor.StreamingProcessorImpl
import com.example.gymbro.core.network.receiver.HttpSseTokenSource
import com.example.gymbro.core.network.receiver.UnifiedTokenReceiver
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.json.Json

/**
 * 🧹 DEPRECATED: 新架构测试运行器
 *
 * 此测试运行器已废弃，因为它验证的组件在架构重构中已被删除：
 * - AdaptiveBufferManager → 已删除（过度工程）
 * - SlidingWindowBuffer → 已删除（与缓冲管理一起移除）
 * - FeatureMatcher → 已删除（与协议检测一起移除）
 * - ProgressiveProtocolDetector → 已删除（不必要的协议检测）
 * - UnifiedTokenReceiver → 已删除（与UnifiedAiResponseService重复）
 *
 * 新架构的验证应该测试：
 * - UnifiedAiResponseService
 * - StreamingProcessor
 * - DirectOutputChannel
 * - NetworkMonitor
 */
object NewArchitectureTestRunner {

    @JvmStatic
    fun main(args: Array<String>) {
        println("🧹 此测试运行器已废弃")
        println("新架构组件验证请使用相应的单元测试")
        println("- UnifiedAiResponseServiceTest")
        println("- StreamingProcessorTest")
        println("- DirectOutputChannelTest")
    }
}
        } else {
            println("❌ AdaptiveBufferManager 测试失败")
        }

        // 2. 测试SlidingWindowBuffer
        totalTests++
        if (testSlidingWindowBuffer()) {
            passedTests++
            println("✅ SlidingWindowBuffer 测试通过")
        } else {
            println("❌ SlidingWindowBuffer 测试失败")
        }

        // 3. 测试FeatureMatcher
        totalTests++
        if (testFeatureMatcher()) {
            passedTests++
            println("✅ FeatureMatcher 测试通过")
        } else {
            println("❌ FeatureMatcher 测试失败")
        }

        // 4. 测试ProgressiveProtocolDetector
        totalTests++
        if (testProgressiveProtocolDetector()) {
            passedTests++
            println("✅ ProgressiveProtocolDetector 测试通过")
        } else {
            println("❌ ProgressiveProtocolDetector 测试失败")
        }

        // 5. 测试StreamingProcessor
        totalTests++
        if (testStreamingProcessor()) {
            passedTests++
            println("✅ StreamingProcessor 测试通过")
        } else {
            println("❌ StreamingProcessor 测试失败")
        }

        // 6. 测试UnifiedTokenReceiver
        totalTests++
        if (testUnifiedTokenReceiver()) {
            passedTests++
            println("✅ UnifiedTokenReceiver 测试通过")
        } else {
            println("❌ UnifiedTokenReceiver 测试失败")
        }

        // 7. 性能验证测试
        totalTests++
        if (testPerformanceValidation()) {
            passedTests++
            println("✅ 性能验证测试通过")
        } else {
            println("❌ 性能验证测试失败")
        }

        println("=" * 50)
        println("🎯 测试结果: $passedTests/$totalTests 通过")

        if (passedTests == totalTests) {
            println("🎉 所有新架构核心组件测试通过！")
            println("🚀 新架构验证成功:")
            println("   ✅ 智能缓冲管理器")
            println("   ✅ 滑动窗口缓冲器")
            println("   ✅ KMP算法特征匹配器")
            println("   ✅ 渐进式协议检测器")
            println("   ✅ 流式处理器")
            println("   ✅ 统一Token接收器")
            println("   ✅ 性能验证")
        } else {
            println("⚠️ 部分测试失败，需要进一步调试")
        }
    }

    private fun testAdaptiveBufferManager(): Boolean {
        return try {
            val performanceMonitor = mockk<PerformanceMonitor>(relaxed = true)
            val manager = AdaptiveBufferManager(performanceMonitor)

            // 测试初始状态
            assert(manager.getCurrentBufferSize() == 32) { "初始缓冲区大小应该是32" }

            // 测试缓冲区调整
            val highMemoryMetrics = ProcessingMetrics(
                tokensPerSecond = 100f,
                networkThroughput = 120f,
                memoryUsagePercent = 0.9f,
                bufferUtilization = 0.7f,
                avgLatencyMs = 20L,
                errorRate = 0.0f,
            )

            val initialSize = manager.getCurrentBufferSize()
            manager.adjustBufferSize(highMemoryMetrics)
            val newSize = manager.getCurrentBufferSize()

            assert(newSize >= initialSize) { "高内存压力时应该增加缓冲区大小" }

            // 测试强制设置
            manager.forceBufferSize(64)
            assert(manager.getCurrentBufferSize() == 64) { "强制设置缓冲区大小应该生效" }

            true
        } catch (e: Exception) {
            println("AdaptiveBufferManager 测试异常: ${e.message}")
            false
        }
    }

    private fun testSlidingWindowBuffer(): Boolean {
        return try {
            val buffer = SlidingWindowBuffer<String>(
                windowSize = 5,
                slideStep = 2,
                maxRetentionMs = 1000L,
            )

            // 测试初始状态
            assert(buffer.isEmpty()) { "初始状态应该为空" }
            assert(buffer.size() == 0) { "初始大小应该为0" }

            // 测试添加项目
            val items = listOf("item1", "item2", "item3", "item4", "item5")
            var slidedItems = emptyList<String>()

            items.forEach { item ->
                val result = buffer.add(item)
                if (result.isNotEmpty()) {
                    slidedItems = result
                }
            }

            assert(slidedItems.isNotEmpty()) { "窗口满时应该触发滑动" }
            assert(slidedItems.size == 2) { "滑动步长应该是2" }

            // 测试flush
            val remaining = buffer.flush()
            assert(remaining.isNotEmpty()) { "flush应该返回剩余项目" }
            assert(buffer.isEmpty()) { "flush后缓冲区应该为空" }

            true
        } catch (e: Exception) {
            println("SlidingWindowBuffer 测试异常: ${e.message}")
            false
        }
    }

    private fun testFeatureMatcher(): Boolean {
        return try {
            val matcher = FeatureMatcher()

            // 测试JSON SSE模式匹配
            val jsonSseText = "data: {\"choices\":[{\"delta\":{\"content\":\"Hello\"}}]}".toCharArray()
            val position = matcher.findPattern(jsonSseText, jsonSseText.size, FeatureMatcher.JSON_SSE_PATTERN)
            assert(position == 0) { "应该在位置0找到JSON SSE模式" }

            // 测试XML thinking模式匹配
            val xmlText = "Some text <thinking>content</thinking> more text".toCharArray()
            val xmlPosition = matcher.findPattern(xmlText, xmlText.size, FeatureMatcher.XML_THINKING_PATTERN)
            assert(xmlPosition == 10) { "应该在正确位置找到XML thinking模式" }

            // 测试协议特征检测
            val features = matcher.detectProtocolFeatures(jsonSseText, jsonSseText.size)
            assert(features.hasJsonSse) { "应该检测到JSON SSE特征" }
            assert(features.getMostLikelyProtocol() == ContentType.JSON_SSE) { "应该识别为JSON SSE协议" }

            true
        } catch (e: Exception) {
            println("FeatureMatcher 测试异常: ${e.message}")
            false
        }
    }

    private fun testProgressiveProtocolDetector(): Boolean {
        return try {
            val featureMatcher = FeatureMatcher()
            val detector = ProgressiveProtocolDetector(featureMatcher)

            // 测试初始状态
            val initialStatus = detector.getDetectionStatus()
            assert(initialStatus.bufferLength == 0) { "初始缓冲区长度应该为0" }
            assert(!initialStatus.detectionComplete) { "初始状态检测应该未完成" }

            // 测试JSON SSE检测
            val jsonSseToken = "data: {\"choices\":[{\"delta\":{\"content\":\"Hello World\"}}]}"
            val result = detector.detectWithConfidence(jsonSseToken)

            // 由于token长度足够，应该能检测到
            assert(result is DetectionResult.Probable || result is DetectionResult.Confirmed) {
                "应该检测到协议类型"
            }

            // 测试重置
            detector.reset()
            val resetStatus = detector.getDetectionStatus()
            assert(resetStatus.bufferLength == 0) { "重置后缓冲区长度应该为0" }

            true
        } catch (e: Exception) {
            println("ProgressiveProtocolDetector 测试异常: ${e.message}")
            false
        }
    }

    private fun testStreamingProcessor(): Boolean {
        return try {
            val json = Json { ignoreUnknownKeys = true }
            val contentExtractor = ContentExtractorImpl(json)
            val piiSanitizer = mockk<com.example.gymbro.core.network.security.PiiSanitizer>(relaxed = true)
            val outputSanitizer = OutputSanitizerImpl(piiSanitizer)
            val processor = StreamingProcessorImpl(contentExtractor, outputSanitizer)

            every { piiSanitizer.sanitizeContent(any()) } returns "sanitized content"

            // 测试JSON SSE处理
            val jsonSseToken = "data: {\"choices\":[{\"delta\":{\"content\":\"Hello\"}}]}"
            val result = processor.processImmediate(jsonSseToken, ContentType.JSON_SSE, "test-conversation")
            assert(result == "sanitized content") { "应该返回净化后的内容" }

            // 测试XML ThinkingBox处理
            val xmlToken = "<thinking>This is a thought</thinking>"
            val xmlResult = processor.processImmediate(
                xmlToken,
                ContentType.XML_THINKING,
                "test-conversation",
            )
            assert(xmlResult == "sanitized content") { "XML内容应该被直接处理" }

            true
        } catch (e: Exception) {
            println("StreamingProcessor 测试异常: ${e.message}")
            false
        }
    }

    private fun testUnifiedTokenReceiver(): Boolean {
        return try {
            val protocolDetector = mockk<ProgressiveProtocolDetector>(relaxed = true)
            val streamingProcessor = mockk<StreamingProcessor>(relaxed = true)
            val adaptiveBufferManager = mockk<AdaptiveBufferManager>(relaxed = true)
            val performanceMonitor = mockk<PerformanceMonitor>(relaxed = true)

            val receiver = UnifiedTokenReceiver(
                protocolDetector,
                streamingProcessor,
                adaptiveBufferManager,
                performanceMonitor,
            )

            // Mock设置
            every { protocolDetector.detectWithConfidence(any()) } returns
                DetectionResult.Confirmed(ContentType.JSON_SSE, 0.9f)
            every { streamingProcessor.processImmediate(any(), any(), any()) } returns "processed"
            coEvery { performanceMonitor.getCurrentMetrics() } returns ProcessingMetrics(
                tokensPerSecond = 100f, networkThroughput = 120f, memoryUsagePercent = 0.5f,
                bufferUtilization = 0.6f, avgLatencyMs = 20L, errorRate = 0.0f,
            )

            // 测试token流处理
            val tokens = listOf("token1", "token2", "token3")
            val tokenSource = HttpSseTokenSource(
                flow {
                    tokens.forEach { emit(it) }
                },
            )

            val results = runBlocking {
                receiver.receiveTokenStream(tokenSource, "test-conversation").toList()
            }

            assert(results.size == 3) { "应该处理所有token" }
            assert(results.all { it == "processed" }) { "所有token应该被正确处理" }

            true
        } catch (e: Exception) {
            println("UnifiedTokenReceiver 测试异常: ${e.message}")
            false
        }
    }

    private fun testPerformanceValidation(): Boolean {
        return try {
            // 简单的性能验证测试
            val startTime = System.currentTimeMillis()

            // 模拟快速处理
            repeat(1000) {
                // 简单的字符串操作，模拟token处理
                val token = "data: {\"content\": \"token$it\"}"
                val processed = token.replace("data: ", "").trim()
            }

            val endTime = System.currentTimeMillis()
            val processingTime = endTime - startTime

            // 验证处理时间在合理范围内（应该远小于旧架构的300ms）
            assert(processingTime < 100) { "处理1000个token应该在100ms内完成" }

            println("📊 性能测试结果: 1000个token处理时间 = ${processingTime}ms")

            true
        } catch (e: Exception) {
            println("性能验证测试异常: ${e.message}")
            false
        }
    }
}

private operator fun String.times(n: Int): String = this.repeat(n)
