package com.example.gymbro.core.monitoring

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.datetime.Clock
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 【PLAN B 重构】架构性能监控器
 *
 * 核心职责：
 * 1. 监控数据流各阶段的性能指标
 * 2. 收集延迟、吞吐量、错误率等关键指标
 * 3. 提供实时性能仪表板数据
 * 4. 支持性能告警和异常检测
 * 5. 为架构优化提供数据支撑
 *
 * 设计原则：
 * - 低开销：监控本身不能影响系统性能
 * - 实时性：提供实时的性能指标
 * - 可扩展：支持新增监控指标
 * - 线程安全：支持并发环境下的指标收集
 */
@Singleton
class ArchitecturePerformanceMonitor @Inject constructor() {

    companion object {
        private const val TAG = "ArchPerformanceMonitor"
        private const val METRICS_WINDOW_SIZE = 100 // 滑动窗口大小
        private const val ALERT_THRESHOLD_MS = 1000L // 告警阈值：1秒
        private const val CLEANUP_INTERVAL_MS = 5 * 60 * 1000L // 5分钟清理一次
    }

    // 性能指标数据
    private val _performanceMetrics = MutableStateFlow(PerformanceMetrics())
    val performanceMetrics: StateFlow<PerformanceMetrics> = _performanceMetrics.asStateFlow()

    // 活跃的性能追踪会话
    private val activeTrackingSessions = ConcurrentHashMap<String, TrackingSession>()
    
    // 历史指标数据（滑动窗口）
    private val latencyHistory = ConcurrentHashMap<String, MutableList<Long>>()
    
    // 计数器
    private val totalRequests = AtomicLong(0)
    private val totalErrors = AtomicLong(0)
    private val totalCompletedSessions = AtomicLong(0)

    /**
     * 性能指标数据类
     */
    data class PerformanceMetrics(
        val averageLatencyMs: Long = 0L,
        val p95LatencyMs: Long = 0L,
        val p99LatencyMs: Long = 0L,
        val throughputPerSecond: Double = 0.0,
        val errorRate: Double = 0.0,
        val activeSessionsCount: Int = 0,
        val totalRequestsCount: Long = 0L,
        val lastUpdated: Long = Clock.System.now().toEpochMilliseconds(),
        val alerts: List<PerformanceAlert> = emptyList()
    )

    /**
     * 性能告警数据类
     */
    data class PerformanceAlert(
        val type: AlertType,
        val message: String,
        val severity: AlertSeverity,
        val timestamp: Long,
        val metadata: Map<String, Any> = emptyMap()
    )

    enum class AlertType {
        HIGH_LATENCY,
        HIGH_ERROR_RATE,
        LOW_THROUGHPUT,
        MEMORY_PRESSURE,
        TIMEOUT_EXCEEDED
    }

    enum class AlertSeverity {
        INFO, WARNING, ERROR, CRITICAL
    }

    /**
     * 追踪会话数据类
     */
    private data class TrackingSession(
        val sessionId: String,
        val startTime: Long,
        val stage: String,
        val metadata: Map<String, Any> = emptyMap()
    )

    /**
     * 开始性能追踪会话
     *
     * @param sessionId 会话ID（通常是messageId）
     * @param stage 当前阶段（如 "coach-request", "core-network-processing", "thinkingbox-rendering"）
     * @param metadata 附加元数据
     */
    fun startTracking(
        sessionId: String,
        stage: String,
        metadata: Map<String, Any> = emptyMap()
    ) {
        val session = TrackingSession(
            sessionId = sessionId,
            startTime = Clock.System.now().toEpochMilliseconds(),
            stage = stage,
            metadata = metadata
        )
        
        activeTrackingSessions[sessionId] = session
        totalRequests.incrementAndGet()
        
        Timber.tag(TAG).d("🎯 开始追踪: sessionId=$sessionId, stage=$stage")
        
        updateMetrics()
    }

    /**
     * 结束性能追踪会话
     *
     * @param sessionId 会话ID
     * @param success 是否成功完成
     * @param errorMessage 错误信息（如果失败）
     */
    fun endTracking(
        sessionId: String,
        success: Boolean = true,
        errorMessage: String? = null
    ) {
        val session = activeTrackingSessions.remove(sessionId)
        if (session == null) {
            Timber.tag(TAG).w("⚠️ 未找到追踪会话: sessionId=$sessionId")
            return
        }

        val endTime = Clock.System.now().toEpochMilliseconds()
        val latency = endTime - session.startTime

        // 记录延迟数据
        recordLatency(session.stage, latency)

        if (success) {
            totalCompletedSessions.incrementAndGet()
            Timber.tag(TAG).d("✅ 追踪完成: sessionId=$sessionId, latency=${latency}ms")
        } else {
            totalErrors.incrementAndGet()
            Timber.tag(TAG).w("❌ 追踪失败: sessionId=$sessionId, latency=${latency}ms, error=$errorMessage")
        }

        // 检查性能告警
        checkPerformanceAlerts(session.stage, latency, success)
        
        updateMetrics()
    }

    /**
     * 记录阶段性能指标
     *
     * @param sessionId 会话ID
     * @param stage 阶段名称
     * @param latencyMs 延迟（毫秒）
     * @param metadata 附加元数据
     */
    fun recordStageMetrics(
        sessionId: String,
        stage: String,
        latencyMs: Long,
        metadata: Map<String, Any> = emptyMap()
    ) {
        recordLatency(stage, latencyMs)
        
        Timber.tag(TAG).d("📊 阶段指标: sessionId=$sessionId, stage=$stage, latency=${latencyMs}ms")
        
        // 检查阶段性能告警
        checkPerformanceAlerts(stage, latencyMs, true)
        
        updateMetrics()
    }

    /**
     * 获取特定阶段的性能统计
     *
     * @param stage 阶段名称
     * @return 阶段性能统计
     */
    fun getStageStatistics(stage: String): StageStatistics? {
        val latencies = latencyHistory[stage] ?: return null
        
        if (latencies.isEmpty()) return null
        
        val sortedLatencies = latencies.sorted()
        val count = sortedLatencies.size
        
        return StageStatistics(
            stage = stage,
            count = count,
            averageMs = sortedLatencies.average().toLong(),
            medianMs = sortedLatencies[count / 2],
            p95Ms = sortedLatencies[(count * 0.95).toInt().coerceAtMost(count - 1)],
            p99Ms = sortedLatencies[(count * 0.99).toInt().coerceAtMost(count - 1)],
            minMs = sortedLatencies.first(),
            maxMs = sortedLatencies.last()
        )
    }

    /**
     * 阶段性能统计数据类
     */
    data class StageStatistics(
        val stage: String,
        val count: Int,
        val averageMs: Long,
        val medianMs: Long,
        val p95Ms: Long,
        val p99Ms: Long,
        val minMs: Long,
        val maxMs: Long
    )

    /**
     * 获取所有阶段的性能概览
     */
    fun getAllStageStatistics(): Map<String, StageStatistics> {
        return latencyHistory.keys.mapNotNull { stage ->
            getStageStatistics(stage)?.let { stage to it }
        }.toMap()
    }

    /**
     * 清理过期数据
     */
    fun cleanup() {
        val cutoffTime = Clock.System.now().toEpochMilliseconds() - CLEANUP_INTERVAL_MS
        
        // 清理过期的活跃会话
        val expiredSessions = activeTrackingSessions.values.filter { 
            it.startTime < cutoffTime 
        }
        
        expiredSessions.forEach { session ->
            activeTrackingSessions.remove(session.sessionId)
            Timber.tag(TAG).w("🧹 清理过期会话: ${session.sessionId}")
        }
        
        // 限制历史数据大小
        latencyHistory.values.forEach { latencies ->
            if (latencies.size > METRICS_WINDOW_SIZE) {
                val excess = latencies.size - METRICS_WINDOW_SIZE
                repeat(excess) { latencies.removeAt(0) }
            }
        }
        
        Timber.tag(TAG).d("🧹 清理完成: 清理${expiredSessions.size}个过期会话")
    }

    /**
     * 记录延迟数据
     */
    private fun recordLatency(stage: String, latencyMs: Long) {
        latencyHistory.getOrPut(stage) { mutableListOf() }.add(latencyMs)
        
        // 限制窗口大小
        val latencies = latencyHistory[stage]!!
        if (latencies.size > METRICS_WINDOW_SIZE) {
            latencies.removeAt(0)
        }
    }

    /**
     * 检查性能告警
     */
    private fun checkPerformanceAlerts(stage: String, latencyMs: Long, success: Boolean) {
        val alerts = mutableListOf<PerformanceAlert>()
        
        // 高延迟告警
        if (latencyMs > ALERT_THRESHOLD_MS) {
            alerts.add(
                PerformanceAlert(
                    type = AlertType.HIGH_LATENCY,
                    message = "阶段 $stage 延迟过高: ${latencyMs}ms",
                    severity = if (latencyMs > ALERT_THRESHOLD_MS * 2) AlertSeverity.ERROR else AlertSeverity.WARNING,
                    timestamp = Clock.System.now().toEpochMilliseconds(),
                    metadata = mapOf("stage" to stage, "latency" to latencyMs)
                )
            )
        }
        
        // 错误率告警
        if (!success) {
            alerts.add(
                PerformanceAlert(
                    type = AlertType.HIGH_ERROR_RATE,
                    message = "阶段 $stage 处理失败",
                    severity = AlertSeverity.ERROR,
                    timestamp = Clock.System.now().toEpochMilliseconds(),
                    metadata = mapOf("stage" to stage)
                )
            )
        }
        
        // 记录告警
        if (alerts.isNotEmpty()) {
            alerts.forEach { alert ->
                Timber.tag(TAG).w("🚨 性能告警: ${alert.message}")
            }
        }
    }

    /**
     * 更新性能指标
     */
    private fun updateMetrics() {
        val allLatencies = latencyHistory.values.flatten()
        val currentTime = Clock.System.now().toEpochMilliseconds()
        
        val metrics = if (allLatencies.isNotEmpty()) {
            val sortedLatencies = allLatencies.sorted()
            val count = sortedLatencies.size
            
            PerformanceMetrics(
                averageLatencyMs = sortedLatencies.average().toLong(),
                p95LatencyMs = sortedLatencies[(count * 0.95).toInt().coerceAtMost(count - 1)],
                p99LatencyMs = sortedLatencies[(count * 0.99).toInt().coerceAtMost(count - 1)],
                throughputPerSecond = calculateThroughput(),
                errorRate = calculateErrorRate(),
                activeSessionsCount = activeTrackingSessions.size,
                totalRequestsCount = totalRequests.get(),
                lastUpdated = currentTime
            )
        } else {
            PerformanceMetrics(
                activeSessionsCount = activeTrackingSessions.size,
                totalRequestsCount = totalRequests.get(),
                lastUpdated = currentTime
            )
        }
        
        _performanceMetrics.value = metrics
    }

    /**
     * 计算吞吐量（每秒请求数）
     */
    private fun calculateThroughput(): Double {
        val completedCount = totalCompletedSessions.get()
        return if (completedCount > 0) {
            // 简化计算：基于最近的完成数量
            completedCount.toDouble() / 60.0 // 假设1分钟窗口
        } else {
            0.0
        }
    }

    /**
     * 计算错误率
     */
    private fun calculateErrorRate(): Double {
        val totalCount = totalRequests.get()
        val errorCount = totalErrors.get()
        
        return if (totalCount > 0) {
            (errorCount.toDouble() / totalCount.toDouble()) * 100.0
        } else {
            0.0
        }
    }
}
