package com.example.gymbro.domain.coach.usecase

import com.example.gymbro.core.conversation.ConversationIdManager
import com.example.gymbro.domain.coach.model.AiTaskType
import com.example.gymbro.domain.coach.repository.AiStreamRepository
import com.example.gymbro.shared.models.ai.ChatRequest
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 【阶段3重构】任务型流式聊天UseCase - 适配新接口
 *
 * 重构内容：
 * 1. 使用 streamChatWithMessageId 替代已删除的 streamChatWithTaskType
 * 2. 自动生成临时 messageId 用于内部处理
 * 3. 提取文本内容，保持原有接口兼容性
 */
@Singleton
class StreamChatWithTaskUseCase @Inject constructor(
    private val aiStreamRepository: AiStreamRepository,
    private val conversationIdManager: ConversationIdManager,
) {

    /**
     * 🔥 【阶段3重构】执行基于任务类型的流式聊天
     *
     * 使用 streamChatWithMessageId 实现，生成临时 messageId
     *
     * @param request 聊天请求
     * @param taskType 任务类型，自动选择最优提供商
     * @return 流式响应文本
     */
    suspend operator fun invoke(
        request: ChatRequest,
        taskType: AiTaskType = AiTaskType.CHAT,
    ): Flow<String> {
        // 生成临时 messageId 用于内部处理
        val tempMessageId = "task_${taskType.name.lowercase()}_${System.currentTimeMillis()}"

        // 使用 streamChatWithMessageId 并提取文本内容
        return aiStreamRepository.streamChatWithMessageId(request, tempMessageId, taskType)
            .map { outputToken -> outputToken.content }
    }

    /**
     * 获取任务类型支持信息
     */
    suspend fun getTaskCapabilities(taskType: AiTaskType) =
        aiStreamRepository.getTaskCapabilities(taskType)

    /**
     * 获取所有支持的任务类型
     */
    fun getSupportedTaskTypes(): List<AiTaskType> = AiTaskType.values().toList()
}
