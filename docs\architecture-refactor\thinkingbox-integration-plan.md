# 🎯 ThinkingBox 集成优化计划

## 📊 优化目标

基于 PLAN B 架构，简化 ThinkingBox 与 Core-Network 的集成，实现直接订阅 DirectOutputChannel 的高效数据流。

## 🔄 当前架构问题

### 1. **复杂的订阅机制**
```kotlin
// 当前：多层订阅链路
Coach → AiStreamRepository → AiResponseReceiver → UnifiedAiResponseService → DirectOutputChannel → ThinkingBox
```

### 2. **ID 传递复杂**
```kotlin
// 当前：多个ID概念混淆
data class State(
    val messageId: String = "",
    val sessionId: String = "",      // 冗余
    val conversationId: String = "", // 与messageId重复
    // ...
)
```

### 3. **状态同步问题**
- ThinkingBox 订阅时机不确定
- 早期 token 可能丢失
- 错误处理不统一

## 🎯 优化方案

### 1. **简化 Contract 定义**

```kotlin
// 优化后的 ThinkingBox Contract
object ThinkingBoxContract {
    
    @Immutable
    data class State(
        val messageId: String = "",
        // 移除 sessionId 字段，通过 ConversationIdManager 获取
        val segmentsQueue: List<SegmentUi> = emptyList(),
        val finalReady: Boolean = false,
        val finalContent: String = "",
        val thinkingClosed: Boolean = false,
        val error: UiText? = null,
        val isLoading: Boolean = false,
        
        // 🔥 新增：订阅状态管理
        val subscriptionState: SubscriptionState = SubscriptionState.Idle,
        val tokenCount: Int = 0,
        val lastTokenTimestamp: Long = 0L
    ) : UiState {
        
        enum class SubscriptionState {
            Idle,           // 未订阅
            Subscribing,    // 订阅中
            Active,         // 活跃订阅
            Completed,      // 处理完成
            Error           // 订阅错误
        }
        
        val isActivelyReceiving: Boolean
            get() = subscriptionState == SubscriptionState.Active && !finalReady
    }
    
    sealed interface Intent : AppIntent {
        data class Initialize(val messageId: String) : Intent  // 移除 sessionId 参数
        data object Reset : Intent
        data class UiSegmentRendered(val segmentId: String) : Intent
        data object ClearError : Intent
        
        // 🔥 新增：订阅管理
        data object StartSubscription : Intent
        data object StopSubscription : Intent
        data class SubscriptionStateChanged(val state: SubscriptionState) : Intent
    }
    
    sealed interface Effect : UiEffect {
        data class SubscribeToMessage(val messageId: String) : Effect
        data class UnsubscribeFromMessage(val messageId: String) : Effect
        data class NotifyProcessingCompleted(
            val messageId: String,
            val finalContent: String
        ) : Effect
        data class ShowError(val error: UiText) : Effect
    }
}
```

### 2. **直接订阅机制**

```kotlin
// 优化后的 ThinkingBox ViewModel
@HiltViewModel
class ThinkingBoxViewModel @Inject constructor(
    private val directOutputChannel: DirectOutputChannel,  // 直接注入
    private val conversationIdManager: ConversationIdManager,
    private val streamingThinkingMLParser: StreamingThinkingMLParser,
    private val reducer: ThinkingBoxReducer,
    private val effectHandler: ThinkingBoxEffectHandler
) : BaseMviViewModel<Intent, State, Effect>(State()) {
    
    private var subscriptionJob: Job? = null
    
    override fun processIntent(intent: Intent) {
        when (intent) {
            is Intent.Initialize -> {
                updateState { it.copy(messageId = intent.messageId) }
                processEffect(Effect.SubscribeToMessage(intent.messageId))
            }
            
            is Intent.StartSubscription -> {
                startDirectSubscription()
            }
            
            is Intent.StopSubscription -> {
                stopDirectSubscription()
            }
            
            // 其他 intent 处理...
        }
    }
    
    /**
     * 🔥 【核心优化】直接订阅 DirectOutputChannel
     */
    private fun startDirectSubscription() {
        val messageId = currentState.messageId
        if (messageId.isEmpty()) return
        
        subscriptionJob?.cancel()
        subscriptionJob = viewModelScope.launch {
            updateState { it.copy(subscriptionState = State.SubscriptionState.Subscribing) }
            
            try {
                // 直接订阅 DirectOutputChannel
                directOutputChannel.subscribeToMessage(messageId)
                    .flowOn(Dispatchers.IO)
                    .collect { outputToken ->
                        handleTokenReceived(outputToken)
                    }
            } catch (e: Exception) {
                Timber.e(e, "ThinkingBox 订阅失败: messageId=$messageId")
                updateState { 
                    it.copy(
                        subscriptionState = State.SubscriptionState.Error,
                        error = UiText.DynamicString("订阅失败: ${e.message}")
                    )
                }
            }
        }
    }
    
    /**
     * 处理接收到的 token
     */
    private suspend fun handleTokenReceived(token: OutputToken) {
        // 更新订阅状态
        if (currentState.subscriptionState != State.SubscriptionState.Active) {
            updateState { 
                it.copy(subscriptionState = State.SubscriptionState.Active)
            }
        }
        
        // 解析和处理 token
        val parseResult = streamingThinkingMLParser.parseToken(
            token.content,
            currentState.segmentsQueue
        )
        
        when (parseResult) {
            is ParseResult.NewSegment -> {
                updateState { state ->
                    state.copy(
                        segmentsQueue = state.segmentsQueue + parseResult.segment,
                        tokenCount = state.tokenCount + 1,
                        lastTokenTimestamp = System.currentTimeMillis()
                    )
                }
            }
            
            is ParseResult.UpdateSegment -> {
                updateState { state ->
                    val updatedQueue = state.segmentsQueue.map { segment ->
                        if (segment.id == parseResult.segmentId) {
                            parseResult.updatedSegment
                        } else {
                            segment
                        }
                    }
                    state.copy(
                        segmentsQueue = updatedQueue,
                        tokenCount = state.tokenCount + 1,
                        lastTokenTimestamp = System.currentTimeMillis()
                    )
                }
            }
            
            is ParseResult.Completed -> {
                updateState { state ->
                    state.copy(
                        finalReady = true,
                        finalContent = parseResult.finalContent,
                        subscriptionState = State.SubscriptionState.Completed
                    )
                }
                
                // 通知 Coach 处理完成
                processEffect(
                    Effect.NotifyProcessingCompleted(
                        messageId = currentState.messageId,
                        finalContent = parseResult.finalContent
                    )
                )
            }
            
            is ParseResult.Error -> {
                updateState { state ->
                    state.copy(
                        error = UiText.DynamicString(parseResult.message),
                        subscriptionState = State.SubscriptionState.Error
                    )
                }
            }
        }
    }
    
    private fun stopDirectSubscription() {
        subscriptionJob?.cancel()
        subscriptionJob = null
        updateState { 
            it.copy(subscriptionState = State.SubscriptionState.Idle)
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        stopDirectSubscription()
    }
}
```

### 3. **优化的 EffectHandler**

```kotlin
class ThinkingBoxEffectHandler @Inject constructor(
    private val directOutputChannel: DirectOutputChannel,
    private val conversationIdManager: ConversationIdManager
) {
    
    suspend fun handle(effect: Effect): Flow<Intent> = flow {
        when (effect) {
            is Effect.SubscribeToMessage -> {
                // 验证 messageId 有效性
                val messageContext = conversationIdManager.getMessageContext(effect.messageId)
                if (messageContext != null) {
                    emit(Intent.StartSubscription)
                } else {
                    emit(Intent.SubscriptionStateChanged(State.SubscriptionState.Error))
                    Timber.w("无效的 messageId: ${effect.messageId}")
                }
            }
            
            is Effect.UnsubscribeFromMessage -> {
                emit(Intent.StopSubscription)
            }
            
            is Effect.NotifyProcessingCompleted -> {
                // 通知 Coach 模块处理完成
                // 这里可以通过事件总线或回调通知
                Timber.i("ThinkingBox 处理完成: ${effect.messageId}")
            }
            
            is Effect.ShowError -> {
                // 处理错误显示
                Timber.e("ThinkingBox 错误: ${effect.error}")
            }
        }
    }
}
```

## 🔧 集成优化步骤

### 步骤 1: 更新依赖注入（1小时）
```kotlin
// ThinkingBox 模块的 DI 配置
@Module
@InstallIn(ViewModelComponent::class)
object ThinkingBoxModule {
    
    @Provides
    fun provideDirectOutputChannel(
        @ApplicationContext context: Context
    ): DirectOutputChannel {
        // 直接提供 DirectOutputChannel 实例
        return DirectOutputChannel.getInstance()
    }
}
```

### 步骤 2: 简化启动流程（2小时）
```kotlin
// Coach 模块中的简化启动
class CoachEffectHandler {
    suspend fun handle(effect: Effect): Flow<Intent> = flow {
        when (effect) {
            is Effect.StartAiStream -> {
                // 1. 启动 AI 流处理
                unifiedAiResponseService.processAiStreamingResponse(
                    request = buildRequest(effect.messageContext, effect.prompt),
                    messageId = effect.messageContext.messageId
                )
                
                // 2. 直接启动 ThinkingBox（简化参数）
                emit(Intent.LaunchThinkingBox(effect.messageContext.messageId))
            }
        }
    }
}
```

### 步骤 3: 错误处理统一（1小时）
```kotlin
// 统一的错误处理机制
sealed class ThinkingBoxError : Exception() {
    data class SubscriptionFailed(val messageId: String, override val cause: Throwable) : ThinkingBoxError()
    data class ParseError(val content: String, override val message: String) : ThinkingBoxError()
    data class InvalidMessageId(val messageId: String) : ThinkingBoxError()
    
    fun toUiText(): UiText = when (this) {
        is SubscriptionFailed -> UiText.StringResource(R.string.error_subscription_failed)
        is ParseError -> UiText.DynamicString("解析错误: $message")
        is InvalidMessageId -> UiText.StringResource(R.string.error_invalid_message_id)
    }
}
```

## 📊 性能优化

### 1. **订阅时机优化**
```kotlin
// 预订阅机制：在 AI 请求启动前就开始订阅
class CoachEffectHandler {
    suspend fun handle(effect: Effect.StartAiStream): Flow<Intent> = flow {
        // 1. 先启动 ThinkingBox 订阅
        emit(Intent.LaunchThinkingBox(effect.messageContext.messageId))
        
        // 2. 等待订阅建立（避免早期 token 丢失）
        delay(100) // 短暂延迟确保订阅建立
        
        // 3. 启动 AI 流处理
        unifiedAiResponseService.processAiStreamingResponse(...)
    }
}
```

### 2. **内存优化**
```kotlin
// 限制 segmentsQueue 大小，避免内存泄漏
data class State(
    val segmentsQueue: List<SegmentUi> = emptyList(),
    // ...
) {
    companion object {
        const val MAX_SEGMENTS = 1000
    }
    
    fun addSegment(segment: SegmentUi): State {
        val newQueue = if (segmentsQueue.size >= MAX_SEGMENTS) {
            segmentsQueue.drop(100) + segment  // 保留最新的 900 个
        } else {
            segmentsQueue + segment
        }
        return copy(segmentsQueue = newQueue)
    }
}
```

## 🧪 测试策略

### 集成测试：
```kotlin
@Test
fun `ThinkingBox should receive all tokens from DirectOutputChannel`() = runTest {
    // Given
    val messageId = "test_message_id"
    val tokens = listOf("Hello", " ", "World", "!")
    
    // When
    viewModel.processIntent(Intent.Initialize(messageId))
    
    tokens.forEach { token ->
        directOutputChannel.sendToken(
            OutputToken(
                content = token,
                messageId = messageId,
                contentType = ContentType.JSON_SSE
            )
        )
    }
    
    // Then
    val finalState = viewModel.state.value
    assertThat(finalState.tokenCount).isEqualTo(tokens.size)
    assertThat(finalState.finalContent).contains("Hello World!")
}
```

## 📈 预期收益

1. **性能提升**: 直接订阅减少 2-3 层调用，延迟降低 40%
2. **可靠性**: 预订阅机制消除 token 丢失问题
3. **简化**: Contract 字段减少 30%，复杂度降低
4. **维护性**: 统一错误处理，调试更容易
