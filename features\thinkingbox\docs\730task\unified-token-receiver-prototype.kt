package com.example.gymbro.core.network.receiver

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚀 统一Token接收器 - 新架构核心组件
 *
 * 设计目标：
 * - 统一所有协议的Token接收入口
 * - 前100-200 token内完成快速协议识别
 * - 零缓冲即时流式处理
 * - 直接输出到ThinkingBox，延迟<5ms
 */
@Singleton
class UnifiedTokenReceiver @Inject constructor(
    private val protocolDetector: ProtocolDetector,
    private val streamingProcessor: StreamingProcessor
) {

    /**
     * 统一Token流接收处理
     *
     * @param source Token来源（HTTP SSE/WebSocket/HTTP Basic等）
     * @param conversationId 会话ID，用于路由和状态管理
     * @return 处理后的Token流，直接输出给ThinkingBox
     */
    suspend fun receiveTokenStream(
        source: TokenSource,
        conversationId: String
    ): Flow<String> = flow {

        // 快速协议识别缓冲区（仅前200 token）
        val identificationBuffer = StringBuilder()
        var protocolDetected = false
        var contentType: ContentType? = null
        var tokenCount = 0

        Timber.d("🔄 开始接收Token流: conversationId=$conversationId")

        source.tokenFlow.collect { token ->
            tokenCount++

            // 阶段1：快速协议识别（仅前100-200 token）
            if (!protocolDetected && identificationBuffer.length < 200) {
                identificationBuffer.append(token)

                // 达到100 token时尝试识别，200 token时强制识别
                if (identificationBuffer.length >= 100 || tokenCount >= 200) {
                    contentType = protocolDetector.detectContentType(
                        content = identificationBuffer.toString(),
                        tokenCount = tokenCount
                    )
                    protocolDetected = true

                    Timber.i("🔍 协议识别完成: type=$contentType, tokens=$tokenCount, " +
                            "buffer_size=${identificationBuffer.length}")

                    // 处理识别缓冲区中的所有token
                    val bufferedContent = streamingProcessor.processImmediate(
                        token = identificationBuffer.toString(),
                        contentType = contentType!!,
                        messageId = conversationId // 🔥 【Plan B重构】使用messageId参数
                    )

                    if (bufferedContent.isNotEmpty()) {
                        emit(bufferedContent)
                    }
                }
            }

            // 阶段2：即时流式处理（协议识别完成后）
            else if (protocolDetected) {
                val processed = streamingProcessor.processImmediate(
                    token = token,
                    contentType = contentType!!,
                    conversationId = conversationId
                )

                // 立即输出，零延迟
                if (processed.isNotEmpty()) {
                    emit(processed)
                }
            }
        }

        Timber.d("✅ Token流接收完成: conversationId=$conversationId, total_tokens=$tokenCount")
    }
}

/**
 * 🔍 快速协议检测器
 *
 * 在前100-200 token内识别内容类型和协议格式
 * 支持多种AI模型的输出格式
 */
class ProtocolDetector @Inject constructor() {

    fun detectContentType(content: String, tokenCount: Int): ContentType {
        val trimmedContent = content.trim()

        return when {
            // JSON SSE格式检测（OpenAI/Claude风格）
            isJsonSseFormat(trimmedContent) -> {
                Timber.d("🔍 检测到JSON SSE格式")
                ContentType.JSON_SSE
            }

            // 纯JSON流检测（一些自定义API）
            isPureJsonStream(trimmedContent) -> {
                Timber.d("🔍 检测到纯JSON流格式")
                ContentType.JSON_STREAM
            }

            // ThinkingBox XML格式检测
            isThinkingBoxXml(trimmedContent) -> {
                Timber.d("🔍 检测到ThinkingBox XML格式")
                ContentType.XML_THINKING
            }

            // WebSocket二进制帧检测
            isWebSocketFrame(trimmedContent) -> {
                Timber.d("🔍 检测到WebSocket帧格式")
                ContentType.WEBSOCKET_FRAME
            }

            // 默认纯文本流
            else -> {
                Timber.d("🔍 默认识别为纯文本流")
                ContentType.PLAIN_TEXT
            }
        }
    }

    private fun isJsonSseFormat(content: String): Boolean {
        return content.contains("data: {") &&
               (content.contains("\"content\"") || content.contains("\"delta\""))
    }

    private fun isPureJsonStream(content: String): Boolean {
        return content.startsWith("{") &&
               (content.contains("\"text\"") || content.contains("\"message\""))
    }

    private fun isThinkingBoxXml(content: String): Boolean {
        return content.contains("<thinking>") ||
               content.contains("<segment") ||
               content.contains("</thinking>")
    }

    private fun isWebSocketFrame(content: String): Boolean {
        return content.startsWith("WS:") || content.contains("frame_type")
    }
}

/**
 * 📝 内容类型枚举
 *
 * 支持的所有Token流格式类型
 */
enum class ContentType {
    JSON_SSE,           // Server-Sent Events with JSON (OpenAI/Claude)
    JSON_STREAM,        // Pure JSON streaming (Custom APIs)
    XML_THINKING,       // ThinkingBox XML format
    WEBSOCKET_FRAME,    // WebSocket binary frames
    PLAIN_TEXT          // Plain text streaming
}

/**
 * 📡 Token来源接口
 *
 * 统一不同协议的Token输入接口
 */
interface TokenSource {
    val tokenFlow: Flow<String>
    val sourceType: String  // "HTTP_SSE", "WEBSOCKET", "HTTP_BASIC"
}

/**
 * 🔄 流式处理器接口
 *
 * 单一处理器，根据内容类型进行即时处理
 */
interface StreamingProcessor {
    fun processImmediate(
        token: String,
        contentType: ContentType,
        conversationId: String
    ): String
}

/**
 * 🚀 HTTP SSE Token来源实现
 */
class HttpSseTokenSource(
    override val tokenFlow: Flow<String>
) : TokenSource {
    override val sourceType: String = "HTTP_SSE"
}

/**
 * 🚀 WebSocket Token来源实现
 */
class WebSocketTokenSource(
    override val tokenFlow: Flow<String>
) : TokenSource {
    override val sourceType: String = "WEBSOCKET"
}

/**
 * 🚀 HTTP Basic Token来源实现
 */
class HttpBasicTokenSource(
    override val tokenFlow: Flow<String>
) : TokenSource {
    override val sourceType: String = "HTTP_BASIC"
}

/**
 * 📊 性能指标记录
 *
 * 记录新架构的性能表现
 */
data class ProcessingMetrics(
    val detectionTimeMs: Long,      // 协议识别耗时
    val processingTimeMs: Long,     // 处理耗时
    val totalLatencyMs: Long,       // 总延迟
    val tokenCount: Int,            // 处理的token数量
    val contentType: ContentType    // 识别的内容类型
)

/**
 * 🎯 使用示例
 *
 * 展示新架构的使用方式
 */
/*
class ExampleUsage @Inject constructor(
    private val unifiedReceiver: UnifiedTokenReceiver
) {
    suspend fun processAiResponse(httpSseFlow: Flow<String>, conversationId: String) {
        val tokenSource = HttpSseTokenSource(httpSseFlow)

        unifiedReceiver.receiveTokenStream(tokenSource, conversationId)
            .collect { processedToken ->
                // 直接发送给ThinkingBox，零延迟
                thinkingBoxViewModel.processToken(processedToken)
            }
    }
}
*/
