# 🔥 【PLAN B 重构】监控体系集成指南

## 📊 监控体系概述

基础监控体系已成功搭建，包含以下核心组件：

1. **ArchitecturePerformanceMonitor** - 核心性能监控器
2. **PerformanceExtensions** - 便捷的监控扩展函数
3. **PerformanceStages** - 标准化的阶段定义
4. **PerformanceBenchmark** - 性能基准测试工具

## 🎯 集成方式

### 1. 在 Coach 模块中集成监控

```kotlin
// AiCoachEffectHandler.kt
class AiCoachEffectHandler @Inject constructor(
    private val performanceMonitor: ArchitecturePerformanceMonitor,
    // ... 其他依赖
) {
    
    private suspend fun handleSendMessage(effect: AiCoachContract.Effect.SendMessage) {
        // 开始性能追踪
        performanceMonitor.trackCoachRequest(
            sessionId = getCurrentSessionId() ?: "",
            messageId = effect.messageId,
            userInput = effect.userInput
        )
        
        try {
            // 执行业务逻辑...
            val messageContext = conversationIdManager.createMessageContext(sessionId)
            
            // 记录阶段性能
            performanceMonitor.recordStageMetrics(
                sessionId = messageContext.messageId,
                stage = PerformanceStages.COACH_EFFECT_HANDLING,
                latencyMs = measureTimeMillis { /* 处理逻辑 */ }
            )
            
            // 结束追踪
            performanceMonitor.endTracking(messageContext.messageId, success = true)
            
        } catch (e: Exception) {
            performanceMonitor.endTracking(effect.messageId, success = false, errorMessage = e.message)
            throw e
        }
    }
}
```

### 2. 在 Core-Network 模块中集成监控

```kotlin
// UnifiedAiResponseService.kt
class UnifiedAiResponseService @Inject constructor(
    private val performanceMonitor: ArchitecturePerformanceMonitor,
    // ... 其他依赖
) {
    
    suspend fun processAiStreamingResponse(
        request: ChatRequest,
        messageId: String
    ): Flow<String> = flow<String> {
        
        // 使用扩展函数进行性能监控
        measurePerformance(
            monitor = performanceMonitor,
            sessionId = messageId,
            stage = PerformanceStages.CORE_NETWORK_AI_REQUEST,
            metadata = createPerformanceMetadata(
                messageId = messageId,
                contentLength = Json.encodeToString(request).length
            )
        ) {
            // AI 请求处理逻辑
            val sseResponse = sendAiRequest(request)
            
            parseSseToTokenFlow(sseResponse)
                .withPerformanceTracking(
                    monitor = performanceMonitor,
                    sessionId = messageId,
                    stage = PerformanceStages.CORE_NETWORK_SSE_PARSING
                )
                .collect { rawToken ->
                    // Token 处理逻辑...
                    emit(processedToken)
                }
        }
    }
}
```

### 3. 在 ThinkingBox 模块中集成监控

```kotlin
// ThinkingBoxStreamAdapter.kt
class ThinkingBoxStreamAdapter @Inject constructor(
    private val performanceMonitor: ArchitecturePerformanceMonitor,
    // ... 其他依赖
) {
    
    suspend fun startDirectOutputProcessing(
        messageId: String,
        onTokenReceived: (ThinkingEvent) -> Unit,
        onStreamComplete: () -> Unit,
        onError: (Throwable) -> Unit,
    ): StreamingJob {
        
        // 开始 ThinkingBox 渲染追踪
        performanceMonitor.trackThinkingBoxRendering(messageId, 0)
        
        val processingJob = jobScope.launch {
            try {
                directOutputChannel.subscribeToConversation(messageId)
                    .withPerformanceTracking(
                        monitor = performanceMonitor,
                        sessionId = messageId,
                        stage = PerformanceStages.THINKINGBOX_SUBSCRIPTION
                    )
                    .collect { outputToken ->
                        // 记录 Token 处理性能
                        measurePerformance(
                            monitor = performanceMonitor,
                            sessionId = messageId,
                            stage = PerformanceStages.THINKINGBOX_PARSING,
                            metadata = createPerformanceMetadata(
                                messageId = messageId,
                                tokenCount = 1,
                                contentLength = outputToken.content.length
                            )
                        ) {
                            // Token 解析和处理逻辑...
                            val thinkingEvent = processToken(outputToken)
                            onTokenReceived(thinkingEvent)
                        }
                    }
                    
                performanceMonitor.endTracking(messageId, success = true)
                onStreamComplete()
                
            } catch (e: Exception) {
                performanceMonitor.endTracking(messageId, success = false, errorMessage = e.message)
                onError(e)
            }
        }
        
        return StreamingJob(processingJob, messageId)
    }
}
```

## 📈 性能指标收集

### 关键性能指标 (KPIs)

1. **端到端延迟**: 从用户输入到 ThinkingBox 首次渲染
2. **各阶段延迟**: Coach、Core-Network、ThinkingBox 各阶段处理时间
3. **吞吐量**: 每秒处理的消息数量
4. **错误率**: 失败请求的百分比
5. **内存使用**: 各组件的内存占用情况

### 监控仪表板数据

```kotlin
// 获取实时性能指标
val metrics = performanceMonitor.performanceMetrics.value

// 显示关键指标
println("平均延迟: ${metrics.averageLatencyMs}ms")
println("P95延迟: ${metrics.p95LatencyMs}ms")
println("错误率: ${metrics.errorRate}%")
println("活跃会话: ${metrics.activeSessionsCount}")

// 获取各阶段统计
val stageStats = performanceMonitor.getAllStageStatistics()
stageStats.forEach { (stage, stats) ->
    println("$stage: 平均${stats.averageMs}ms, P95${stats.p95Ms}ms")
}
```

## 🚨 性能告警

### 自动告警触发条件

1. **高延迟告警**: 单次请求延迟 > 1000ms
2. **高错误率告警**: 错误率 > 5%
3. **低吞吐量告警**: 吞吐量 < 预期值的50%
4. **内存压力告警**: 内存使用 > 80%

### 告警处理示例

```kotlin
// 监听性能告警
performanceMonitor.performanceMetrics
    .map { it.alerts }
    .distinctUntilChanged()
    .collect { alerts ->
        alerts.forEach { alert ->
            when (alert.severity) {
                AlertSeverity.CRITICAL -> {
                    // 发送紧急通知
                    notificationService.sendCriticalAlert(alert)
                }
                AlertSeverity.ERROR -> {
                    // 记录错误日志
                    Timber.e("性能告警: ${alert.message}")
                }
                AlertSeverity.WARNING -> {
                    // 记录警告日志
                    Timber.w("性能警告: ${alert.message}")
                }
                AlertSeverity.INFO -> {
                    // 记录信息日志
                    Timber.i("性能信息: ${alert.message}")
                }
            }
        }
    }
```

## 🧪 性能基准测试

### 端到端性能测试

```kotlin
// 运行端到端性能基准测试
val benchmarkResult = PerformanceBenchmark.runBenchmark(
    monitor = performanceMonitor,
    testName = "E2E Message Processing",
    iterations = 100,
    warmupIterations = 10
) { iteration ->
    // 模拟完整的消息处理流程
    val messageId = "test-message-$iteration"
    val userInput = "测试消息 $iteration"
    
    // Coach 处理
    coachEffectHandler.handleSendMessage(
        AiCoachContract.Effect.SendMessage(messageId, userInput)
    )
    
    // 等待 ThinkingBox 完成渲染
    delay(100) // 模拟渲染时间
}

// 输出基准测试结果
println(benchmarkResult.toLogString())
```

## 📊 监控数据导出

### 性能报告生成

```kotlin
// 生成性能报告
fun generatePerformanceReport(): PerformanceReport {
    val metrics = performanceMonitor.performanceMetrics.value
    val stageStats = performanceMonitor.getAllStageStatistics()
    
    return PerformanceReport(
        timestamp = Clock.System.now().toEpochMilliseconds(),
        overallMetrics = metrics,
        stageStatistics = stageStats,
        recommendations = generateRecommendations(metrics, stageStats)
    )
}

// 性能优化建议
fun generateRecommendations(
    metrics: PerformanceMetrics,
    stageStats: Map<String, StageStatistics>
): List<String> {
    val recommendations = mutableListOf<String>()
    
    if (metrics.averageLatencyMs > 500) {
        recommendations.add("整体延迟偏高，建议优化数据流处理")
    }
    
    stageStats.forEach { (stage, stats) ->
        if (stats.averageMs > 200) {
            recommendations.add("$stage 阶段延迟偏高，建议优化处理逻辑")
        }
    }
    
    if (metrics.errorRate > 2.0) {
        recommendations.add("错误率偏高，建议加强错误处理和重试机制")
    }
    
    return recommendations
}
```

## 🔧 配置和调优

### 监控参数配置

```kotlin
// 在 Application 类中初始化监控配置
class GymBroApplication : Application() {
    
    @Inject
    lateinit var performanceMonitor: ArchitecturePerformanceMonitor
    
    override fun onCreate() {
        super.onCreate()
        
        // 启动定期清理任务
        lifecycleScope.launch {
            while (true) {
                delay(5 * 60 * 1000L) // 5分钟
                performanceMonitor.cleanup()
            }
        }
        
        // 启动性能监控日志
        lifecycleScope.launch {
            performanceMonitor.performanceMetrics.collect { metrics ->
                if (metrics.totalRequestsCount % 100 == 0L) {
                    Timber.i("性能统计: 平均延迟${metrics.averageLatencyMs}ms, 错误率${metrics.errorRate}%")
                }
            }
        }
    }
}
```

---

**监控体系状态**: ✅ 已完成  
**集成状态**: ✅ 就绪  
**下一步**: 进入阶段2 - 冗余组件清理
