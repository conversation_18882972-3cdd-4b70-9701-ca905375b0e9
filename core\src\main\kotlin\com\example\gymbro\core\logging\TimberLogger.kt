package com.example.gymbro.core.logging

import com.example.gymbro.core.util.LogTagOptimizer
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Timber日志器实现
 *
 * 🔄 重构：集成CompactIdGenerator统一ID生成策略
 * 实现Logger接口，将日志调用委托给Timber框架。
 * 提供统一的日志接口，解耦具体的日志框架实现。
 * 自动应用标签优化，提高日志可读性。
 */
@Singleton
class TimberLogger @Inject constructor() : Logger {

    override fun v(message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.v(optimizedMessage, *args)
    }

    override fun v(t: Throwable?, message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.v(t, optimizedMessage, *args)
    }

    override fun v(t: Throwable?) {
        Timber.v(t)
    }

    override fun d(message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.d(optimizedMessage, *args)
    }

    override fun d(t: Throwable?, message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.d(t, optimizedMessage, *args)
    }

    override fun d(t: Throwable?) {
        Timber.d(t)
    }

    override fun i(message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.i(optimizedMessage, *args)
    }

    override fun i(t: Throwable?, message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.i(t, optimizedMessage, *args)
    }

    override fun i(t: Throwable?) {
        Timber.i(t)
    }

    override fun w(message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.w(optimizedMessage, *args)
    }

    override fun w(t: Throwable?, message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.w(t, optimizedMessage, *args)
    }

    override fun w(t: Throwable?) {
        Timber.w(t)
    }

    override fun e(message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.e(optimizedMessage, *args)
    }

    override fun e(t: Throwable?, message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.e(t, optimizedMessage, *args)
    }

    override fun e(t: Throwable?) {
        Timber.e(t)
    }

    override fun wtf(message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.wtf(optimizedMessage, *args)
    }

    override fun wtf(t: Throwable?, message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.wtf(t, optimizedMessage, *args)
    }

    override fun wtf(t: Throwable?) {
        Timber.wtf(t)
    }

    override fun tag(tag: String): Logger {
        val optimizedTag = LogTagOptimizer.optimizeTag(tag)
        return TaggedTimberLogger(optimizedTag)
    }
}

/**
 * 带标签的Timber日志器
 *
 * 🔄 重构：集成标签优化功能
 * 为特定标签创建的日志器实例，所有日志调用都会带上指定的标签。
 * 自动应用消息优化，提高日志可读性。
 */
private class TaggedTimberLogger(private val tag: String) : Logger {

    override fun v(message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.tag(tag).v(optimizedMessage, *args)
    }

    override fun v(t: Throwable?, message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.tag(tag).v(t, optimizedMessage, *args)
    }

    override fun v(t: Throwable?) {
        Timber.tag(tag).v(t)
    }

    override fun d(message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.tag(tag).d(optimizedMessage, *args)
    }

    override fun d(t: Throwable?, message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.tag(tag).d(t, optimizedMessage, *args)
    }

    override fun d(t: Throwable?) {
        Timber.tag(tag).d(t)
    }

    override fun i(message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.tag(tag).i(optimizedMessage, *args)
    }

    override fun i(t: Throwable?, message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.tag(tag).i(t, optimizedMessage, *args)
    }

    override fun i(t: Throwable?) {
        Timber.tag(tag).i(t)
    }

    override fun w(message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.tag(tag).w(optimizedMessage, *args)
    }

    override fun w(t: Throwable?, message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.tag(tag).w(t, optimizedMessage, *args)
    }

    override fun w(t: Throwable?) {
        Timber.tag(tag).w(t)
    }

    override fun e(message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.tag(tag).e(optimizedMessage, *args)
    }

    override fun e(t: Throwable?, message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.tag(tag).e(t, optimizedMessage, *args)
    }

    override fun e(t: Throwable?) {
        Timber.tag(tag).e(t)
    }

    override fun wtf(message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.tag(tag).wtf(optimizedMessage, *args)
    }

    override fun wtf(t: Throwable?, message: String, vararg args: Any?) {
        val optimizedMessage = LogTagOptimizer.optimizeMessage(message)
        Timber.tag(tag).wtf(t, optimizedMessage, *args)
    }

    override fun wtf(t: Throwable?) {
        Timber.tag(tag).wtf(t)
    }

    override fun tag(tag: String): Logger {
        val optimizedTag = LogTagOptimizer.optimizeTag(tag)
        return TaggedTimberLogger(optimizedTag)
    }
}
