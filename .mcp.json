{"mcpServers": {"memory-bank": {"command": "cmd", "args": ["/c", "npx", "@neko0721/memory-bank-mcp"]}, "serena": {"command": "cmd", "args": ["/c", "uvx", "--from", "git+https://github.com/oraios/serena", "serena-mcp-server", "--project", "D:\\GymBro\\GymBro", "--context", "ide-assistant", "--mode", "interactive", "--mode", "editing", "--enable-web-dashboard", "true"]}, "desktop-commander": {"command": "cmd", "args": ["/c", "npx", "-y", "@wonderwhy-er/desktop-commander"]}, "code-reasoning": {"command": "cmd", "args": ["/c", "npx", "-y", "@mettamatt/code-reasoning"]}, "Context 7": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"]}}}