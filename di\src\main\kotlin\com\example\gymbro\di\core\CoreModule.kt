package com.example.gymbro.di.core

import com.example.gymbro.core.error.utils.DefaultErrorMapper
import com.example.gymbro.core.error.utils.ErrorMapper
import com.example.gymbro.core.resources.ResourceProvider
import com.example.gymbro.core.security.EncryptionManager
import com.example.gymbro.core.util.DateTimeUtils
import com.example.gymbro.domain.service.TimeService
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import java.time.Instant
import javax.inject.Qualifier
import javax.inject.Singleton

/**
 * 核心模块
 *
 * 提供整个应用程序中使用的核心组件
 *
 * 注意：RegionProvider绑定现在在 AppRegionModule 中处理
 */
@Module(
    includes = [
        ConversationModule::class, // 🔥 【PLAN B 重构】对话ID管理模块
    ]
)
@InstallIn(SingletonComponent::class)
abstract class CoreModule {

    // ResourceProvider 绑定已移至 core.di.ResourceModule 中统一管理
    // ModernErrorHandler 绑定已移至 ErrorHandlingModule 中统一管理
    // RegionProvider 绑定已移至 AppRegionModule 中统一管理

    /**
     * 绑定错误映射器接口实现
     */
    @Binds
    @Singleton
    abstract fun bindErrorMapper(impl: DefaultErrorMapper): ErrorMapper

    // RegionProvider 绑定已移至 core.di.RegionModule 中统一管理

    companion object {
        /**
         * 提供加密管理器
         */
        @Provides
        @Singleton
        fun provideEncryptionManager(): EncryptionManager {
            return EncryptionManager()
        }

        /**
         * 提供IP信息API的Retrofit限定符
         */
        @Qualifier
        @Retention(AnnotationRetention.BINARY)
        annotation class IpInfoRetrofit

        /**
         * 提供定价API的Retrofit限定符
         */
        @Qualifier
        @Retention(AnnotationRetention.BINARY)
        annotation class PricingRetrofit

        /**
         * 提供日期时间工具类
         */
        @Provides
        @Singleton
        fun provideDateTimeUtils(resourceProvider: ResourceProvider): DateTimeUtils {
            return DateTimeUtils(resourceProvider)
        }

        /**
         * 提供时间服务实现
         * 临时实现，提供基础时间操作功能
         */
        @Provides
        @Singleton
        fun provideTimeService(): TimeService {
            return object : TimeService {
                override fun getCurrentTime(): Instant = Instant.now()

                override fun getDaysBetween(start: Instant, end: Instant): Int {
                    return java.time.Duration.between(start, end).toDays().toInt()
                }

                override fun isExpired(time: Instant?): Boolean {
                    return time?.isBefore(Instant.now()) ?: false
                }

                override fun addDays(time: Instant, days: Int): Instant {
                    return time.plus(java.time.Duration.ofDays(days.toLong()))
                }
            }
        }
    }
}
