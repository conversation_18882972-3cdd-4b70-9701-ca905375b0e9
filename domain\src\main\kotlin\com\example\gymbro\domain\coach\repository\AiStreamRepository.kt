package com.example.gymbro.domain.coach.repository

import com.example.gymbro.core.ai.prompt.builder.CoreChatMessage
import com.example.gymbro.core.conversation.ConversationIdManager
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.coach.model.AiTaskType
import com.example.gymbro.domain.coach.model.StreamEvent
import com.example.gymbro.shared.models.ai.ChatRequest
import kotlinx.coroutines.flow.Flow

// 🔥 【架构重构阶段三】TokenEvent 现在由 core-network 模块的 TokenBus 统一定义
// 使用 com.example.gymbro.core.network.output.OutputToken

/**
 * 🔥 【阶段3重构】AI流式仓库 - 简化接口，统一数据流
 *
 * 重构目标：
 * - 使用 MessageContext 统一ID管理
 * - 移除所有废弃方法，简化接口
 * - 统一数据流路径，所有方法通过 streamChatWithMessageId 实现
 * - 直接调用 Core-Network，消除中间层
 *
 * 核心原则：
 * - 单一数据流：所有AI请求都通过统一路径处理
 * - ID统一管理：使用 MessageContext 替代多个ID参数
 * - 接口简化：只保留必要的核心方法
 */
interface AiStreamRepository {
    /**
     * 🔥 【阶段3重构】主要流式AI请求方法
     *
     * 使用 MessageContext 统一ID管理，简化参数传递
     * 接收已构建的消息列表，避免重复prompt构建
     *
     * @param messageContext 消息上下文，包含统一的ID管理
     * @param messages 已构建的消息列表（来自LayeredPromptBuilder）
     * @param taskType 任务类型，用于选择合适的AI提供商
     * @return 流式事件（首包为Thinking事件）
     */
    suspend fun streamAiResponse(
        messageContext: ConversationIdManager.MessageContext,
        messages: List<CoreChatMessage>,
        taskType: AiTaskType = AiTaskType.CHAT,
    ): Flow<StreamEvent>

    /**
     * 串行插入thinking占位
     *
     * @param sessionId 会话ID
     * @param prompt 用户输入
     * @return thinking消息ID
     */
    suspend fun insertThinking(
        sessionId: String,
        prompt: String,
    ): ModernResult<String>

    /**
     * 获取任务类型支持的功能
     *
     * @param taskType 任务类型
     * @return 支持的功能描述
     */
    suspend fun getTaskCapabilities(taskType: AiTaskType): TaskCapabilities

    /**
     * 🔥 【架构重构】直接连接方法：返回带 messageId 的 Token 事件流
     * 用于 ThinkingBox 直接订阅，绕过复杂的 TokenRouter 机制
     *
     * 🔥 【事件总线架构】改为suspend函数以支持新的streamChatWithTaskType
     *
     * @param request 聊天请求
     * @param messageId 消息ID，用于 ThinkingBox 过滤
     * @param taskType 任务类型
     * @return 带 messageId 的 Token 事件流
     */
    suspend fun streamChatWithMessageId(
        request: ChatRequest,
        messageId: String,
        taskType: AiTaskType = AiTaskType.CHAT,
    ): Flow<com.example.gymbro.core.network.output.OutputToken>
}

/**
 * 任务能力描述
 */
data class TaskCapabilities(
    val taskType: AiTaskType,
    val supportedProviders: List<String>,
    val recommendedModel: String,
    val maxTokens: Int,
    val temperatureRange: ClosedFloatingPointRange<Float>,
)
