package com.example.gymbro.domain.coach.repository

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.coach.model.AiTaskType
import com.example.gymbro.domain.coach.model.StreamEvent
import com.example.gymbro.shared.models.ai.ChatRequest
import kotlinx.coroutines.flow.Flow

// 🔥 【架构重构阶段三】TokenEvent 现在由 core-network 模块的 TokenBus 统一定义
// 使用 com.example.gymbro.core.network.output.OutputToken

/**
 * AI流式仓库 - 专注于流式请求和重试
 *
 * 按照Coach生命线修复PRD v3要求：
 * - 首包Thinking事件
 * - 流式重试机制
 * - 串行thinking插入支持
 * - 任务类型路由支持
 */
interface AiStreamRepository {
    /**
     * 流式AI请求（含thinking首包和任务路由）
     *
     * 🔥 修复：接收已构建的消息列表，避免重复prompt构建
     * 确保所有StreamEvent都携带正确的ID
     *
     * @param sessionId 会话ID
     * @param userMessageId 用户消息ID
     * @param aiResponseId AI响应ID（通常与thinkingId相同）
     * @param messages 已构建的消息列表（来自LayeredPromptBuilder）
     * @param taskType 任务类型，用于选择合适的AI提供商
     * @return 流式事件（首包为Thinking事件）
     */
    suspend fun streamAiResponse(
        sessionId: String,
        messageId: String, // 🔥 【ID统一】使用统一的messageId
        messages: List<com.example.gymbro.core.ai.prompt.builder.CoreChatMessage>,
        taskType: AiTaskType = AiTaskType.CHAT,
    ): Flow<StreamEvent>

    /**
     * 兼容性方法：流式AI请求（使用prompt字符串）
     *
     * 🚨 【已废弃】此方法会导致重复的prompt构建
     * 请使用接收messages参数的streamAiResponse方法
     *
     * @param prompt 用户输入
     * @param sessionId 会话ID
     * @param userMessageId 用户消息ID
     * @param aiResponseId AI响应ID
     * @return 流式事件（首包为Thinking事件）
     * @deprecated 使用streamAiResponse(messages)避免重复prompt构建
     */
    @Deprecated(
        message = "使用streamAiResponse(messages)避免重复prompt构建",
        replaceWith = ReplaceWith(
            "streamAiResponse(sessionId, userMessageId, aiResponseId, messages, taskType)",
        ),
        level = DeprecationLevel.WARNING,
    )
    suspend fun streamAiResponseLegacy(
        sessionId: String,
        messageId: String, // 🔥 【ID统一】使用统一的messageId
        prompt: String,
        taskType: AiTaskType = AiTaskType.CHAT,
    ): Flow<StreamEvent>

    /**
     * 兼容性方法：流式AI请求（默认聊天任务）
     *
     * @param prompt 用户输入
     * @param sessionId 会话ID
     * @param userMessageId 用户消息ID
     * @param aiResponseId AI响应ID
     * @return 流式事件（首包为Thinking事件）
     * @deprecated 使用streamAiResponse(messages)避免重复prompt构建
     */
    @Deprecated("使用streamAiResponse(messages)避免重复prompt构建")
    fun streamAi(
        prompt: String,
        sessionId: String,
        userMessageId: String,
        aiResponseId: String,
    ): Flow<StreamEvent> = kotlinx.coroutines.runBlocking {
        // 🔥 【ID统一修复】使用aiResponseId作为统一的messageId
        // 保持向后兼容，优先使用aiResponseId作为消息标识
        streamAiResponseLegacy(
            sessionId = sessionId,
            messageId = aiResponseId, // 🔥 使用aiResponseId作为统一messageId
            prompt = prompt,
            taskType = AiTaskType.CHAT,
        )
    }

    /**
     * 兼容性方法：保持向后兼容
     *
     * @deprecated 使用带完整上下文的streamAiResponse方法替代
     */
    @Deprecated("使用带完整上下文的streamAiResponse方法替代")
    fun streamAi(
        prompt: String,
        thinkingId: String,
    ): Flow<StreamEvent> =
        streamAi(
            prompt = prompt,
            sessionId = "unknown",
            userMessageId = thinkingId,
            aiResponseId = thinkingId,
        )

    /**
     * 串行插入thinking占位
     *
     * @param sessionId 会话ID
     * @param prompt 用户输入
     * @return thinking消息ID
     */
    suspend fun insertThinking(
        sessionId: String,
        prompt: String,
    ): ModernResult<String>

    /**
     * 基于任务类型的流式聊天
     *
     * 🔥 【事件总线架构】改为suspend函数以支持新的streamChatWithMessageId
     *
     * 根据任务类型自动选择最优的AI提供商和配置
     * @param request 聊天请求
     * @param taskType 任务类型，用于Provider选择
     * @return 流式响应文本
     */
    suspend fun streamChatWithTaskType(
        request: ChatRequest,
        taskType: AiTaskType,
    ): Flow<String>

    /**
     * 获取任务类型支持的功能
     *
     * @param taskType 任务类型
     * @return 支持的功能描述
     */
    suspend fun getTaskCapabilities(taskType: AiTaskType): TaskCapabilities

    /**
     * 🔥 【架构重构】直接连接方法：返回带 messageId 的 Token 事件流
     * 用于 ThinkingBox 直接订阅，绕过复杂的 TokenRouter 机制
     *
     * 🔥 【事件总线架构】改为suspend函数以支持新的streamChatWithTaskType
     *
     * @param request 聊天请求
     * @param messageId 消息ID，用于 ThinkingBox 过滤
     * @param taskType 任务类型
     * @return 带 messageId 的 Token 事件流
     */
    suspend fun streamChatWithMessageId(
        request: ChatRequest,
        messageId: String,
        taskType: AiTaskType = AiTaskType.CHAT,
    ): Flow<com.example.gymbro.core.network.output.OutputToken>
}

/**
 * 任务能力描述
 */
data class TaskCapabilities(
    val taskType: AiTaskType,
    val supportedProviders: List<String>,
    val recommendedModel: String,
    val maxTokens: Int,
    val temperatureRange: ClosedFloatingPointRange<Float>,
)
