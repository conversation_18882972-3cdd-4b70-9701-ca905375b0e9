package com.example.gymbro.core.util

import timber.log.Timber

/**
 * 统一日志工具 - 标签优化和格式统一
 *
 * 🎯 功能特点：
 * - 优化日志标签显示
 * - 统一的日志输出接口
 * - 支持各种日志级别
 * - 性能监控和统计
 * - 🔄 重构：移除UUID压缩功能（现在使用CompactIdGenerator直接生成压缩ID）
 *
 * 🏗️ 使用场景：
 * - 替代直接的Timber调用
 * - 模块间统一日志格式
 * - 调试信息优化
 * - 错误跟踪简化
 *
 * 🔧 示例用法：
 * ```kotlin
 * OptimizedLogger.d("WK-TEMPLATE-CORE", "保存模板: ${templateId}")
 * OptimizedLogger.i("SAVE-SUCCESS", "模板 ${templateId} 保存完成")
 * OptimizedLogger.e("ERROR", "保存失败: ${error}", exception)
 * ```
 */
object OptimizedLogger {

    // 是否启用日志优化
    private var optimizationEnabled = true

    // 是否启用详细统计
    private var detailedStatsEnabled = false

    /**
     * Debug级别日志
     */
    fun d(tag: String, message: String) {
        log(LogLevel.DEBUG, tag, message, null)
    }

    /**
     * Info级别日志
     */
    fun i(tag: String, message: String) {
        log(LogLevel.INFO, tag, message, null)
    }

    /**
     * Warning级别日志
     */
    fun w(tag: String, message: String) {
        log(LogLevel.WARNING, tag, message, null)
    }

    /**
     * Error级别日志
     */
    fun e(tag: String, message: String, throwable: Throwable? = null) {
        log(LogLevel.ERROR, tag, message, throwable)
    }

    /**
     * Verbose级别日志
     */
    fun v(tag: String, message: String) {
        log(LogLevel.VERBOSE, tag, message, null)
    }

    /**
     * 核心日志方法
     */
    private fun log(level: LogLevel, tag: String, message: String, throwable: Throwable?) {
        val optimizedTag = if (optimizationEnabled) {
            LogTagOptimizer.optimizeTag(tag)
        } else {
            tag
        }

        val optimizedMessage = if (optimizationEnabled) {
            LogTagOptimizer.optimizeMessage(message)
        } else {
            message
        }

        // 如果启用详细统计，记录优化效果
        if (detailedStatsEnabled && optimizationEnabled) {
            val stats = LogTagOptimizer.getOptimizationStats(message, optimizedMessage)
            // 只有在实际发生优化时才记录统计
            if (stats.compressionRatio < 1.0) {
                Timber.tag("LOG_OPTIMIZER").v(stats.toLogString())
            }
        }

        // 使用Timber输出优化后的日志
        when (level) {
            LogLevel.DEBUG -> Timber.tag(optimizedTag).d(optimizedMessage)
            LogLevel.INFO -> Timber.tag(optimizedTag).i(optimizedMessage)
            LogLevel.WARNING -> Timber.tag(optimizedTag).w(optimizedMessage)
            LogLevel.ERROR -> {
                if (throwable != null) {
                    Timber.tag(optimizedTag).e(throwable, optimizedMessage)
                } else {
                    Timber.tag(optimizedTag).e(optimizedMessage)
                }
            }
            LogLevel.VERBOSE -> Timber.tag(optimizedTag).v(optimizedMessage)
        }
    }

    /**
     * 设置优化开关
     */
    fun setOptimizationEnabled(enabled: Boolean) {
        optimizationEnabled = enabled
        Timber.tag("LOG_OPTIMIZER").i("日志优化已${if (enabled) "启用" else "禁用"}")
    }

    /**
     * 设置详细统计开关
     */
    fun setDetailedStatsEnabled(enabled: Boolean) {
        detailedStatsEnabled = enabled
        Timber.tag("LOG_OPTIMIZER").i("详细统计已${if (enabled) "启用" else "禁用"}")
    }

    /**
     * 获取优化器统计信息
     * 🔄 重构：现在主要显示CompactIdGenerator的统计信息
     */
    fun getOptimizerStats(): String {
        val compactStats = CompactIdGenerator.getStats()
        return "ID生成器统计 - ${compactStats.toLogString()}"
    }

    /**
     * 清理优化器缓存
     */
    fun clearCache() {
        CompactIdGenerator.clearCache()
        Timber.tag("LOG_OPTIMIZER").i("优化器缓存已清理")
    }

    /**
     * 日志级别枚举
     */
    private enum class LogLevel {
        VERBOSE, DEBUG, INFO, WARNING, ERROR
    }
}

/**
 * 便捷扩展函数 - 模块特定日志
 */
object ModuleLogger {

    /**
     * 模板模块日志
     */
    object Template {
        fun d(message: String) = OptimizedLogger.d("TEMPLATE", message)
        fun i(message: String) = OptimizedLogger.i("TEMPLATE", message)
        fun w(message: String) = OptimizedLogger.w("TEMPLATE", message)
        fun e(message: String, throwable: Throwable? = null) = OptimizedLogger.e("TEMPLATE", message, throwable)
    }

    /**
     * ThinkingBox模块日志
     */
    object ThinkingBox {
        fun d(message: String) = OptimizedLogger.d("THINKING", message)
        fun i(message: String) = OptimizedLogger.i("THINKING", message)
        fun w(message: String) = OptimizedLogger.w("THINKING", message)
        fun e(message: String, throwable: Throwable? = null) = OptimizedLogger.e("THINKING", message, throwable)
    }

    /**
     * Coach模块日志 - 🔄 重构：使用COA前缀标签
     */
    object Coach {
        fun d(message: String) = OptimizedLogger.d("COA-CORE", message)
        fun i(message: String) = OptimizedLogger.i("COA-CORE", message)
        fun w(message: String) = OptimizedLogger.w("COA-CORE", message)
        fun e(message: String, throwable: Throwable? = null) = OptimizedLogger.e("COA-ERROR", message, throwable)

        // 🔥 【新增】AI相关快速日志
        fun ai(message: String) = OptimizedLogger.i("COA-AI-FLOW", message)
        fun mvi(message: String) = OptimizedLogger.d("COA-MVI", message)
        fun data(message: String) = OptimizedLogger.d("COA-DATA", message)
        fun ui(message: String) = OptimizedLogger.d("COA-UI", message)
    }

    /**
     * 网络模块日志
     */
    object Network {
        fun d(message: String) = OptimizedLogger.d("NETWORK", message)
        fun i(message: String) = OptimizedLogger.i("NETWORK", message)
        fun w(message: String) = OptimizedLogger.w("NETWORK", message)
        fun e(message: String, throwable: Throwable? = null) = OptimizedLogger.e("NETWORK", message, throwable)
    }
}

/**
 * 性能监控扩展
 */
object PerformanceLogger {

    /**
     * 记录操作耗时
     */
    inline fun <T> measureTime(tag: String, operation: String, block: () -> T): T {
        val startTime = System.currentTimeMillis()
        val result = block()
        val duration = System.currentTimeMillis() - startTime

        OptimizedLogger.d(tag, "⏱️ $operation 耗时: ${duration}ms")
        return result
    }

    /**
     * 记录内存使用
     */
    fun logMemoryUsage(tag: String, context: String) {
        val runtime = Runtime.getRuntime()
        val usedMemory = (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024
        val maxMemory = runtime.maxMemory() / 1024 / 1024

        OptimizedLogger.d(tag, "📊 $context 内存使用: ${usedMemory}MB/${maxMemory}MB")
    }
}
