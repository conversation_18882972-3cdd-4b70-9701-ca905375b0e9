package com.example.gymbro.shared.models.workout

import com.example.gymbro.shared.models.exercise.DifficultyLevel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 模板状态枚举 - 优化的二元状态设计
 * 🎯 核心设计原则：简化状态管理，符合用户心智模型
 */
@Serializable
enum class TemplateState {
    /**
     * 草稿状态
     * - 特征：模板处于草稿阶段，可以继续编辑
     * - UI显示：显示"保存草稿" + "发布模板"按钮
     * - Tab位置：显示在草稿Tab中
     * - 包含场景：新建模板、编辑中模板、已保存草稿
     */
    DRAFT,

    /**
     * 已发布状态
     * - 特征：模板已正式发布，作为正式模板使用
     * - UI显示：显示"保存为草稿" + "更新模板"按钮
     * - Tab位置：显示在模板Tab中
     */
    PUBLISHED
}

/**
 * 训练模板数据传输对象 (纯DTO)
 *
 * 用于模块间数据传输，不包含业务逻辑或UI依赖
 * 支持统一JSON数据中心架构的核心Template功能
 * 按照训练模板.md v1.0设计规范实现，扩展支持动作库集成
 *
 * @property id 模板唯一标识符
 * @property name 模板名称
 * @property description 模板描述
 * @property difficulty 难度等级
 * @property difficultyLevel 新增：难度等级枚举（统一JSON数据中心）
 * @property category 模板分类
 * @property exercises 模板动作列表
 * @property metadata 新增：模板元数据信息
 * @property source 模板来源
 * @property createdAt 创建时间戳
 * @property updatedAt 更新时间戳
 * @property version 版本号
 */
@Serializable
data class WorkoutTemplateDto(
    val id: String,
    val name: String,
    val description: String = "",
    val difficulty: Difficulty = Difficulty.MEDIUM,

    /**
     * 新增：难度等级枚举（统一JSON数据中心要求）
     */
    val difficultyLevel: DifficultyLevel? = null,

    val category: TemplateCategory = TemplateCategory.STRENGTH,
    val exercises: List<TemplateExerciseDto>,

    /**
     * 新增：模板元数据（统一JSON数据中心要求）
     */
    val metadata: TemplateMetadataDto? = null,

    val source: TemplateSource = TemplateSource.USER,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = createdAt,
    val version: Int = 1,

    // === 🔥 严格状态管理字段 - 零null值容忍 ===
    /**
     * 模板状态（严格管理，非null）
     * 🎯 设计原则：单一状态字段，消除状态冲突和歧义
     * 🎯 默认值：DRAFT - 创建模版点击后赋予的默认属性=草稿
     */
    val templateState: TemplateState = TemplateState.DRAFT,

    /**
     * 当前版本号（严格管理，非null）
     */
    val currentVersion: Int = 1,

    /**
     * 最后发布时间（仅当状态为PUBLISHED时有效）
     */
    val lastPublishedAt: Long = 0L,
) {
    // === 🔥 严格状态驱动方法 - 按照Template_Naming_Convention_Baseline.md 6.3节要求 ===

    /**
     * 获取当前模板状态（严格管理，直接返回状态枚举）
     */
    fun getCurrentTemplateState(): TemplateState = templateState

    /**
     * 是否应该在模板Tab显示
     * 规则：只有PUBLISHED状态的模板才在模板Tab显示
     */
    fun shouldShowInTemplatesTab(): Boolean = (templateState == TemplateState.PUBLISHED)

    /**
     * 是否应该在草稿Tab显示
     * 规则：只有DRAFT状态的模板才在草稿Tab显示
     */
    fun shouldShowInDraftsTab(): Boolean = (templateState == TemplateState.DRAFT)

    /**
     * 是否可以保存草稿
     * 规则：草稿状态可以保存修改，已发布状态不能退回草稿
     */
    fun canSaveAsDraft(): Boolean = (templateState == TemplateState.DRAFT)

    /**
     * 是否可以发布
     * 规则：任何状态都可以发布（草稿升级为模板，模板更新）
     */
    fun canPublish(): Boolean = true

    /**
     * 获取状态描述
     */
    fun getStatusDescription(): String = when (templateState) {
        TemplateState.DRAFT -> "草稿"
        TemplateState.PUBLISHED -> "已发布"
    }

    /**
     * 🔥 向后兼容方法 - 为现有代码提供过渡支持
     * @deprecated 使用 shouldShowInDraftsTab() 替代
     */
    @Deprecated("使用严格状态管理方法 shouldShowInDraftsTab()", ReplaceWith("shouldShowInDraftsTab()"))
    val actualIsDraft: Boolean
        get() = shouldShowInDraftsTab()

    /**
     * 🔥 向后兼容方法 - 为现有代码提供过渡支持
     * @deprecated 使用 shouldShowInTemplatesTab() 替代
     */
    @Deprecated("使用严格状态管理方法 shouldShowInTemplatesTab()", ReplaceWith("shouldShowInTemplatesTab()"))
    val actualIsPublished: Boolean
        get() = shouldShowInTemplatesTab()

    companion object {
        /**
         * 生成新的模板ID
         */
        fun generateId(): String {
            return java.util.UUID.randomUUID().toString()
        }

        /**
         * 生成用户模板ID（统一JSON数据中心ID规则）
         */
        fun generateUserTemplateId(userId: String): String {
            return "tmpl_${userId}_${java.util.UUID.randomUUID()}"
        }

        /**
         * 创建空白模板（严格状态管理）
         * 🎯 默认状态：DRAFT - 创建模版点击后赋予的默认属性=草稿
         */
        fun empty(): WorkoutTemplateDto {
            return WorkoutTemplateDto(
                id = generateId(),
                name = "",
                exercises = emptyList(),
                templateState = TemplateState.DRAFT
            )
        }

        /**
         * 创建用户自定义模板（统一JSON数据中心功能）
         * 🎯 严格状态管理：创建模版点击后默认为草稿状态
         */
        fun createUserTemplate(
            userId: String,
            name: String,
            description: String = "",
            exercises: List<TemplateExerciseDto>,
            category: TemplateCategory = TemplateCategory.STRENGTH,
            difficultyLevel: DifficultyLevel? = null,
        ): WorkoutTemplateDto {
            return WorkoutTemplateDto(
                id = generateUserTemplateId(userId),
                name = name,
                description = description,
                exercises = exercises,
                category = category,
                difficultyLevel = difficultyLevel,
                source = TemplateSource.USER,
                templateState = TemplateState.DRAFT,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis(),
            )
        }

        /**
         * 从旧版难度转换为新版难度枚举
         */
        fun difficultyToEnum(difficulty: Difficulty): DifficultyLevel {
            return when (difficulty) {
                Difficulty.EASY -> DifficultyLevel.BEGINNER
                Difficulty.MEDIUM -> DifficultyLevel.INTERMEDIATE
                Difficulty.HARD -> DifficultyLevel.ADVANCED
                Difficulty.EXPERT -> DifficultyLevel.EXPERT
            }
        }

        /**
         * 从新版难度枚举转换为旧版难度
         */
        fun enumToDifficulty(difficultyLevel: DifficultyLevel): Difficulty {
            return when (difficultyLevel) {
                DifficultyLevel.BEGINNER -> Difficulty.EASY
                DifficultyLevel.NOVICE -> Difficulty.EASY
                DifficultyLevel.INTERMEDIATE -> Difficulty.MEDIUM
                DifficultyLevel.ADVANCED -> Difficulty.HARD
                DifficultyLevel.EXPERT -> Difficulty.EXPERT
            }
        }
    }

    /**
     * 🔥 新增：计算整个模板的总训练量（公斤）
     * 基于每个动作的 customSets 精确计算
     */
    val totalVolume: Float
        get() = exercises.sumOf { exercise ->
            if (exercise.customSets.isNotEmpty()) {
                exercise.customSets.sumOf { set ->
                    (set.targetWeight * set.targetReps).toDouble()
                }
            } else {
                // 向后兼容：使用基础字段
                val weight = exercise.targetWeight ?: 0f
                (weight * exercise.reps * exercise.sets).toDouble()
            }
        }.toFloat()

    /**
     * 🔥 新增：计算整个模板的总组数
     * 基于每个动作的 customSets 精确计算
     */
    val totalSets: Int
        get() = exercises.sumOf { exercise ->
            if (exercise.customSets.isNotEmpty()) {
                exercise.customSets.size
            } else {
                // 向后兼容：使用基础字段
                exercise.sets
            }
        }

    /**
     * 🔥 新增：整个模板的总结信息
     * 格式：总计 XXX.X kg • X 个动作 • Y 组
     */
    val summary: String
        get() = "总计 ${"%.1f".format(totalVolume)} kg • ${exercises.size} 个动作 • $totalSets 组"
}

/**
 * 模板动作数据传输对象（扩展版）
 * 支持统一JSON数据中心的详细动作配置
 *
 * @property id 新增：动作在模板中的唯一标识
 * @property exerciseId 关联的动作库ID
 * @property exerciseName 新增：动作名称（从动作库获取）
 * @property imageUrl 新增：动作图片URL（从动作库获取）
 * @property videoUrl 新增：动作视频URL（从动作库获取）
 * @property sets 组数
 * @property reps 次数
 * @property customSets 新增：详细组数配置
 * @property rpe 自感疲劳等级 (1-10)
 * @property targetWeight 目标重量 (kg)
 * @property restSec 休息时间 (秒)
 * @property notes 新增：用户备注
 */
@Serializable
data class TemplateExerciseDto(
    /**
     * 新增：动作在模板中的唯一标识
     */
    val id: String = java.util.UUID.randomUUID().toString(),

    val exerciseId: String,

    /**
     * 新增：动作名称（从动作库获取，统一JSON数据中心要求）
     */
    val exerciseName: String = "",

    /**
     * 新增：动作图片URL（从动作库获取）
     */
    val imageUrl: String? = null,

    /**
     * 新增：动作视频URL（从动作库获取）
     */
    val videoUrl: String? = null,

    val sets: Int = 3,
    val reps: Int = 10,

    /**
     * 新增：详细组数配置（统一JSON数据中心要求）
     */
    val customSets: List<TemplateSetDto> = emptyList(),

    val rpe: Float? = null,
    val targetWeight: Float? = null,
    val restTimeSeconds: Int = 90,

    /**
     * 新增：用户备注（统一JSON数据中心要求）
     */
    val notes: String? = null,
) {
    /**
     * 🔥 动态计算的摘要属性
     *
     * 基于 customSets 的实时数据生成摘要，解决缩略图不更新的问题。
     * 优先使用 customSets 进行精确统计，提供向后兼容的备用逻辑。
     *
     * @return 格式化的摘要文本，如 "总计 1250.0 kg • 3 组"
     */
    val summary: String
        get() {
            // 优先使用 customSets 进行精确统计
            if (customSets.isNotEmpty()) {
                // 计算总体积 = Σ(重量 * 次数)
                val totalVolume = customSets.sumOf {
                    (it.targetWeight * it.targetReps).toDouble()
                }.toFloat()
                val totalSetsCount = customSets.size

                // 返回格式： "总计 XXX.X kg • X 组"
                return "总计 ${String.format("%.1f", totalVolume)} kg • $totalSetsCount 组"
            } else {
                // 向后兼容：使用基础字段的备用逻辑
                val totalSetsCount = sets
                val avgWeight = targetWeight ?: 0f
                val avgReps = reps
                val totalVolume = totalSetsCount * avgWeight * avgReps

                return "总计 ${String.format("%.1f", totalVolume)} kg • $totalSetsCount 组"
            }
        }
}

/**
 * 新增：模板组数配置（统一JSON数据中心要求）
 * 🔥 v2.1: 添加每组独立的休息时间支持
 * 🔥 v2.2: 强化序列化配置，确保数据完整性
 */
@Serializable
data class TemplateSetDto(
    @SerialName("setNumber") val setNumber: Int,
    @SerialName("weight") val targetWeight: Float = 0f, // 0~999 kg - 🔥 统一JSON字段名为weight
    @SerialName("reps") val targetReps: Int = 0, // 0~99 次 - 🔥 统一JSON字段名为reps
    @SerialName("restTimeSeconds") val restTimeSeconds: Int = 90, // 0~999 秒（每组独立）
    @SerialName("targetDuration") val targetDuration: Int? = null, // 目标时长（秒，用于计时类动作）
    @SerialName("rpe") val rpe: Float? = null, // 目标RPE
)

/**
 * 新增：模板元数据（统一JSON数据中心要求）
 */
@Serializable
data class TemplateMetadataDto(
    /**
     * 预计训练时长（分钟）
     */
    val estimatedDuration: Int = 30,

    /**
     * 目标肌群列表
     */
    val targetMuscleGroups: List<String> = emptyList(),

    /**
     * 所需器材列表
     */
    val equipment: List<String> = emptyList(),

    /**
     * 模板标签
     */
    val tags: List<String> = emptyList(),

    /**
     * 模板作者（用于用户自定义模板）
     */
    val authorId: String? = null,

    /**
     * 模板作者姓名
     */
    val authorName: String? = null,

    /**
     * 是否公开分享
     */
    val isPublic: Boolean = false,

    /**
     * 使用次数/流行度
     */
    val popularity: Int = 0,
)

/**
 * 难度等级枚举（保持向后兼容）
 */
@Serializable
enum class Difficulty {
    EASY,
    MEDIUM,
    HARD,
    EXPERT,
}

/**
 * 模板分类枚举（扩展版）
 */
@Serializable
enum class TemplateCategory {
    STRENGTH, // 力量训练
    CARDIO, // 有氧训练
    FLEXIBILITY, // 柔韧性
    MIXED, // 混合训练
    REHABILITATION, // 康复训练
    UPPER_BODY, // 新增：上肢训练
    LOWER_BODY, // 新增：下肢训练
    CORE, // 新增：核心训练
    FULL_BODY, // 新增：全身训练
    CUSTOM, // 新增：自定义分类
}

/**
 * 模板来源枚举（扩展版）
 */
@Serializable
enum class TemplateSource {
    AI, // AI生成
    USER, // 用户创建
    IMPORT, // 导入
    OFFICIAL, // 新增：官方模板
    COMMUNITY, // 新增：社区分享
    AI_ASSISTED, // 新增：AI辅助创建
}

// === 模板级别的计算属性 (基于 712template预览card优化.md) ===

/**
 * 计算模板的总组数
 *
 * 统计规则：
 * - 优先使用 customSets 数量进行精确统计
 * - customSets 为空时回退到基础 sets 字段
 * - 遵循 100% 基于 customSets 的统计原则
 *
 * @return 模板中所有动作的总组数
 */
val WorkoutTemplateDto.totalSets: Int
    get() = exercises.sumOf { exercise ->
        if (exercise.customSets.isNotEmpty()) {
            exercise.customSets.size
        } else {
            exercise.sets
        }
    }

/**
 * 计算模板的总训练量 (kg)
 *
 * 统计规则：
 * - 优先使用 customSets 进行精确计算：Σ(重量 × 次数)
 * - customSets 为空时回退到基础字段：(targetWeight × reps × sets)
 * - 空重量视为 0，不影响总体积计算
 *
 * @return 模板的总训练量，单位：千克 (kg)
 */
val WorkoutTemplateDto.totalVolume: Float
    get() = exercises.sumOf { exercise ->
        if (exercise.customSets.isNotEmpty()) {
            // 基于 customSets 的精确计算
            exercise.customSets.sumOf { set ->
                (set.targetWeight * set.targetReps).toDouble()
            }
        } else {
            // 向后兼容：基础字段计算
            val weight = exercise.targetWeight ?: 0f
            (weight * exercise.reps * exercise.sets).toDouble()
        }
    }.toFloat()

/**
 * 供 UI 直接使用的格式化摘要
 *
 * 为外层模板卡片、副标题等提供统一的摘要文本格式。
 * 基于 712template预览card优化.md 文档要求实现中文格式。
 * 格式：「总计 XXX.X kg • X 组」
 *
 * 统计规则：
 * - 优先使用 customSets 进行精确统计（通过 totalSets 和 totalVolume 扩展属性）
 * - customSets 为空时回退到基础字段计算
 * - 遵循 100% 基于 customSets 的统计原则
 *
 * 使用场景：
 * - 模板列表卡片的副标题
 * - 预览页面的摘要展示
 * - 外部训练卡片的快速预览
 *
 * @return 格式化的摘要文本，如 "总计 1250.0 kg • 18 组"
 */
val WorkoutTemplateDto.summary: String
    get() = "总计 ${"%.1f".format(totalVolume)} kg • $totalSets 组"

// === 🔥 严格状态驱动扩展方法 - 全局访问 ===

/**
 * 获取当前模板状态（全局扩展）
 * 🎯 严格状态管理：直接基于templateState枚举
 */
fun WorkoutTemplateDto.getCurrentTemplateState(): TemplateState = templateState

/**
 * 是否应该在模板Tab显示（全局扩展）
 * 🎯 规则：只有PUBLISHED状态的模板才在模板Tab显示
 */
fun WorkoutTemplateDto.shouldShowInTemplatesTab(): Boolean = (templateState == TemplateState.PUBLISHED)

/**
 * 是否应该在草稿Tab显示（全局扩展）
 * 🎯 规则：只有DRAFT状态的模板才在草稿Tab显示
 */
fun WorkoutTemplateDto.shouldShowInDraftsTab(): Boolean = (templateState == TemplateState.DRAFT)

/**
 * 是否可以保存草稿（全局扩展）
 * 🎯 规则：草稿状态可以保存修改，已发布状态不能退回草稿
 */
fun WorkoutTemplateDto.canSaveAsDraft(): Boolean = (templateState == TemplateState.DRAFT)

/**
 * 是否可以发布（全局扩展）
 * 🎯 规则：任何状态都可以发布（草稿升级为模板，模板更新）
 */
fun WorkoutTemplateDto.canPublish(): Boolean = true

/**
 * 获取状态描述（全局扩展）
 */
fun WorkoutTemplateDto.getStatusDescription(): String = when (templateState) {
    TemplateState.DRAFT -> "草稿"
    TemplateState.PUBLISHED -> "已发布"
}
