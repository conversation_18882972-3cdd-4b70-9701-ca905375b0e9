package com.example.gymbro.core.conversation

import timber.log.Timber

/**
 * ConversationIdManager扩展函数
 * 
 * 提供便捷的操作方法和语法糖
 */

/**
 * 安全获取消息上下文，带日志记录
 */
fun ConversationIdManager.getMessageContextSafely(messageId: String): ConversationIdManager.MessageContext? {
    return try {
        getMessageContext(messageId)?.also {
            Timber.d("✅ 获取消息上下文成功: ${it.getDisplayId()}")
        } ?: run {
            Timber.w("⚠️ 消息上下文不存在: messageId=${messageId.take(8)}...")
            null
        }
    } catch (e: Exception) {
        Timber.e(e, "❌ 获取消息上下文失败: messageId=${messageId.take(8)}...")
        null
    }
}

/**
 * 安全获取会话上下文，带日志记录
 */
fun ConversationIdManager.getSessionContextSafely(sessionId: String): ConversationIdManager.SessionContext? {
    return try {
        getSessionContext(sessionId)?.also {
            Timber.d("✅ 获取会话上下文成功: sessionId=$sessionId")
        } ?: run {
            Timber.w("⚠️ 会话上下文不存在: sessionId=$sessionId")
            null
        }
    } catch (e: Exception) {
        Timber.e(e, "❌ 获取会话上下文失败: sessionId=$sessionId")
        null
    }
}

/**
 * 创建消息上下文并记录日志
 */
fun ConversationIdManager.createMessageContextWithLogging(sessionId: String): ConversationIdManager.MessageContext {
    return createMessageContext(sessionId).also { context ->
        Timber.i("🆕 创建新消息: ${context.getDisplayId()} in session $sessionId")
    }
}

/**
 * 创建会话上下文并记录日志
 */
fun ConversationIdManager.createSessionContextWithLogging(userId: String): ConversationIdManager.SessionContext {
    return createSessionContext(userId).also { context ->
        Timber.i("🆕 创建新会话: ${context.sessionId} for user $userId")
    }
}

/**
 * 验证消息ID并返回友好的错误信息
 */
fun ConversationIdManager.validateMessageIdWithMessage(messageId: String): Pair<Boolean, String> {
    return when (val result = validateMessageId(messageId)) {
        is ConversationIdManager.ValidationResult.Valid -> true to "消息ID有效"
        is ConversationIdManager.ValidationResult.Invalid -> false to "消息ID无效: ${result.reason}"
        is ConversationIdManager.ValidationResult.NotFound -> false to "消息ID不存在: ${result.searchedId.take(8)}..."
    }
}

/**
 * 智能查找消息，支持多种ID格式
 */
fun ConversationIdManager.findMessageSmart(idHint: String): ConversationIdManager.MessageContext? {
    if (idHint.isBlank()) return null
    
    return findBestMatchingMessage(idHint)?.also { context ->
        Timber.d("🔍 智能匹配成功: '$idHint' -> ${context.getDisplayId()}")
    } ?: run {
        Timber.w("🔍 智能匹配失败: '$idHint'")
        null
    }
}

/**
 * 获取会话的消息数量
 */
fun ConversationIdManager.getSessionMessageCount(sessionId: String): Int {
    return getSessionContext(sessionId)?.messageChain?.size ?: 0
}

/**
 * 检查会话是否为空
 */
fun ConversationIdManager.isSessionEmpty(sessionId: String): Boolean {
    return getSessionMessageCount(sessionId) == 0
}

/**
 * 获取会话的最后活跃时间
 */
fun ConversationIdManager.getSessionLastActiveTime(sessionId: String): Long? {
    return getSessionContext(sessionId)?.lastActiveAt
}

/**
 * 检查消息是否属于指定会话
 */
fun ConversationIdManager.isMessageInSession(messageId: String, sessionId: String): Boolean {
    return getMessageContext(messageId)?.sessionId == sessionId
}

/**
 * 获取统计信息的简化字符串
 */
fun ConversationIdManager.getStatsString(): String {
    return getStatistics().toLogString()
}

/**
 * MessageContext扩展函数
 */

/**
 * 检查消息是否过期
 */
fun ConversationIdManager.MessageContext.isExpired(maxAgeMs: Long): Boolean {
    return System.currentTimeMillis() - timestamp > maxAgeMs
}

/**
 * 获取消息年龄（毫秒）
 */
fun ConversationIdManager.MessageContext.getAgeMs(): Long {
    return System.currentTimeMillis() - timestamp
}

/**
 * 获取格式化的年龄字符串
 */
fun ConversationIdManager.MessageContext.getFormattedAge(): String {
    val ageMs = getAgeMs()
    return when {
        ageMs < 1000 -> "${ageMs}ms"
        ageMs < 60000 -> "${ageMs / 1000}s"
        ageMs < 3600000 -> "${ageMs / 60000}m"
        else -> "${ageMs / 3600000}h"
    }
}

/**
 * SessionContext扩展函数
 */

/**
 * 获取会话持续时间（毫秒）
 */
fun ConversationIdManager.SessionContext.getDurationMs(): Long {
    return lastActiveAt - createdAt
}

/**
 * 获取格式化的持续时间字符串
 */
fun ConversationIdManager.SessionContext.getFormattedDuration(): String {
    val durationMs = getDurationMs()
    return when {
        durationMs < 60000 -> "${durationMs / 1000}s"
        durationMs < 3600000 -> "${durationMs / 60000}m"
        else -> "${durationMs / 3600000}h"
    }
}

/**
 * 检查会话是否活跃（最近有消息）
 */
fun ConversationIdManager.SessionContext.isActive(thresholdMs: Long = 5 * 60 * 1000L): Boolean {
    return System.currentTimeMillis() - lastActiveAt < thresholdMs
}

/**
 * 获取会话摘要信息
 */
fun ConversationIdManager.SessionContext.getSummary(): String {
    return "Session(id=$sessionId, messages=${messageChain.size}, duration=${getFormattedDuration()}, active=${isActive()})"
}
