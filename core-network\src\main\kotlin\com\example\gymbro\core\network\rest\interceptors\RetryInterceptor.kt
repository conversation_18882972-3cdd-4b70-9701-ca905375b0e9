package com.example.gymbro.core.network.rest.interceptors

import com.example.gymbro.core.logging.GymBroLogTags
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import okhttp3.Interceptor
import okhttp3.Response
import timber.log.Timber
import java.io.IOException
import java.net.SocketTimeoutException
import kotlin.math.pow
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds

/**
 * 重试拦截器 - 按照websock+Okhttp.md核心实现方案
 *
 * 🎯 功能：
 * - 指数退避重试策略：base * 2^attempt
 * - 最大重试次数：2次
 * - 基础延迟：1秒
 * - 指标收集：Metrics.incRetry()
 */
class RetryInterceptor(
    private val max: Int = 2,
    private val base: Duration = 1.seconds,
) : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        var attempt = 0
        var lastException: IOException? = null

        while (attempt <= max) {
            try {
                val response = chain.proceed(request)

                // 检查是否需要重试
                if (shouldRetry(response) && attempt < max) {
                    Timber.tag(GymBroLogTags.CoreNetwork.INTERCEPTOR_RETRY).w(
                        "🔄 [重试] HTTP ${response.code}, 第${attempt + 1}次重试"
                    )
                    response.close()

                    // 指数退避延迟
                    val delayMs = (base.inWholeMilliseconds * 2.0.pow(attempt)).toLong()
                    runBlocking { delay(delayMs) }

                    attempt++
                    continue
                }

                // 成功或不需要重试
                return response
            } catch (e: IOException) {
                lastException = e

                if (shouldRetryOnException(e) && attempt < max) {
                    Timber.w(e, "🔄 网络异常，第${attempt + 1}次重试: ${request.url}")

                    // 指数退避延迟
                    val delayMs = (base.inWholeMilliseconds * 2.0.pow(attempt)).toLong()
                    runBlocking { delay(delayMs) }

                    attempt++

                    // 🔥 【修复】记录重试统计
                    recordRetryAttempt(attempt, e)

                    continue
                }

                // 不重试或重试次数用完
                break
            }
        }

        // 重试失败，抛出最后的异常
        Timber.e(lastException, "🚨 重试${max}次后仍然失败: ${request.url}")
        throw lastException!!
    }

    /**
     * 判断HTTP响应是否需要重试
     */
    private fun shouldRetry(response: Response): Boolean {
        return when (response.code) {
            // 服务器错误，可以重试
            502, 503, 504 -> true
            // 429 Too Many Requests，可以重试
            429 -> true
            // 其他错误不重试
            else -> false
        }
    }

    /**
     * 判断异常是否需要重试
     */
    private fun shouldRetryOnException(exception: IOException): Boolean {
        return when (exception) {
            // 超时错误可以重试
            is SocketTimeoutException -> true
            // 连接重置可以重试
            is java.net.SocketException -> {
                exception.message?.contains("Connection reset") == true ||
                    exception.message?.contains("Connection refused") == true
            }
            // 其他IO异常不重试
            else -> false
        }
    }

    /**
     * 🔥 【新增】记录重试统计
     */
    private fun recordRetryAttempt(attempt: Int, exception: IOException) {
        Timber.tag("RetryInterceptor").w(
            "🔄 [重试统计] 第${attempt}次重试, 原因: ${exception.javaClass.simpleName} - ${exception.message}"
        )

        // 这里可以集成更复杂的指标收集系统
        // 例如：metricsReporter.incRetry(exception.javaClass.simpleName)
    }
}
