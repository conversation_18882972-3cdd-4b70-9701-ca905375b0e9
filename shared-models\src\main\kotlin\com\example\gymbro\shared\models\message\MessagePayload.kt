package com.example.gymbro.shared.models.message

import androidx.annotation.Keep
import kotlinx.serialization.Serializable

/**
 * 消息事件Payload - 纯业务数据载体
 *
 * 根据706任务保存.md规格设计，遵循shared-models模块EntityWrapper架构：
 * - 纯Kotlin data class，零Android依赖
 * - 使用kotlinx.serialization，支持跨端使用
 * - 只包含业务数据，无任何业务逻辑
 * - 配合EntityWrapper<MessagePayload>使用
 * - 支持版本控制和乐观锁机制
 *
 * <AUTHOR> 4.0 sonnet
 * @since 706任务保存 (两层数据模型)
 */
@Serializable
@Keep
data class MessagePayload(
    /**
     * 消息事件唯一标识符
     */
    val eventId: String,

    /**
     * 会话ID
     * 🔥 【Plan B重构】保持字段名以确保序列化兼容性，但语义为sessionId
     */
    val conversationId: String, // 实际存储sessionId值

    /**
     * 消息序号（防并发乱序）
     */
    val sequence: Long,

    /**
     * 消息角色
     */
    val role: MessageRole,

    /**
     * 原始内容
     */
    val content: String,

    /**
     * Token数量（可选）
     */
    val tokenCount: Int? = null,

    /**
     * 用户ID（隐私合规）
     */
    val userId: String,

    /**
     * 消息类型
     */
    val messageType: MessageType = MessageType.TEXT,

    /**
     * 元数据（JSON格式）
     */
    val metadata: String? = null,

    /**
     * 思考节点数据
     */
    val thinkingNodes: String? = null,

    /**
     * 最终富文本内容
     */
    val finalMarkdown: String? = null,

    /**
     * 回复的消息ID
     */
    val inReplyToEventId: String? = null,

    /**
     * 远端同步时间戳
     */
    val syncedAt: Long? = null,

    /**
     * 同步版本号
     */
    val syncVersion: Int = 1,

    /**
     * 创建时间戳
     */
    val createdAt: Long,

    /**
     * 更新时间戳
     */
    val updatedAt: Long = System.currentTimeMillis(),
)

/**
 * 消息角色枚举
 */
@Serializable
@Keep
enum class MessageRole(val value: String, val displayName: String) {
    /**
     * 用户消息
     */
    USER("user", "User"),

    /**
     * AI助手消息
     */
    ASSISTANT("assistant", "Assistant"),

    /**
     * 系统消息
     */
    SYSTEM("system", "System"),
    ;

    companion object {
        /**
         * 从字符串值获取角色
         */
        fun fromValue(value: String): MessageRole {
            return values().find { it.value.equals(value, ignoreCase = true) } ?: USER
        }

        /**
         * 获取所有角色的显示名称
         */
        fun getAllDisplayNames(): List<String> {
            return values().map { it.displayName }
        }
    }
}

/**
 * 消息类型枚举
 */
@Serializable
@Keep
enum class MessageType(val value: String, val displayName: String) {
    /**
     * 文本消息
     */
    TEXT("text", "Text"),

    /**
     * 图片消息
     */
    IMAGE("image", "Image"),

    /**
     * 文件消息
     */
    FILE("file", "File"),

    /**
     * 思考过程消息
     */
    THINKING("thinking", "Thinking"),
    ;

    companion object {
        /**
         * 从字符串值获取类型
         */
        fun fromValue(value: String): MessageType {
            return values().find { it.value.equals(value, ignoreCase = true) } ?: TEXT
        }

        /**
         * 获取所有类型的显示名称
         */
        fun getAllDisplayNames(): List<String> {
            return values().map { it.displayName }
        }
    }
}
