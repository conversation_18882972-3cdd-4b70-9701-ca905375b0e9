# Coach History模块日志使用指南

## 📋 概述

本指南详细说明了Coach History子模块中日志系统的使用方法、标签规范和最佳实践。

🎯 **设计原则**: 遵循COA-子包-*标签规范，统一接入core的timber log进行动态管理和加载。

## 🏗️ 架构集成

### 统一接入Core Timber Log
- **动态管理**: 通过TimberManager统一加载CoachLogTree
- **标签路由**: 通过ModuleAwareTree自动路由COA-HISTORY-*标签
- **配置管理**: 通过LoggingConfig统一配置History标签
- **优化处理**: 通过LogTagOptimizer自动优化标签显示

### 与Coach主模块的关系
- **继承体系**: History作为Coach的子模块，继承Coach的日志管理体系
- **标签前缀**: 使用COA-HISTORY-*前缀，明确标识子模块
- **统一处理**: 由CoachLogTree统一处理所有COA-*标签

## 🏷️ History标签规范

### 核心业务标签
```kotlin
COA-HISTORY-CORE     // 核心历史记录业务
COA-HISTORY-ERROR    // 错误处理
COA-HISTORY-DEBUG    // 调试信息
COA-HISTORY-PERF     // 性能监控
```

### 功能模块标签
```kotlin
COA-HISTORY-PAGING   // 分页加载
COA-HISTORY-DB       // 数据库操作
COA-HISTORY-UI       // UI渲染
COA-HISTORY-SEARCH   // 搜索功能
COA-HISTORY-CACHE    // 缓存管理
COA-HISTORY-SYNC     // 同步操作
```

### MVI架构标签
```kotlin
COA-HISTORY-MVI-INTENT   // Intent处理
COA-HISTORY-MVI-REDUCER  // Reducer逻辑
COA-HISTORY-MVI-EFFECT   // Effect处理
COA-HISTORY-MVI-STATE    // State更新
```

### 数据层标签
```kotlin
COA-HISTORY-DATA-REPO    // Repository操作
COA-HISTORY-DATA-USECASE // UseCase执行
COA-HISTORY-DATA-MAPPER  // 数据映射
```

### 兼容旧标签
```kotlin
HISTORY-ACTOR           // → COA-HISTORY-CORE
```

## 🔧 使用方法

### 1. 基础日志使用
```kotlin
import timber.log.Timber
import com.example.gymbro.features.coach.history.logging.HistoryLogUtils

// 直接使用Timber + 标签
Timber.tag(HistoryLogUtils.TAG_CORE).i("历史记录加载开始")
Timber.tag(HistoryLogUtils.TAG_PAGING).d("分页加载: page=${page}")
Timber.tag(HistoryLogUtils.TAG_ERROR).e("历史记录加载失败", exception)
```

### 2. 快速日志方法
```kotlin
// 核心业务日志
HistoryLogUtils.Core.info("开始加载对话历史")
HistoryLogUtils.Core.error("历史记录处理失败", exception)

// 分页相关日志
HistoryLogUtils.Paging.loadStart(1)
HistoryLogUtils.Paging.loadSuccess(1, 20)
HistoryLogUtils.Paging.loadError(1, "网络错误")

// 数据库相关日志
HistoryLogUtils.Database.query("SELECT * FROM conversations", 15)
HistoryLogUtils.Database.insert("conversations", 1)

// UI相关日志
HistoryLogUtils.Ui.render("ConversationList", 20)
HistoryLogUtils.Ui.interaction("点击", "对话项")

// 搜索相关日志
HistoryLogUtils.Search.query("关键词")
HistoryLogUtils.Search.result("关键词", 5)

// MVI相关日志
HistoryLogUtils.Mvi.intent("LoadHistory")
HistoryLogUtils.Mvi.reducer("Loading -> Success")
HistoryLogUtils.Mvi.effect("NavigateToConversation")
```

### 3. 统一流程跟踪
```kotlin
// 历史记录流程跟踪
val sessionId = CompactIdGenerator.generateId("session")
HistoryLogUtils.logHistoryFlow("LOAD", "开始加载历史记录", sessionId)
HistoryLogUtils.logHistoryFlow("PROCESS", "处理历史数据", sessionId, "记录数: ${count}")
HistoryLogUtils.logHistoryFlow("COMPLETE", "历史记录加载完成", sessionId)

// 分页流程跟踪
HistoryLogUtils.logPagingFlow("LOAD_PAGE", 1, 20, 100)
HistoryLogUtils.logPagingFlow("APPEND_PAGE", 2, 20, 100)

// 数据库操作跟踪
HistoryLogUtils.logDatabaseOperation("SELECT", "conversations", 15, true)
HistoryLogUtils.logDatabaseOperation("INSERT", "conversations", 1, true)

// 搜索操作跟踪
HistoryLogUtils.logSearchOperation("关键词", 5, 150)

// 缓存操作跟踪
HistoryLogUtils.logCacheOperation("GET", "conversation_123", true)
HistoryLogUtils.logCacheOperation("PUT", "conversation_123", true)

// UI渲染跟踪
HistoryLogUtils.logUiRendering("ConversationList", 20, 45)

// 错误跟踪
HistoryLogUtils.logHistoryError("LOAD", "分页加载失败", error.message, sessionId)
```

### 4. 性能监控
```kotlin
// 性能测量
HistoryLogUtils.PerformanceTracker.measureTime("历史记录加载") {
    loadHistoryData()
}

// 带返回值的性能测量
val result = HistoryLogUtils.PerformanceTracker.measureTimeWithResult("搜索历史记录") {
    searchHistory(query)
}

// 手动性能日志
Timber.tag(HistoryLogUtils.TAG_PERFORMANCE).i("⏱️ 历史记录渲染时间: ${duration}ms")
```

### 5. 通过CoachLogUtils快速访问
```kotlin
// 使用Coach主模块的快速方法
CoachLogUtils.History.info("历史记录模块初始化")
CoachLogUtils.History.paging("加载第2页")
CoachLogUtils.History.database("查询对话记录")
CoachLogUtils.History.search("搜索关键词")
CoachLogUtils.History.cache("缓存命中")
CoachLogUtils.History.ui("渲染对话列表")

// MVI相关
CoachLogUtils.History.intent("LoadMoreHistory")
CoachLogUtils.History.reducer("Idle -> Loading")
CoachLogUtils.History.effect("ShowLoadingIndicator")
CoachLogUtils.History.state("HistoryLoaded")

// 数据层相关
CoachLogUtils.History.repo("获取对话历史")
CoachLogUtils.History.usecase("执行LoadHistoryUseCase")
CoachLogUtils.History.mapper("映射数据库记录到UI模型")
```

## 📊 日志级别规范

### 级别使用指南
- **ERROR**: 历史记录加载失败、数据库错误、搜索异常
- **WARN**: 分页加载超时、缓存失效、数据不一致
- **INFO**: 重要业务流程、分页加载成功、搜索结果
- **DEBUG**: 详细的流程跟踪、UI交互、数据映射
- **VERBOSE**: 高频操作（如滚动事件、实时搜索）

### 消息格式规范
```kotlin
// ✅ 好的日志格式
HistoryLogUtils.Core.info("🔥 [HISTORY-LOAD] 开始加载: sessionId=$sessionId, page=$page")
HistoryLogUtils.Paging.debug("📄 [PAGE-LOAD] 第${page}页加载完成: ${itemCount}项")

// ❌ 避免的格式
Timber.d("loading history") // 信息不足
Timber.i("分页完成") // 缺少具体信息
```

## 🎯 与Core Timber Log集成

### 自动路由机制
```kotlin
// 标签自动路由到Coach模块
tag?.startsWith("COA-HISTORY-") == true -> LoggingConfig.MODULE_COACH

// 通过CoachLogTree统一处理
private val HISTORY_TAGS = setOf(
    "COA-HISTORY-CORE", "COA-HISTORY-PAGING", "COA-HISTORY-DB", // ...
)
```

### 动态加载流程
```kotlin
// TimberManager自动加载CoachLogTree
loadCoachLogTree() // 包含History标签处理

// LoggingConfig统一配置
moduleConfigs[MODULE_COACH] = ModuleLogConfig(
    tags = setOf(
        "COA-HISTORY-CORE", "COA-HISTORY-PAGING", // ...
    )
)
```

### 标签优化处理
```kotlin
// LogTagOptimizer自动简化标签
.removePrefix("COA-HISTORY-") // COA-HISTORY-PAGING -> PAGING
```

## 📝 最佳实践

### 1. 标签选择原则
- **核心流程**: 使用 `COA-HISTORY-CORE`
- **分页操作**: 使用 `COA-HISTORY-PAGING`
- **数据库操作**: 使用 `COA-HISTORY-DB`
- **UI渲染**: 使用 `COA-HISTORY-UI`
- **搜索功能**: 使用 `COA-HISTORY-SEARCH`
- **错误处理**: 使用 `COA-HISTORY-ERROR`

### 2. 性能考虑
- 高频操作（如滚动）使用DEBUG级别
- 分页加载使用INFO级别记录关键节点
- 数据库操作记录执行时间和结果数量
- UI渲染记录组件名称和渲染项数

### 3. 错误处理规范
```kotlin
// ✅ 完整的错误日志
try {
    loadHistoryPage(page)
} catch (e: Exception) {
    HistoryLogUtils.logHistoryError("LOAD_PAGE", "分页加载失败", e.message ?: "未知错误", sessionId)
    throw e
}
```

## 🔗 相关文件

- `HistoryLogUtils.kt` - History专用日志工具类
- `CoachLogTree.kt` - Coach模块日志树（包含History标签处理）
- `CoachLogUtils.kt` - Coach主模块日志工具类（包含History快速方法）
- `core/logging/LoggingConfig.kt` - 全局日志配置
- `core/logging/TimberManager.kt` - 日志管理器

---

**更新日期**: 2025-01-26  
**版本**: v1.0 (COA-HISTORY前缀标准)  
**维护者**: GymBro开发团队
