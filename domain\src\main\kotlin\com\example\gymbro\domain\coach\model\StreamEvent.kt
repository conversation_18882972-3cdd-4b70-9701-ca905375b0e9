package com.example.gymbro.domain.coach.model

/**
 * AI流式事件 - Domain层模型 (ID统一化版本)
 *
 * 🎯 ID统一化架构：简化ID管理，确保数据流一致性
 * - 统一使用messageId，移除aiResponseId和userMessageId
 * - 确保Coach、Core-Network、ThinkingBox使用相同ID
 * - 简化事件模型，提高可维护性
 *
 * 设计原则：
 * 1. ID统一 - 所有事件使用统一的messageId
 * 2. 数据流一致 - 确保端到端ID匹配
 * 3. 类型安全 - sealed interface确保编译时类型检查
 * 4. 简化架构 - 移除冗余ID，降低复杂度
 *
 * @since v7.0 - ID统一化架构重构
 */
sealed interface StreamEvent {
    val sessionId: String
    val messageId: String // 🔥 【ID统一】使用统一的messageId，移除userMessageId和aiResponseId
    val timestamp: Long

    /**
     * 思考事件 - AI开始思考/处理
     *
     * 合并原有功能：
     * - Thinking: AI开始思考
     * - StepStarted: AI开始执行思考步骤
     *
     * 使用场景：
     * - AI收到用户消息，开始处理
     * - 显示"正在思考..."状态
     */
    data class Thinking(
        override val sessionId: String,
        override val messageId: String, // 🔥 【ID统一】使用统一的messageId
        override val timestamp: Long = System.currentTimeMillis(),
    ) : StreamEvent

    /**
     * 内容块事件 - 流式内容片段
     *
     * 合并原有功能：
     * - Chunk: 流式内容片段
     * - ContentDelta: 内容增量更新
     * - FinalStarted: 最终内容开始
     *
     * 使用场景：
     * - 流式响应中的每个内容片段
     * - 实时更新UI显示内容
     */
    data class Chunk(
        override val sessionId: String,
        override val messageId: String, // 🔥 【ID统一】使用统一的messageId
        override val timestamp: Long = System.currentTimeMillis(),
        val content: String,
    ) : StreamEvent

    /**
     * 完成事件 - 流式响应结束
     *
     * 合并原有功能：
     * - Done: 响应完成
     * - StepEnded: 思考步骤结束
     * - FinalEnded: 最终内容结束
     *
     * 使用场景：
     * - AI完成完整响应
     * - 停止流式状态，保存消息
     */
    data class Done(
        override val sessionId: String,
        override val messageId: String, // 🔥 【ID统一】使用统一的messageId
        override val timestamp: Long = System.currentTimeMillis(),
        val fullText: String,
        val finishReason: String? = null,
    ) : StreamEvent

    /**
     * 错误事件 - 处理异常情况
     *
     * 使用场景：
     * - 网络异常、API错误
     * - 解析错误、超时等
     * - 保留部分内容，标记错误状态
     */
    data class Error(
        override val sessionId: String,
        override val messageId: String, // 🔥 【ID统一】使用统一的messageId
        override val timestamp: Long = System.currentTimeMillis(),
        val error: Throwable,
        val partialContent: String? = null,
    ) : StreamEvent

    /**
     * 🔥 Phase事件 - 思考框轮播功能
     *
     * 新增功能：
     * - 支持ChatGPT风格的思考过程可视化
     * - 四个阶段轮播：分析问题→搜索信息→规划回答→生成内容
     * - 独立于现有流式事件，不影响消息内容处理
     *
     * 使用场景：
     * - AI开始处理用户请求时发送ANALYZE
     * - 调用搜索工具时发送SEARCH
     * - 规划回答结构时发送PLAN
     * - 开始生成最终内容时发送GENERATE
     *
     * @since 富文本功能 - 思考框轮播实现
     */
    data class Phase(
        override val sessionId: String,
        override val messageId: String, // 🔥 【ID统一】使用统一的messageId
        override val timestamp: Long = System.currentTimeMillis(),
        val phase: PhaseKind,
    ) : StreamEvent
}

/**
 * 🔥 思考阶段枚举 - 定义AI处理的四个核心阶段
 *
 * 基于文档富文本.md的设计要求：
 * - ANALYZE: 分析问题阶段，AI理解用户意图
 * - SEARCH: 搜索信息阶段，调用工具获取相关数据
 * - PLAN: 规划回答阶段，组织回答结构和逻辑
 * - GENERATE: 生成内容阶段，输出最终回答
 *
 * @since 富文本功能 - 思考框轮播实现
 */
enum class PhaseKind {
    ANALYZE, // 分析问题…
    SEARCH, // 搜索资料…
    PLAN, // 规划回答…
    GENERATE, // 生成内容…
}
