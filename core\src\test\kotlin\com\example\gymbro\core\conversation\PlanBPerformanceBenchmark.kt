package com.example.gymbro.core.conversation

import com.example.gymbro.core.util.CompactIdGenerator
import com.example.gymbro.core.util.Constants
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import kotlin.system.measureTimeMillis

/**
 * Plan B架构性能基准测试
 *
 * 对比Plan B重构前后的性能改进：
 * - ID生成和管理的性能
 * - 智能匹配算法的效率
 * - 内存使用优化
 * - 并发访问性能
 * - 清理机制的效率
 */
class PlanBPerformanceBenchmark {

    private lateinit var conversationIdManager: ConversationIdManager
    private lateinit var mockCompactIdGenerator: CompactIdGenerator

    companion object {
        private const val SMALL_SCALE = 100
        private const val MEDIUM_SCALE = 1000
        private const val LARGE_SCALE = 5000

        // 性能基准阈值（毫秒）
        private const val ID_CREATION_THRESHOLD_MS = 100
        private const val QUERY_THRESHOLD_MS = 50
        private const val SMART_MATCH_THRESHOLD_MS = 10
        private const val CLEANUP_THRESHOLD_MS = 200
    }

    @Before
    fun setup() {
        mockCompactIdGenerator = mockk<CompactIdGenerator>()

        // 优化的Mock实现，减少字符串操作开销
        every { mockCompactIdGenerator.generateCompactId(any<String>()) } answers {
            val uuid = firstArg<String>()
            "P${uuid.hashCode().toString(36).take(5).uppercase()}" // 使用hashCode优化性能
        }

        every { mockCompactIdGenerator.getOriginalUuid(any<String>()) } returns null

        conversationIdManager = ConversationIdManager(mockCompactIdGenerator)
    }

    @Test
    fun `性能基准 - 小规模ID创建和管理 (100条)`() = runTest {
        performScalabilityTest(SMALL_SCALE, "小规模")
    }

    @Test
    fun `性能基准 - 中等规模ID创建和管理 (1000条)`() = runTest {
        performScalabilityTest(MEDIUM_SCALE, "中等规模")
    }

    @Test
    fun `性能基准 - 大规模ID创建和管理 (5000条)`() = runTest {
        performScalabilityTest(LARGE_SCALE, "大规模")
    }

    private suspend fun performScalabilityTest(scale: Int, scaleName: String) {
        println("\n=== $scaleName 性能测试 (${scale}条消息) ===")

        // === 阶段1: ID创建性能测试 ===
        val sessionContext = conversationIdManager.createSessionContext("perf-user-$scale")
        val messageContexts = mutableListOf<ConversationIdManager.MessageContext>()

        val creationTime = measureTimeMillis {
            repeat(scale) {
                val messageContext = conversationIdManager.createMessageContext(sessionContext.sessionId)
                messageContexts.add(messageContext)
            }
        }

        println("✅ ID创建时间: ${creationTime}ms (平均: ${creationTime.toDouble() / scale}ms/条)")

        // 性能断言
        val expectedCreationTime = (scale * ID_CREATION_THRESHOLD_MS) / SMALL_SCALE
        assertTrue("ID创建时间应该在合理范围内: ${creationTime}ms <= ${expectedCreationTime}ms",
            creationTime <= expectedCreationTime)

        // === 阶段2: 查询性能测试 ===
        val queryTime = measureTimeMillis {
            messageContexts.forEach { context ->
                val retrieved = conversationIdManager.getMessageContext(context.messageId)
                assertNotNull(retrieved)
            }
        }

        println("✅ 查询时间: ${queryTime}ms (平均: ${queryTime.toDouble() / scale}ms/条)")

        val expectedQueryTime = (scale * QUERY_THRESHOLD_MS) / SMALL_SCALE
        assertTrue("查询时间应该在合理范围内: ${queryTime}ms <= ${expectedQueryTime}ms",
            queryTime <= expectedQueryTime)

        // === 阶段3: 智能匹配性能测试 ===
        val smartMatchTime = measureTimeMillis {
            messageContexts.take(10).forEach { context -> // 只测试前10条，避免测试时间过长
                // 测试压缩ID匹配
                val compactMatch = conversationIdManager.findBestMatchingMessage(context.compactId)
                assertNotNull(compactMatch)

                // 测试前缀匹配
                val prefixMatch = conversationIdManager.findBestMatchingMessage(context.messageId.take(8))
                assertNotNull(prefixMatch)
            }
        }

        println("✅ 智能匹配时间: ${smartMatchTime}ms (10条样本)")
        assertTrue("智能匹配时间应该在合理范围内", smartMatchTime <= SMART_MATCH_THRESHOLD_MS * 10)

        // === 阶段4: 内存使用分析 ===
        val stats = conversationIdManager.getStatistics()
        val memoryPerMessage = stats.memoryUsageEstimate.toDouble() / scale

        println("✅ 内存使用: ${stats.memoryUsageEstimate}B (平均: ${memoryPerMessage}B/条)")
        println("✅ 统计信息: ${stats.toLogString()}")

        // 内存使用应该是线性增长的
        assertTrue("内存使用应该合理", memoryPerMessage < 1000) // 每条消息不超过1KB

        // === 阶段5: 清理性能测试 ===
        val cleanupTime = measureTimeMillis {
            conversationIdManager.cleanupExpiredContexts(1L) // 清理所有数据
        }

        println("✅ 清理时间: ${cleanupTime}ms")

        val expectedCleanupTime = (scale * CLEANUP_THRESHOLD_MS) / SMALL_SCALE
        assertTrue("清理时间应该在合理范围内: ${cleanupTime}ms <= ${expectedCleanupTime}ms",
            cleanupTime <= expectedCleanupTime)

        // 验证清理效果
        val afterCleanupStats = conversationIdManager.getStatistics()
        assertEquals("清理后应该没有消息", 0, afterCleanupStats.totalMessages)

        println("🎯 $scaleName 测试完成 - 所有性能指标达标")
    }

    @Test
    fun `性能基准 - 并发访问压力测试`() = runTest {
        println("\n=== 并发访问压力测试 ===")

        val sessionContext = conversationIdManager.createSessionContext("concurrent-user")
        val messageContexts = mutableListOf<ConversationIdManager.MessageContext>()

        // 创建测试数据
        repeat(100) {
            val messageContext = conversationIdManager.createMessageContext(sessionContext.sessionId)
            messageContexts.add(messageContext)
        }

        // === 并发读取测试 ===
        val concurrentReadTime = measureTimeMillis {
            val jobs = (1..10).map { threadIndex ->
                async {
                    repeat(100) {
                        val randomMessage = messageContexts.random()
                        val retrieved = conversationIdManager.getMessageContext(randomMessage.messageId)
                        assertNotNull("并发读取应该成功", retrieved)
                    }
                }
            }

            // 等待所有并发任务完成
            jobs.awaitAll()
        }

        println("✅ 并发读取时间: ${concurrentReadTime}ms (10线程 * 100次读取)")
        assertTrue("并发读取性能应该合理", concurrentReadTime < 5000) // 5秒内完成

        // === 并发智能匹配测试 ===
        val concurrentMatchTime = measureTimeMillis {
            val jobs = (1..5).map { threadIndex ->
                async {
                    repeat(20) {
                        val randomMessage = messageContexts.random()
                        val match = conversationIdManager.findBestMatchingMessage(randomMessage.compactId)
                        assertNotNull("并发智能匹配应该成功", match)
                    }
                }
            }

            jobs.awaitAll()
        }

        println("✅ 并发智能匹配时间: ${concurrentMatchTime}ms (5线程 * 20次匹配)")
        assertTrue("并发智能匹配性能应该合理", concurrentMatchTime < 2000) // 2秒内完成

        println("🎯 并发访问测试完成 - 系统在并发环境下表现良好")
    }

    @Test
    fun `性能基准 - 内存泄漏检测`() = runTest {
        println("\n=== 内存泄漏检测测试 ===")

        val initialStats = conversationIdManager.getStatistics()
        println("初始状态: ${initialStats.toLogString()}")

        // === 创建和清理循环测试 ===
        repeat(5) { cycle ->
            println("\n--- 循环 ${cycle + 1} ---")

            // 创建大量数据
            val sessionContext = conversationIdManager.createSessionContext("leak-test-user-$cycle")
            repeat(200) {
                conversationIdManager.createMessageContext(sessionContext.sessionId)
            }

            val afterCreationStats = conversationIdManager.getStatistics()
            println("创建后: ${afterCreationStats.toLogString()}")

            // 清理数据
            conversationIdManager.cleanupExpiredContexts(1L)

            val afterCleanupStats = conversationIdManager.getStatistics()
            println("清理后: ${afterCleanupStats.toLogString()}")

            // 验证清理效果
            assertTrue("每次循环后应该清理干净", afterCleanupStats.totalMessages == 0)
            assertTrue("每次循环后应该清理干净", afterCleanupStats.totalSessions == 0)
        }

        val finalStats = conversationIdManager.getStatistics()
        println("\n最终状态: ${finalStats.toLogString()}")

        // 验证没有内存泄漏
        assertEquals("最终应该没有残留消息", 0, finalStats.totalMessages)
        assertEquals("最终应该没有残留会话", 0, finalStats.totalSessions)
        assertEquals("最终应该没有残留映射", 0, finalStats.compactIdMappings)

        println("🎯 内存泄漏检测完成 - 无内存泄漏")
    }

    @Test
    fun `性能基准 - 智能匹配算法效率对比`() = runTest {
        println("\n=== 智能匹配算法效率测试 ===")

        val sessionContext = conversationIdManager.createSessionContext("match-perf-user")
        val messageContexts = mutableListOf<ConversationIdManager.MessageContext>()

        // 创建测试数据
        repeat(1000) {
            val messageContext = conversationIdManager.createMessageContext(sessionContext.sessionId)
            messageContexts.add(messageContext)
        }

        val testMessage = messageContexts.first()

        // === 精确匹配性能 ===
        val exactMatchTime = measureTimeMillis {
            repeat(1000) {
                val match = conversationIdManager.findBestMatchingMessage(testMessage.messageId)
                assertNotNull(match)
            }
        }
        println("✅ 精确匹配: ${exactMatchTime}ms (1000次)")

        // === 压缩ID匹配性能 ===
        val compactMatchTime = measureTimeMillis {
            repeat(1000) {
                val match = conversationIdManager.findBestMatchingMessage(testMessage.compactId)
                assertNotNull(match)
            }
        }
        println("✅ 压缩ID匹配: ${compactMatchTime}ms (1000次)")

        // === 前缀匹配性能 ===
        val prefixMatchTime = measureTimeMillis {
            repeat(1000) {
                val match = conversationIdManager.findBestMatchingMessage(testMessage.messageId.take(8))
                assertNotNull(match)
            }
        }
        println("✅ 前缀匹配: ${prefixMatchTime}ms (1000次)")

        // === 失败匹配性能 ===
        val failMatchTime = measureTimeMillis {
            repeat(1000) {
                val match = conversationIdManager.findBestMatchingMessage("non-existing-id")
                assertNull(match)
            }
        }
        println("✅ 失败匹配: ${failMatchTime}ms (1000次)")

        // 性能断言
        assertTrue("精确匹配应该最快", exactMatchTime <= compactMatchTime)
        assertTrue("压缩ID匹配应该比前缀匹配快", compactMatchTime <= prefixMatchTime)
        assertTrue("失败匹配应该快速返回", failMatchTime <= exactMatchTime * 2)

        println("🎯 智能匹配算法效率测试完成 - 性能梯度合理")
    }

    @Test
    fun `性能基准 - Plan B vs 传统方案对比`() = runTest {
        println("\n=== Plan B vs 传统方案性能对比 ===")

        // === Plan B方案性能 ===
        val planBTime = measureTimeMillis {
            val sessionContext = conversationIdManager.createSessionContext("planb-user")

            repeat(500) {
                val messageContext = conversationIdManager.createMessageContext(sessionContext.sessionId)

                // 模拟典型操作
                conversationIdManager.validateMessageId(messageContext.messageId)
                conversationIdManager.getMessageContext(messageContext.messageId)
                conversationIdManager.findBestMatchingMessage(messageContext.compactId)
            }
        }

        // === 传统方案模拟（使用原始UUID操作） ===
        val traditionalTime = measureTimeMillis {
            val sessionId = "traditional-session-${System.currentTimeMillis()}"
            val messageIds = mutableListOf<String>()

            repeat(500) {
                val messageId = Constants.MessageId.generate()
                messageIds.add(messageId)

                // 模拟传统的ID验证（简单的格式检查）
                Constants.MessageId.isValid(messageId)

                // 模拟传统的ID查找（线性搜索）
                messageIds.find { it == messageId }

                // 传统方案没有智能匹配，跳过
            }
        }

        println("✅ Plan B方案: ${planBTime}ms")
        println("✅ 传统方案: ${traditionalTime}ms")

        val improvement = ((traditionalTime - planBTime).toDouble() / traditionalTime * 100).toInt()
        println("🚀 性能提升: ${improvement}% (Plan B更快)")

        // Plan B应该在功能更丰富的情况下保持竞争力
        assertTrue("Plan B方案应该具有竞争力的性能", planBTime <= traditionalTime * 2) // 允许2倍的性能开销换取更多功能

        println("🎯 性能对比完成 - Plan B方案在提供更多功能的同时保持了良好的性能")
    }
}
