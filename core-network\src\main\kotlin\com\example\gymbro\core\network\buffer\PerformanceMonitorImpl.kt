package com.example.gymbro.core.network.buffer

import timber.log.Timber
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚀 性能监控实现 - 简化版
 *
 * 保持接口兼容，简化内部实现为基础监控
 */
@Singleton
class PerformanceMonitorImpl @Inject constructor() : PerformanceMonitor {

    companion object {
        private const val TAG = "PerformanceMonitor"
    }

    // 简化的性能指标
    private val tokenProcessedCount = AtomicLong(0)
    private val totalLatencyMs = AtomicLong(0)
    private val errorCount = AtomicLong(0)
    private val bufferAdjustmentCount = AtomicLong(0)
    private var monitoringStartTime = System.currentTimeMillis()

    /**
     * 记录缓冲区调整事件 - 简化实现
     */
    override fun recordBufferAdjustment(oldSize: Int, newSize: Int, reason: String) {
        bufferAdjustmentCount.incrementAndGet()
        Timber.tag(TAG).d("🔧 缓冲调整: $oldSize → $newSize ($reason)")
    }

    /**
     * 记录性能指标 - 简化实现
     */
    override fun recordMetrics(metrics: ProcessingMetrics) {
        // 简化：仅记录基础指标
        tokenProcessedCount.incrementAndGet()
        totalLatencyMs.addAndGet(metrics.avgLatencyMs)
        if (metrics.errorRate > 0) {
            errorCount.incrementAndGet()
        }
    }

    /**
     * 获取当前性能指标 - 简化实现
     */
    override suspend fun getCurrentMetrics(): ProcessingMetrics {
        val currentTime = System.currentTimeMillis()
        val timeElapsed = currentTime - monitoringStartTime
        val totalTokens = tokenProcessedCount.get()

        val tokensPerSecond = if (timeElapsed > 0) {
            (totalTokens * 1000.0f / timeElapsed)
        } else {
            0f
        }

        val avgLatency = if (totalTokens > 0) totalLatencyMs.get() / totalTokens else 0L
        val errorRate = if (totalTokens > 0) errorCount.get().toFloat() / totalTokens else 0f

        return ProcessingMetrics(
            tokensPerSecond = tokensPerSecond,
            networkThroughput = tokensPerSecond * 1.1f, // 估算值
            memoryUsagePercent = getActualMemoryUsage(), // 🔥 【修复】使用真实内存使用率
            bufferUtilization = getActualBufferUtilization(), // 🔥 【修复】使用真实缓冲区利用率
            avgLatencyMs = avgLatency,
            errorRate = errorRate,
        )
    }

    /**
     * 记录token延迟 - 简化方法
     */
    fun recordTokenLatency(latencyMs: Long) {
        tokenProcessedCount.incrementAndGet()
        totalLatencyMs.addAndGet(latencyMs)
    }

    /**
     * 记录错误 - 简化方法
     */
    fun recordError(errorType: String, message: String) {
        errorCount.incrementAndGet()
        Timber.tag(TAG).w("❌ 错误记录: $errorType - $message")
    }

    /**
     * 重置统计 - 简化方法
     */
    fun reset() {
        tokenProcessedCount.set(0)
        totalLatencyMs.set(0)
        errorCount.set(0)
        bufferAdjustmentCount.set(0)
        monitoringStartTime = System.currentTimeMillis()
        Timber.tag(TAG).i("🔄 性能统计已重置")
    }

    /**
     * 🔥 【新增】获取真实的内存使用率
     */
    private fun getActualMemoryUsage(): Float {
        return try {
            val runtime = Runtime.getRuntime()
            val totalMemory = runtime.totalMemory()
            val freeMemory = runtime.freeMemory()
            val usedMemory = totalMemory - freeMemory
            (usedMemory.toFloat() / runtime.maxMemory()).coerceIn(0f, 1f)
        } catch (e: Exception) {
            Timber.w(e, "⚠️ 获取内存使用率失败，使用默认值")
            0.5f // 默认值
        }
    }

    /**
     * 🔥 【新增】获取真实的缓冲区利用率
     * 基于当前处理的token数量估算
     */
    private fun getActualBufferUtilization(): Float {
        return try {
            val currentTokens = tokenProcessedCount.get()
            val timeSinceStart = System.currentTimeMillis() - monitoringStartTime
            val timeWindowSeconds = (timeSinceStart / 1000f).coerceAtLeast(1f)

            // 基于token处理速率估算缓冲区利用率
            val tokensPerSecond = currentTokens / timeWindowSeconds
            val estimatedUtilization = (tokensPerSecond / 100f).coerceIn(0f, 1f) // 假设100 tokens/s为满负荷

            estimatedUtilization
        } catch (e: Exception) {
            Timber.w(e, "⚠️ 计算缓冲区利用率失败，使用默认值")
            0.3f // 保守的默认值
        }
    }
}

/**
 * 📊 性能统计摘要
 */
data class PerformanceSummary(
    val uptimeMs: Long, // 运行时间
    val totalTokensProcessed: Long, // 总处理Token数
    val totalErrors: Long, // 总错误数
    val totalBufferAdjustments: Long, // 总缓冲调整次数
    val avgLatencyMs: Long, // 平均延迟
    val overallThroughput: Float, // 总体吞吐量
    val overallErrorRate: Float, // 总体错误率
    val currentMetrics: ProcessingMetrics, // 当前指标
)
