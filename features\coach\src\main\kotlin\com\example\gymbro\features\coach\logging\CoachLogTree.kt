package com.example.gymbro.features.coach.logging

import android.util.Log
import kotlinx.coroutines.*
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * Coach 专用日志树
 *
 * 🎯 参考TB和WK模块标准，实现Coach模块统一日志管理
 * - 使用COA-前缀标识Coach模块日志
 * - 支持AI对话流日志聚合
 * - 过滤低优先级的token级别日志
 * - 统一Coach模块的日志标签和处理逻辑
 */
class CoachLogTree : Timber.DebugTree() {

    companion object {
        // === Coach模块简化标签定义（每个文件1-3个核心标签）===

        // 🔥 【核心标签 - 只保留最重要的3个】
        private val CORE_TAGS = setOf(
            "COA-CORE", // 核心业务流程
            "COA-ERROR", // 错误日志
            "COA-DEBUG", // 调试信息
        )

        // 🔥 【History子模块 - 只保留3个核心标签】
        private val HISTORY_TAGS = setOf(
            "COA-HISTORY", // History核心业务
            "COA-HISTORY-ERROR", // History错误
            "COA-HISTORY-DEBUG", // History调试
        )

        // 🔥 【兼容旧标签 - 逐步迁移】
        private val LEGACY_TAGS = setOf(
            "COACH-ERROR", "COACH-NEW", "COACH-LISTENER", "PROMPT-BUILDER",
            "COACH-USECASE", "COACH-REPO", "COACH-MESSAGE-FLOW", "TOKEN-FLOW",
            "REDUCER-DEBUG", "EFFECT-FLOW", "MVI-DEBUG", "HISTORY-ACTOR",
        )

        // 🔥 【高频日志标签 - 需要聚合处理】
        private val HIGH_FREQUENCY_TAGS = setOf(
            "TOKEN-FLOW"
        )

        // 🔥 【所有Coach相关标签】
        private val ALL_COACH_TAGS = CORE_TAGS + HISTORY_TAGS + LEGACY_TAGS
    }

    override fun isLoggable(tag: String?, priority: Int): Boolean {
        // 1. 🔥 【Coach标签过滤】只处理Coach相关标签
        if (!isCoachTag(tag)) {
            return false
        }

        // 2. 🔥 【错误日志】ERROR级别始终输出
        if (priority >= Log.ERROR) {
            return true
        }

        // 3. 🔥 【核心业务日志】WARN级别以上输出
        if (isCoreTag(tag) && priority >= Log.WARN) {
            return true
        }

        // 4. 🔥 【高频日志聚合】使用聚合器处理
        if (isHighFrequencyTag(tag)) {
            return handleHighFrequencyLog(tag, priority)
        }

        // 5. 🔥 【普通日志】INFO级别以上输出
        return priority >= Log.INFO
    }

    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        // 🔥 【高频日志聚合处理】
        if (isHighFrequencyTag(tag)) {
            LogAggregatorManager.getAggregator(tag ?: "COA-UNKNOWN").append(message)
            return
        }

        // 🔥 【标准日志输出】
        super.log(priority, tag, message, t)
    }

    /**
     * 检查是否为Coach相关标签
     */
    private fun isCoachTag(tag: String?): Boolean {
        if (tag == null) return false

        return tag.startsWith("COA-", ignoreCase = true) ||
               tag.contains("COACH", ignoreCase = true) ||
               tag in ALL_COACH_TAGS
    }

    /**
     * 检查是否为核心业务标签
     */
    private fun isCoreTag(tag: String?): Boolean {
        return tag in CORE_TAGS
    }

    /**
     * 检查是否为高频日志标签
     */
    private fun isHighFrequencyTag(tag: String?): Boolean {
        return tag in HIGH_FREQUENCY_TAGS
    }

    /**
     * 处理高频日志
     */
    private fun handleHighFrequencyLog(tag: String?, priority: Int): Boolean {
        // 高频日志只在ERROR级别直接输出，其他级别通过聚合器处理
        return priority >= Log.ERROR
    }

    /**
     * 🔥 【DEBUG构建检查】
     */
    private fun isDebugBuild(): Boolean {
        return try {
            // 实际项目中应该使用BuildConfig.DEBUG
            true
        } catch (e: Exception) {
            false
        }
    }

    override fun createStackElementTag(element: StackTraceElement): String? {
        // 为Coach相关的类添加特殊标签前缀
        val className = element.className
        return when {
            className.contains("coach", ignoreCase = true) -> {
                "COA-${super.createStackElementTag(element)}"
            }
            else -> super.createStackElementTag(element)
        }
    }
}

/**
 * Coach 日志配置工具
 * 🎯 参考ThinkingBox模块标准
 */
object CoachLogConfig {

    /**
     * 配置Coach日志系统
     */
    fun configure() {
        // 移除默认的日志树
        Timber.uprootAll()

        // 植入Coach专用日志树
        Timber.plant(CoachLogTree())

        // 使用Android Log避免递归调用
        android.util.Log.i("CoachLogConfig", "Coach日志系统已配置 (支持COA-*标签聚合)")
    }

    /**
     * 强制刷新所有日志聚合器
     */
    fun flushAllAggregators() {
        LogAggregatorManager.cleanup()
        android.util.Log.d("CoachLogConfig", "Coach日志聚合器已刷新")
    }
}

/**
 * 日志聚合器管理器
 * 🎯 参考ThinkingBox模块的LogAggregatorManager
 */
object LogAggregatorManager {
    private val aggregators = ConcurrentHashMap<String, LogAggregator>()

    fun getAggregator(tag: String): LogAggregator {
        return aggregators.computeIfAbsent(tag) { createAggregatorForTag(it) }
    }

    fun cleanup() {
        aggregators.values.forEach { it.flush() }
        aggregators.clear()
    }

    /**
     * 根据标签类型创建合适的聚合器
     */
    private fun createAggregatorForTag(tag: String): LogAggregator {
        return when (tag) {
            "COA-AI-TOKEN" -> LogAggregator(
                tag = tag,
                tokenThreshold = 100, // 100 token输出一次
                timeThresholdMs = 1500L,
                messageCountThreshold = 50,
            )
            "COA-AI-STREAM" -> LogAggregator(
                tag = tag,
                tokenThreshold = 50,
                timeThresholdMs = 2000L,
                messageCountThreshold = 20,
            )
            "TOKEN-FLOW" -> LogAggregator(
                tag = tag,
                tokenThreshold = 150,
                timeThresholdMs = 1000L,
                messageCountThreshold = 30,
            )
            else -> LogAggregator(
                tag = tag,
                tokenThreshold = 100,
                timeThresholdMs = 1500L,
                messageCountThreshold = 50,
            )
        }
    }
}

/**
 * 日志聚合器
 * 🎯 参考ThinkingBox模块的LogAggregator实现
 */
class LogAggregator(
    private val tag: String,
    private val tokenThreshold: Int = 100,
    private val timeThresholdMs: Long = 1500L,
    private val messageCountThreshold: Int = 50,
) {
    private val buffer = StringBuilder()
    private val tokenCount = AtomicLong(0)
    private val messageCount = AtomicLong(0)
    private var lastFlushTime = System.currentTimeMillis()
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    /**
     * 添加日志内容
     */
    fun append(message: String) {
        synchronized(buffer) {
            buffer.append(message).append(" ")

            // 简单token计数：按空格分词
            val tokens = message.split("\\s+".toRegex()).size
            val currentTokens = tokenCount.addAndGet(tokens.toLong())
            val currentMessages = messageCount.incrementAndGet()
            val currentTime = System.currentTimeMillis()

            // 检查是否需要刷新
            if (currentTokens >= tokenThreshold ||
                currentMessages >= messageCountThreshold ||
                (currentTime - lastFlushTime) >= timeThresholdMs
            ) {
                flush()
            }
        }
    }

    /**
     * 刷新缓冲区
     */
    fun flush() {
        synchronized(buffer) {
            if (buffer.isNotEmpty()) {
                val content = buffer.toString().trim()
                val tokens = tokenCount.get()
                val messages = messageCount.get()

                // 输出聚合日志
                android.util.Log.i(tag, "🔥 [聚合] $tokens tokens, $messages msgs: $content")

                // 重置计数器和缓冲区
                buffer.clear()
                tokenCount.set(0)
                messageCount.set(0)
                lastFlushTime = System.currentTimeMillis()
            }
        }
    }
}
