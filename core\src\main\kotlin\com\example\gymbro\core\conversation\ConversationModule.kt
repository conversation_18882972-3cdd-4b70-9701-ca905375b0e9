package com.example.gymbro.core.conversation

import com.example.gymbro.core.util.CompactIdGenerator
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 对话模块 - Hilt依赖注入配置
 *
 * 🎯 提供对话相关的依赖注入：
 * - ConversationIdManager单例
 * - CompactIdGenerator集成
 * - 线程安全的实例管理
 *
 * 🏗️ 架构特点：
 * - 遵循Hilt最佳实践
 * - 单例模式确保全局唯一性
 * - 自动处理依赖关系
 */
@Module
@InstallIn(SingletonComponent::class)
object ConversationModule {
    
    /**
     * 提供ConversationIdManager单例
     * 
     * 自动注入CompactIdGenerator依赖
     */
    @Provides
    @Singleton
    fun provideConversationIdManager(
        compactIdGenerator: CompactIdGenerator
    ): ConversationIdManager {
        return ConversationIdManager(compactIdGenerator)
    }
    
    /**
     * 提供CompactIdGenerator单例
     * 
     * 确保全局唯一的ID生成器实例
     */
    @Provides
    @Singleton
    fun provideCompactIdGenerator(): CompactIdGenerator {
        return CompactIdGenerator
    }
}
