2025-08-04 12:11:43.188 COACH-NEW                I  🚀 Coach使用新架构启动AI处理: sessionId=83accd38-2e92-4cc6-8d58-29065915bc12, messageId=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.188 COACH-NEW                I  📤 Coach发送消息到AI供应商: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.199 COACH-NEW                I  ✅ AI消息发送启动成功，等待响应回流到ThinkingBox
2025-08-04 12:11:43.200 TB-AiCoach...BoxDisplay  D  🎯 [LaunchThinkingBoxDisplay] 启动ThinkingBox: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.201 TB-Thinkin...isplayImpl  D  TB-Display: 🚀 [ThinkingBoxDisplay] 启动显示: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, sessionId=current_session
2025-08-04 12:11:43.202 TB-Thinkin...isplayImpl  D  TB-Display: 🛑 [ThinkingBoxDisplay] 停止显示: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.203 TB-Thinkin...isplayImpl  D  TB-Display: ✅ [ThinkingBoxDisplay] 显示会话已停止: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.208 TB-Thinkin...laySession  D  TB-Display: 🎬 [DisplaySession] 开始监听: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, sessionId=current_session
2025-08-04 12:11:43.209 TB-Thinkin...isplayImpl  D  TB-Display: ✅ [ThinkingBoxDisplay] 显示会话已启动: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, sessionId=current_session
2025-08-04 12:11:43.209 TB-AiCoach...BoxDisplay  D  🚀 [LaunchThinkingBoxDisplay] ThinkingBox已启动: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.225 TB-Thinkin...laySession  D  TB-Display: 🔍 [DisplaySession] 开始监听ThinkingBox完成状态: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, sessionId=current_session
2025-08-04 12:11:43.230 CNET-SERVICE-UnifiedAi   I  🚀 [请求开始] messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, 开始统一AI响应处理
2025-08-04 12:11:43.231 CNET-SERVICE-UnifiedAi   D  🔍 [数据流] Core-Network接收: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.240 TB-Thinkin...laySession  W  TB-Display: ⚠️ [DisplaySession] 未找到ViewModel状态流: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.256 CNET-SERVICE-UnifiedAi   D  📡 [流式AI请求] 发送到: https://catsapi.com/v1/chat/completions
2025-08-04 12:11:43.257 CNET-PROCESSOR-Stream    I  📊 [批量统计] 已处理tokens=1, 解析错误=0, 错误率=0%
2025-08-04 12:11:43.258 Coach-ThinkingBox        D  🔍 [显示决策] isStreaming=true
2025-08-04 12:11:43.294 TB-COACH                 I  🔍 AwaitingFirstToken状态: 实际AI消息ID=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.310 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=49->5
2025-08-04 12:11:43.320 TB-E2E-TRACE             E  🔍 [输出通道接收] messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, contentType=JSON_SSE, token='Hello...'
2025-08-04 12:11:43.324 CNET-OUTPUT-Direct       D  🔍 [数据流] DirectOutputChannel发送: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, token长度=5
2025-08-04 12:11:43.324 CNET-OUTPUT-Direct       I  📊 [输出统计] 已输出tokens=1, 活跃订阅者=0, 总订阅者=0
2025-08-04 12:11:43.377 TB-VM                    D  🚀 [ViewModel初始化] ThinkingBoxViewModel启动
2025-08-04 12:11:43.398 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=50->6
2025-08-04 12:11:43.400 TB-E2E-TRACE             E  🔍 [输出通道接收] messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, contentType=JSON_SSE, token=' there...'
2025-08-04 12:11:43.400 CNET-OUTPUT-Direct       D  🔍 [数据流] DirectOutputChannel发送: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, token长度=6
2025-08-04 12:11:43.405 CNET-TOKEN-OUT           I  📦 [518bc8d5-5d70-4e21-a725-dfed760434b3] ThinkingBox ← Batch[2 tokens, 22B, 64ms]
2025-08-04 12:11:43.428 CNET-TOKEN-BATCH         D  📊 批量刷新完成: received=0, output=2
2025-08-04 12:11:43.458 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=45->1
2025-08-04 12:11:43.459 TB-E2E-TRACE             E  🔍 [输出通道接收] messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, contentType=JSON_SSE, token='!...'
2025-08-04 12:11:43.459 CNET-OUTPUT-Direct       D  🔍 [数据流] DirectOutputChannel发送: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, token长度=1
2025-08-04 12:11:43.502 TB-COACH                 I  🚀 ThinkingBox激活: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, state=AwaitingFirstToken
2025-08-04 12:11:43.504 TB-Thinkin...thCallback  I  TB-API: 🔗 设置ThinkingBox完成回调: sessionId=83accd38-2e92-4cc6-8d58-29065915bc12, messageId=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.504 TB-Thinkin...thCallback  D  TB-API: 🚀 [初始化] messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, sessionId=83accd38-2e92-4cc6-8d58-29065915bc12
2025-08-04 12:11:43.514 TB-REDUCER-Segment       D  🔄 [Intent处理] Initialize
2025-08-04 12:11:43.514 TB-VM-Main               D  🔍 [数据流] ViewModel激活: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.514 TB-INIT                  I  🚀 [初始化] ThinkingBox: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.520 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=48->4
2025-08-04 12:11:43.521 TB-E2E-TRACE             E  🔍 [输出通道接收] messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, contentType=JSON_SSE, token=' How...'
2025-08-04 12:11:43.522 CNET-OUTPUT-Direct       D  🔍 [数据流] DirectOutputChannel发送: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, token长度=4
2025-08-04 12:11:43.524 CNET-TOKEN-OUT           I  📦 [518bc8d5-5d70-4e21-a725-dfed760434b3] ThinkingBox ← Batch[2 tokens, 10B, 62ms]
2025-08-04 12:11:43.526 CNET-TOKEN-BATCH         D  📊 批量刷新完成: received=0, output=2
2025-08-04 12:11:43.575 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=48->4
2025-08-04 12:11:43.577 TB-E2E-TRACE             E  🔍 [输出通道接收] messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, contentType=JSON_SSE, token=' can...'
2025-08-04 12:11:43.577 CNET-OUTPUT-Direct       D  🔍 [数据流] DirectOutputChannel发送: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, token长度=4
2025-08-04 12:11:43.597 TB-EFFECT                D  ⚡ [Effect处理] StartTokenStreamListening
2025-08-04 12:11:43.597 TB-STREAM                I  🎯 [Token流启动] messageId=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.599 TB-STREAM                I  ✅ [直接订阅] DirectOutputChannel: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.599 CNET-OUTPUT-Direct       D  🔗 [订阅] messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, 活跃订阅者=1
2025-08-04 12:11:43.605 TB-PARSER-Stream         I  🚀 [解析启动] 开始解析token流: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.609 TB-Thinkin...oxInternal  D  TB-VM:  🚀 初始化ThinkingBox: 518bc8d5-5d70-4e21-a725-dfed760434b3, hasTokenFlow=false
2025-08-04 12:11:43.609 TB-Thinkin...elProvider  D  TB-Provider:TB-Provider: 🔗 [ThinkingBoxViewModelProvider] 注册ViewModel: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.610 TB-REDUCER-Segment       D  🔄 [Intent处理] Initialize
2025-08-04 12:11:43.611 TB-VM-Main               D  🔍 [数据流] ViewModel激活: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.611 TB-INIT                  I  🚀 [初始化] ThinkingBox: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.611 TB-INIT                  D  ⏭️ [跳过重复] 初始化: 518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.613 TB-Thinkin...oxInternal  D  TB-VM:  🔗 连接HistoryActor到Effect流: 518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.617 TB-HistoryActor          I  TB-History: 🚀 [初始化] 开始监听ThinkingBox Effect流
2025-08-04 12:11:43.630 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=46->2
2025-08-04 12:11:43.631 TB-E2E-TRACE             E  🔍 [输出通道接收] messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, contentType=JSON_SSE, token=' I...'
2025-08-04 12:11:43.631 CNET-OUTPUT-Direct       D  🔍 [数据流] DirectOutputChannel发送: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, token长度=2
2025-08-04 12:11:43.633 CNET-TOKEN-OUT           I  📦 [518bc8d5-5d70-4e21-a725-dfed760434b3] ThinkingBox ← Batch[2 tokens, 12B, 54ms]
2025-08-04 12:11:43.635 CNET-TOKEN-BATCH         D  📊 批量刷新完成: received=0, output=2
2025-08-04 12:11:43.655 Coach-ThinkingBox        D  🔍 [显示决策] isStreaming=true
2025-08-04 12:11:43.667 TB-COACH                 I  🔍 AwaitingFirstToken状态: 实际AI消息ID=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.683 TB-E2E-TRACE             E  📨 [输出通道分发] messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, token=' I...'
2025-08-04 12:11:43.684 TB-STREAM                D  📥 [Token接收] content= I, type=JSON_SSE
2025-08-04 12:11:43.684 TB-PARSER-Stream         D  🔍 [数据流] 首个Token接收: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, 内容=' I...'
2025-08-04 12:11:43.684 TB-PARSER-Stream         I  🚀 [解析首Token] 开始处理token流: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3
2025-08-04 12:11:43.687 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=49->5
2025-08-04 12:11:43.688 TB-E2E-TRACE             E  🔍 [输出通道接收] messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, contentType=JSON_SSE, token=' help...'
2025-08-04 12:11:43.688 CNET-OUTPUT-Direct       D  🔍 [数据流] DirectOutputChannel发送: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, token长度=5
2025-08-04 12:11:43.691 TB-MAPPER-TRACE          E  🔍 [事件映射输入] event=TextChunk, context.parseState=PRE_THINK
2025-08-04 12:11:43.692 TB-MAPPER-Domain         D  🔍 [数据流] 解析开始: 解析器=DomainMapper
2025-08-04 12:11:43.692 TB-MAPPER-Domain         I  📝 [finalmermaid规范] PRE_THINK状态，自动创建perthink段
2025-08-04 12:11:43.698 TB-MAPPER-TRACE          E  ✅ [事件映射输出] 生成2个ThinkingEvent: [SegmentStarted, SegmentText]
2025-08-04 12:11:43.698 TB-REDUCER-Segment       D  🔄 处理事件: SegmentStarted
2025-08-04 12:11:43.698 TB-SEGMENT-Queue         D  🔍 [数据流] Segment创建: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, segmentId=perthink, 类型=PERTHINK
2025-08-04 12:11:43.698 TB-SEGMENT-Queue         D  🎯 创建段: perthink (PERTHINK)
2025-08-04 12:11:43.702 TB-SegmentQueueReducer   D  TB-Reducer: 📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
2025-08-04 12:11:43.702 TB-REDUCER-Segment       D  🔄 处理事件: SegmentText
2025-08-04 12:11:43.703 TB-SegmentQueueReducer   D  TB-Reducer: 📝 追加文本到段[perthink]:  I...
2025-08-04 12:11:43.703 TB-SegmentQueueReducer   D  TB-Reducer: 📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
2025-08-04 12:11:43.704 TB-E2E-TRACE             E  📨 [输出通道分发] messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, token=' help...'
2025-08-04 12:11:43.704 TB-STREAM                D  📥 [Token接收] content= help, type=JSON_SSE
2025-08-04 12:11:43.705 TB-MAPPER-TRACE          E  🔍 [事件映射输入] event=TextChunk, context.parseState=PRE_THINK
2025-08-04 12:11:43.705 TB-MAPPER-TRACE          E  ✅ [事件映射输出] 生成1个ThinkingEvent: [SegmentText]
2025-08-04 12:11:43.705 TB-REDUCER-Segment       D  🔄 处理事件: SegmentText
2025-08-04 12:11:43.705 TB-SegmentQueueReducer   D  TB-Reducer: 📝 追加文本到段[perthink]:  help...
2025-08-04 12:11:43.705 TB-SegmentQueueReducer   D  TB-Reducer: 📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
2025-08-04 12:11:43.712 Coach-ThinkingBox        D  🔍 [显示决策] isStreaming=true
2025-08-04 12:11:43.740 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=48->4
2025-08-04 12:11:43.741 TB-E2E-TRACE             E  🔍 [输出通道接收] messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, contentType=JSON_SSE, token=' you...'
2025-08-04 12:11:43.741 CNET-OUTPUT-Direct       D  🔍 [数据流] DirectOutputChannel发送: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, token长度=4
2025-08-04 12:11:43.743 CNET-TOKEN-OUT           I  📦 [518bc8d5-5d70-4e21-a725-dfed760434b3] ThinkingBox ← Batch[2 tokens, 18B, 53ms]
2025-08-04 12:11:43.744 CNET-TOKEN-BATCH         D  📊 批量刷新完成: received=0, output=2
2025-08-04 12:11:43.747 TB-E2E-TRACE             E  📨 [输出通道分发] messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, token=' you...'
2025-08-04 12:11:43.748 TB-STREAM                D  📥 [Token接收] content= you, type=JSON_SSE
2025-08-04 12:11:43.749 TB-MAPPER-TRACE          E  🔍 [事件映射输入] event=TextChunk, context.parseState=PRE_THINK
2025-08-04 12:11:43.749 TB-MAPPER-TRACE          E  ✅ [事件映射输出] 生成1个ThinkingEvent: [SegmentText]
2025-08-04 12:11:43.749 TB-REDUCER-Segment       D  🔄 处理事件: SegmentText
2025-08-04 12:11:43.750 TB-SegmentQueueReducer   D  TB-Reducer: 📝 追加文本到段[perthink]:  you...
2025-08-04 12:11:43.750 TB-SegmentQueueReducer   D  TB-Reducer: 📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
2025-08-04 12:11:43.793 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=45->1
2025-08-04 12:11:43.796 TB-E2E-TRACE             E  🔍 [输出通道接收] messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, contentType=JSON_SSE, token='?...'
2025-08-04 12:11:43.796 CNET-OUTPUT-Direct       D  🔍 [数据流] DirectOutputChannel发送: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, token长度=1
2025-08-04 12:11:43.798 TB-E2E-TRACE             E  📨 [输出通道分发] messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, token='?...'
2025-08-04 12:11:43.798 TB-STREAM                D  📥 [Token接收] content=?, type=JSON_SSE
2025-08-04 12:11:43.799 TB-MAPPER-TRACE          E  🔍 [事件映射输入] event=TextChunk, context.parseState=PRE_THINK
2025-08-04 12:11:43.799 TB-MAPPER-TRACE          E  ✅ [事件映射输出] 生成1个ThinkingEvent: [SegmentText]
2025-08-04 12:11:43.800 TB-REDUCER-Segment       D  🔄 处理事件: SegmentText
2025-08-04 12:11:43.800 TB-SegmentQueueReducer   D  TB-Reducer: 📝 追加文本到段[perthink]: ?...
2025-08-04 12:11:43.800 TB-SegmentQueueReducer   D  TB-Reducer: 📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
2025-08-04 12:11:43.848 CNET-PROCESSOR-Stream    D  ✅ [处理结果] 长度变化=12->0
2025-08-04 12:11:43.899 CNET-SERVICE-UnifiedAi   I  ✅ [请求完成] messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, tokens=9, 耗时=674ms
2025-08-04 12:11:43.927 CNET-TOKEN-OUT           I  📦 [518bc8d5-5d70-4e21-a725-dfed760434b3] ThinkingBox ← Batch[1 tokens, 2B, 0ms]
2025-08-04 12:11:43.928 CNET-TOKEN-BATCH         D  📊 批量刷新完成: received=0, output=1
2025-08-04 12:11:44.246 TB-Thinkin...rStateFlow  D  TB-Display: 🔄 [DisplaySession] 状态更新: messageId=518bc8d5-5d70-4e21-a725-dfed760434b3, finalReady=false, thinkingClosed=false
