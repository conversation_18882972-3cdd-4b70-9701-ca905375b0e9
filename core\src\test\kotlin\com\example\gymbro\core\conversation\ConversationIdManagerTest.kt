package com.example.gymbro.core.conversation

import com.example.gymbro.core.util.CompactIdGenerator
import com.example.gymbro.core.util.Constants
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * ConversationIdManager单元测试
 * 
 * 验证ID管理的核心功能：
 * - 消息和会话上下文创建
 * - ID验证和匹配
 * - 智能查找功能
 * - 清理和统计功能
 */
class ConversationIdManagerTest {
    
    private lateinit var conversationIdManager: ConversationIdManager
    private lateinit var mockCompactIdGenerator: CompactIdGenerator
    
    @Before
    fun setup() {
        mockCompactIdGenerator = mockk<CompactIdGenerator>()
        
        // Mock CompactIdGenerator行为
        every { mockCompactIdGenerator.generateCompactId(any<String>()) } returns "ABC123"
        every { mockCompactIdGenerator.getOriginalUuid("ABC123") } returns "test-uuid-123"
        
        conversationIdManager = ConversationIdManager(mockCompactIdGenerator)
    }
    
    @Test
    fun `创建消息上下文应该成功`() {
        // Given
        val sessionId = "test-session-123"
        
        // When
        val messageContext = conversationIdManager.createMessageContext(sessionId)
        
        // Then
        assertNotNull(messageContext)
        assertEquals(sessionId, messageContext.sessionId)
        assertEquals("ABC123", messageContext.compactId)
        assertTrue(Constants.MessageId.isValid(messageContext.messageId))
        assertTrue(messageContext.timestamp > 0)
    }
    
    @Test
    fun `创建会话上下文应该成功`() {
        // Given
        val userId = "test-user-456"
        
        // When
        val sessionContext = conversationIdManager.createSessionContext(userId)
        
        // Then
        assertNotNull(sessionContext)
        assertEquals(userId, sessionContext.userId)
        assertTrue(sessionContext.sessionId.startsWith("session_"))
        assertTrue(sessionContext.messageChain.isEmpty())
        assertTrue(sessionContext.createdAt > 0)
        assertEquals(sessionContext.createdAt, sessionContext.lastActiveAt)
    }
    
    @Test
    fun `验证有效消息ID应该返回Valid`() {
        // Given
        val sessionId = "test-session"
        val messageContext = conversationIdManager.createMessageContext(sessionId)
        
        // When
        val result = conversationIdManager.validateMessageId(messageContext.messageId)
        
        // Then
        assertTrue(result is ConversationIdManager.ValidationResult.Valid)
    }
    
    @Test
    fun `验证无效消息ID应该返回Invalid`() {
        // When
        val result = conversationIdManager.validateMessageId("invalid-id")
        
        // Then
        assertTrue(result is ConversationIdManager.ValidationResult.Invalid)
    }
    
    @Test
    fun `验证空消息ID应该返回Invalid`() {
        // When
        val result = conversationIdManager.validateMessageId("")
        
        // Then
        assertTrue(result is ConversationIdManager.ValidationResult.Invalid)
        assertEquals("消息ID不能为空", (result as ConversationIdManager.ValidationResult.Invalid).reason)
    }
    
    @Test
    fun `验证不存在的消息ID应该返回NotFound`() {
        // Given
        val validButNotExistingId = Constants.MessageId.generate()
        
        // When
        val result = conversationIdManager.validateMessageId(validButNotExistingId)
        
        // Then
        assertTrue(result is ConversationIdManager.ValidationResult.NotFound)
        assertEquals(validButNotExistingId, (result as ConversationIdManager.ValidationResult.NotFound).searchedId)
    }
    
    @Test
    fun `获取存在的消息上下文应该成功`() {
        // Given
        val sessionId = "test-session"
        val messageContext = conversationIdManager.createMessageContext(sessionId)
        
        // When
        val retrieved = conversationIdManager.getMessageContext(messageContext.messageId)
        
        // Then
        assertNotNull(retrieved)
        assertEquals(messageContext.messageId, retrieved!!.messageId)
        assertEquals(messageContext.sessionId, retrieved.sessionId)
    }
    
    @Test
    fun `获取不存在的消息上下文应该返回null`() {
        // When
        val retrieved = conversationIdManager.getMessageContext("non-existing-id")
        
        // Then
        assertNull(retrieved)
    }
    
    @Test
    fun `智能匹配应该支持精确匹配`() {
        // Given
        val sessionId = "test-session"
        val messageContext = conversationIdManager.createMessageContext(sessionId)
        
        // When
        val matched = conversationIdManager.findBestMatchingMessage(messageContext.messageId)
        
        // Then
        assertNotNull(matched)
        assertEquals(messageContext.messageId, matched!!.messageId)
    }
    
    @Test
    fun `智能匹配应该支持压缩ID匹配`() {
        // Given
        val sessionId = "test-session"
        val messageContext = conversationIdManager.createMessageContext(sessionId)
        
        // When
        val matched = conversationIdManager.findBestMatchingMessage("ABC123")
        
        // Then
        assertNotNull(matched)
        assertEquals(messageContext.messageId, matched!!.messageId)
    }
    
    @Test
    fun `智能匹配应该支持前缀匹配`() {
        // Given
        val sessionId = "test-session"
        val messageContext = conversationIdManager.createMessageContext(sessionId)
        val prefix = messageContext.messageId.take(8)
        
        // When
        val matched = conversationIdManager.findBestMatchingMessage(prefix)
        
        // Then
        assertNotNull(matched)
        assertEquals(messageContext.messageId, matched!!.messageId)
    }
    
    @Test
    fun `智能匹配对于空字符串应该返回null`() {
        // When
        val matched = conversationIdManager.findBestMatchingMessage("")
        
        // Then
        assertNull(matched)
    }
    
    @Test
    fun `智能匹配对于不存在的ID应该返回null`() {
        // When
        val matched = conversationIdManager.findBestMatchingMessage("non-existing")
        
        // Then
        assertNull(matched)
    }
    
    @Test
    fun `获取会话消息应该返回正确的消息列表`() {
        // Given
        val sessionId = "test-session"
        val sessionContext = conversationIdManager.createSessionContext("test-user")
        val messageContext1 = conversationIdManager.createMessageContext(sessionContext.sessionId)
        val messageContext2 = conversationIdManager.createMessageContext(sessionContext.sessionId)
        
        // When
        val messages = conversationIdManager.getSessionMessages(sessionContext.sessionId)
        
        // Then
        assertEquals(2, messages.size)
        assertTrue(messages.any { it.messageId == messageContext1.messageId })
        assertTrue(messages.any { it.messageId == messageContext2.messageId })
    }
    
    @Test
    fun `清理过期上下文应该移除过期的消息和会话`() = runTest {
        // Given
        val sessionId = "test-session"
        val messageContext = conversationIdManager.createMessageContext(sessionId)
        
        // 模拟过期（设置很短的过期时间）
        val shortExpireTime = 1L
        
        // When
        Thread.sleep(10) // 确保时间过去
        conversationIdManager.cleanupExpiredContexts(shortExpireTime)
        
        // Then
        val retrieved = conversationIdManager.getMessageContext(messageContext.messageId)
        assertNull(retrieved) // 应该被清理掉
    }
    
    @Test
    fun `获取统计信息应该返回正确的数据`() {
        // Given
        val sessionContext = conversationIdManager.createSessionContext("test-user")
        val messageContext = conversationIdManager.createMessageContext(sessionContext.sessionId)
        
        // When
        val stats = conversationIdManager.getStatistics()
        
        // Then
        assertEquals(1, stats.totalMessages)
        assertEquals(1, stats.totalSessions)
        assertEquals(1, stats.compactIdMappings)
        assertTrue(stats.memoryUsageEstimate > 0)
    }
    
    @Test
    fun `MessageContext应该提供正确的显示ID`() {
        // Given
        val sessionId = "test-session"
        val messageContext = conversationIdManager.createMessageContext(sessionId)
        
        // When
        val displayId = messageContext.getDisplayId()
        
        // Then
        assertEquals("ABC123", displayId)
    }
    
    @Test
    fun `SessionContext应该正确管理消息链`() {
        // Given
        val sessionContext = conversationIdManager.createSessionContext("test-user")
        val messageContext1 = conversationIdManager.createMessageContext(sessionContext.sessionId)
        val messageContext2 = conversationIdManager.createMessageContext(sessionContext.sessionId)
        
        // When
        val updatedSession = conversationIdManager.getSessionContext(sessionContext.sessionId)
        
        // Then
        assertNotNull(updatedSession)
        assertEquals(2, updatedSession!!.messageChain.size)
        assertTrue(updatedSession.messageChain.contains(messageContext1.messageId))
        assertTrue(updatedSession.messageChain.contains(messageContext2.messageId))
    }
}
