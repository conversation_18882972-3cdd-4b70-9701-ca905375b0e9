package com.example.gymbro.features.workout.template.internal.effect

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.core.util.toCompactId
import com.example.gymbro.domain.workout.usecase.template.TemplateManagementUseCase
import com.example.gymbro.features.workout.template.TemplateContract
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Template模块EffectHandler
 *
 * 🎯 职责:
 * - 处理所有副作用 (API调用、数据库操作)
 * - 消费UseCase返回的Flow
 * - 将Domain结果转换为Reducer事件
 * - 处理导航和Toast等Effect
 *
 * 🔄 数据流:
 * Intent -> EffectHandler -> UseCase -> Domain Result -> Reducer Event
 *
 * ✅ 已升级：使用聚合UseCase架构，符合template-todo完善.md要求
 * 🔥 循环防护：添加加载状态控制，防止无限循环加载
 */
class TemplateEffectHandler
@Inject
constructor(
    private val templateManagementUseCase: TemplateManagementUseCase,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    private val logger: Logger,
) {
    // 🔥 添加：循环防护机制
    private var isLoadingTemplates = false
    private var isLoadingDrafts = false

    /**
     * 处理Effect的副作用
     */
    fun handleEffect(
        effect: TemplateContract.Effect,
        currentState: TemplateContract.State,
        effectScope: CoroutineScope,
        dispatch: (TemplateContract.Intent) -> Unit,
        emitEffect: (TemplateContract.Effect) -> Unit,
    ) {
        logger.d("TemplateEffectHandler", "处理Effect: ${effect::class.simpleName}")

        when (effect) {
            // === 数据加载Effect ===
            is TemplateContract.Effect.LoadTemplatesData -> {
                loadTemplates(effectScope, dispatch, forceRefresh = false)
            }
            is TemplateContract.Effect.RefreshTemplatesData -> {
                // 🔥 增强：刷新时强制重新加载，忽略防护机制
                loadTemplates(effectScope, dispatch, forceRefresh = true)
            }

            // === 草稿数据加载Effect ===
            is TemplateContract.Effect.LoadDraftsData,
            is TemplateContract.Effect.RefreshDraftsData,
            -> {
                logger.d("TemplateEffectHandler", "🎯 收到草稿Effect: ${effect::class.simpleName}")
                loadDrafts(effectScope, dispatch)
            }

            // === 删除操作Effect ===
            is TemplateContract.Effect.DeleteTemplateData -> {
                logger.d("TemplateEffectHandler", "🗑️ 处理模板删除Effect: ${effect.templateId}")
                deleteTemplate(effect.templateId, effectScope, dispatch, emitEffect)
            }

            is TemplateContract.Effect.DeleteDraftData -> {
                logger.d("TemplateEffectHandler", "🗑️ 处理草稿删除Effect: ${effect.draftId}")
                deleteDraft(effect.draftId, effectScope, dispatch, emitEffect)
            }

            // === 排序持久化Effect ===
            is TemplateContract.Effect.SaveTemplateOrder -> {
                logger.d("TemplateEffectHandler", "💾 保存模板排序: ${effect.templateOrder.size} 个模板")
                saveTemplateOrder(effect.templateOrder, effectScope, dispatch)
            }

            is TemplateContract.Effect.SaveDraftOrder -> {
                logger.d("TemplateEffectHandler", "💾 保存草稿排序: ${effect.draftOrder.size} 个草稿")
                saveDraftOrder(effect.draftOrder, effectScope, dispatch)
            }

            // === 其他Effect处理 ===
            else -> {
                // 其他Effect暂时不处理
                logger.d("TemplateEffectHandler", "未处理的Effect: ${effect::class.simpleName}")
            }
        }
    }

    /**
     * 🔥 保留原有的Intent处理方法用于兼容性
     */
    fun handleIntent(
        intent: TemplateContract.Intent,
        currentState: TemplateContract.State,
        effectScope: CoroutineScope,
        dispatch: (TemplateContract.Intent) -> Unit,
        emitEffect: (TemplateContract.Effect) -> Unit,
    ) {
        logger.d("TemplateEffectHandler", "处理Intent: ${intent::class.simpleName}")

        when (intent) {
            // === 模板操作 ===
            is TemplateContract.Intent.DeleteTemplate -> {
                deleteTemplate(intent.templateId, effectScope, dispatch, emitEffect)
            }

            is TemplateContract.Intent.DuplicateTemplate -> {
                duplicateTemplate(intent.templateId, effectScope, dispatch, emitEffect)
            }

            is TemplateContract.Intent.ToggleTemplateFavorite -> {
                toggleTemplateFavorite(intent.templateId, effectScope, dispatch, emitEffect)
            }

            is TemplateContract.Intent.StartWorkoutFromTemplate -> {
                emitEffect(TemplateContract.Effect.StartWorkout(intent.templateId))
            }

            // === 草稿操作 - 🔧 修复：添加草稿操作处理 ===
            is TemplateContract.Intent.SaveDraft -> {
                saveDraft(intent.draft, effectScope, dispatch, emitEffect)
            }

            is TemplateContract.Intent.DeleteDraft -> {
                deleteDraft(intent.draftId, effectScope, dispatch, emitEffect)
            }

            is TemplateContract.Intent.PromoteDraft -> {
                promoteDraft(intent.draftId, effectScope, dispatch, emitEffect)
            }

            // === 其他Intent由Reducer直接处理 ===
            else -> {
                // 搜索、筛选、UI状态等由Reducer处理，无需副作用
            }
        }
    }

    /**
     * 加载模板列表
     * ✅ 已更新：使用TemplateManagementUseCase.getTemplates()
     * 🔥 循环防护：避免重复加载导致的无限循环
     * 🎯 增强刷新：支持强制刷新以解决模板保存后不显示的问题
     */
    private fun loadTemplates(
        effectScope: CoroutineScope,
        dispatch: (TemplateContract.Intent) -> Unit,
        forceRefresh: Boolean = false,
    ) {
        // 🔥 循环防护：检查是否正在加载（除非是强制刷新）
        if (isLoadingTemplates && !forceRefresh) {
            logger.d("TemplateEffectHandler", "⚠️ 模板正在加载中，跳过重复请求")
            return
        }

        isLoadingTemplates = true
        logger.d("TemplateEffectHandler", "🔄 开始加载模板 [防护版本] forceRefresh=$forceRefresh")

        templateManagementUseCase
            .getTemplates
            .invoke(Unit)
            .onEach { result ->
                when (result) {
                    is ModernResult.Success -> {
                        val allTemplates = result.data
                        logger.d("TemplateEffectHandler", "✅ 加载到 ${allTemplates.size} 个模板")

                        // 🔥 减少重复调试日志，只记录关键信息
                        allTemplates.take(3).forEach { template ->
                            logger.d("TemplateEffectHandler",
                                "📋 [DEBUG] 模板详情: id=${template.id.toCompactId()}, name=${template.name}, templateState=${template.templateState}")
                        }
                        if (allTemplates.size > 3) {
                            logger.d("TemplateEffectHandler", "📋 [DEBUG] ... 及其他 ${allTemplates.size - 3} 个模板")
                        }

                        // 🔥 严格状态驱动的模板过滤逻辑
                        val publishedTemplates = allTemplates.filter { template ->
                            // 🎯 新逻辑：基于严格状态管理的DTO过滤（template已经是DTO类型）
                            val shouldShow = template.shouldShowInTemplatesTab()

                            // 📝 简化日志：只记录关键信息
                            if (shouldShow) {
                                logger.i("TemplateEffectHandler",
                                    "✅ [SHOW] 模板显示: ${template.id.toCompactId()}(${template.name}) -> ${template.getCurrentTemplateState()}")
                            }

                            shouldShow
                        }

                        logger.d("TemplateEffectHandler", "🎯 筛选出 ${publishedTemplates.size} 个正式模板")

                        // 🎯 publishedTemplates已经是DTO类型，无需转换
                        val templateDtos = publishedTemplates

                        // 🔥 关键修复：先重置加载状态，再dispatch结果
                        isLoadingTemplates = false

                        // 🎯 增强刷新：确保UI能立即看到最新数据
                        logger.d("TemplateEffectHandler", "🔄 派发模板加载结果，模板数量：${templateDtos.size}")
                        dispatch(TemplateContract.Intent.TemplatesLoaded(templateDtos))
                    }

                    is ModernResult.Error -> {
                        logger.e("TemplateEffectHandler", "❌ 加载模板失败: ${result.error}")
                        isLoadingTemplates = false // 🔥 失败时也要重置状态
                        dispatch(
                            TemplateContract.Intent.LoadError(
                                result.error.uiMessage ?: UiText.DynamicString("加载模板失败"),
                            ),
                        )
                    }

                    is ModernResult.Loading -> {
                        // Loading状态可以在UI中显示加载指示器
                        logger.d("TemplateEffectHandler", "🔄 模板加载中...")
                    }
                }
            }.catch { exception ->
                logger.e("TemplateEffectHandler", "❌ 加载模板异常", exception)
                isLoadingTemplates = false // 🔥 异常时也要重置状态
                dispatch(
                    TemplateContract.Intent.LoadError(
                        UiText.DynamicString(exception.message ?: "加载模板失败"),
                    ),
                )
            }.launchIn(effectScope)
    }

    /**
     * 🔧 修复：加载草稿列表
     * ✅ 已更新：使用TemplateManagementUseCase.getTemplates()并筛选草稿
     * 🔥 循环防护：避免重复加载导致的无限循环
     */
    private fun loadDrafts(
        effectScope: CoroutineScope,
        dispatch: (TemplateContract.Intent) -> Unit,
    ) {
        // 🔥 循环防护：检查是否正在加载
        if (isLoadingDrafts) {
            logger.d("TemplateEffectHandler", "⚠️ 草稿正在加载中，跳过重复请求")
            return
        }

        isLoadingDrafts = true
        logger.d("TemplateEffectHandler", "🔄 开始加载草稿列表 [防护版本]")

        try {
            templateManagementUseCase
                .getTemplates
                .invoke(Unit)
                .onEach { result ->
                    when (result) {
                        is ModernResult.Success -> {
                            val allTemplates = result.data
                            logger.d("TemplateEffectHandler", "获取到 ${allTemplates.size} 个模板")

                            // 🔥 严格状态驱动的草稿过滤逻辑
                            val draftTemplates = allTemplates.filter { template ->
                                // 🎯 新逻辑：基于严格状态管理的DTO过滤（template已经是DTO类型）
                                template.shouldShowInDraftsTab()
                            }

                            logger.d("TemplateEffectHandler", "筛选出 ${draftTemplates.size} 个草稿")

                            // 🔥 简化转换：最小化字段，避免复杂逻辑
                            val simpleDrafts =
                                draftTemplates.mapNotNull { dto ->
                                    try {
                                        com.example.gymbro.domain.workout.model.TemplateDraft(
                                            id = dto.id,
                                            name = dto.name,
                                            description = dto.description.takeIf { it.isNotBlank() },
                                            exercises = emptyList(),
                                            source = com.example.gymbro.domain.workout.model.DraftSource.USER_CREATED,
                                            createdAt = kotlinx.datetime.Instant.fromEpochMilliseconds(
                                                dto.createdAt,
                                            ),
                                            updatedAt = kotlinx.datetime.Instant.fromEpochMilliseconds(
                                                dto.updatedAt,
                                            ),
                                            userId = "current_user", // 固定值，避免复杂获取
                                            version = 1,
                                            targetMuscleGroups = emptyList(), // 简化
                                            estimatedDuration = 30, // 固定安全值
                                            difficulty = 3, // 固定中等难度
                                            tags = emptyList(),
                                            notes = null,
                                        )
                                    } catch (e: Exception) {
                                        logger.e("TemplateEffectHandler", "❌ 转换草稿失败: ${dto.name}", e)
                                        null
                                    }
                                }

                            logger.d("TemplateEffectHandler", "成功转换 ${simpleDrafts.size} 个草稿")

                            // 🔥 关键修复：先重置加载状态，再dispatch结果
                            isLoadingDrafts = false
                            dispatch(TemplateContract.Intent.DraftsLoaded(simpleDrafts))
                        }

                        is ModernResult.Error -> {
                            logger.e("TemplateEffectHandler", "❌ UseCase返回错误: ${result.error}")
                            isLoadingDrafts = false // 🔥 失败时也要重置状态
                            dispatch(
                                TemplateContract.Intent.LoadError(
                                    UiText.DynamicString("加载草稿失败: ${result.error}"),
                                ),
                            )
                        }

                        is ModernResult.Loading -> {
                            logger.d("TemplateEffectHandler", "⏳ UseCase加载中...")
                        }
                    }
                }.catch { exception ->
                    logger.e("TemplateEffectHandler", "❌ Flow异常", exception)
                    isLoadingDrafts = false // 🔥 异常时也要重置状态
                    dispatch(
                        TemplateContract.Intent.LoadError(
                            UiText.DynamicString("草稿加载异常: ${exception.message}"),
                        ),
                    )
                }.launchIn(effectScope)
        } catch (e: Exception) {
            logger.e("TemplateEffectHandler", "❌ loadDrafts初始化异常", e)
            isLoadingDrafts = false // 🔥 初始化异常时也要重置状态
            dispatch(
                TemplateContract.Intent.LoadError(
                    UiText.DynamicString("草稿功能初始化失败"),
                ),
            )
        }
    }

    /**
     * 删除模板
     * ✅ 已更新：使用TemplateManagementUseCase.deleteTemplate()并正确处理ModernResult
     */
    private fun deleteTemplate(
        templateId: String,
        effectScope: CoroutineScope,
        dispatch: (TemplateContract.Intent) -> Unit,
        emitEffect: (TemplateContract.Effect) -> Unit,
    ) {
        effectScope.launch(ioDispatcher) {
            try {
                val result = templateManagementUseCase.DeleteTemplate().invoke(templateId)
                when (result) {
                    is ModernResult.Success<*> -> {
                        emitEffect(
                            TemplateContract.Effect.ShowToast(
                                UiText.DynamicString("模板删除成功"),
                            ),
                        )
                        // 🔥 修复：移除重新加载，防止循环加载
                        // dispatch(TemplateContract.Intent.LoadTemplates)
                        logger.d("TemplateEffectHandler", "✅ 模板删除成功，已跳过重新加载防止循环")
                    }

                    is ModernResult.Error -> {
                        emitEffect(
                            TemplateContract.Effect.ShowToast(
                                result.error.uiMessage ?: UiText.DynamicString("删除模板失败"),
                            ),
                        )
                    }

                    is ModernResult.Loading -> {
                        // 删除过程中，暂时不处理
                    }
                }
            } catch (exception: Exception) {
                logger.e("TemplateEffectHandler", "删除模板时出错", exception)
                emitEffect(
                    TemplateContract.Effect.ShowToast(
                        UiText.DynamicString("删除模板失败: ${exception.message}"),
                    ),
                )
            }
        }
    }

    /**
     * 复制模板
     */
    private fun duplicateTemplate(
        templateId: String,
        effectScope: CoroutineScope,
        dispatch: (TemplateContract.Intent) -> Unit,
        emitEffect: (TemplateContract.Effect) -> Unit,
    ) {
        effectScope.launch(ioDispatcher) {
            try {
                val result = templateManagementUseCase.DuplicateTemplate().invoke(templateId)
                when (result) {
                    is ModernResult.Success<*> -> {
                        emitEffect(
                            TemplateContract.Effect.ShowToast(
                                UiText.DynamicString("模板复制成功"),
                            ),
                        )
                        // 🔥 修复：移除重新加载，防止循环加载
                        // dispatch(TemplateContract.Intent.LoadTemplates)
                        logger.d("TemplateEffectHandler", "✅ 模板复制成功，已跳过重新加载防止循环")
                    }

                    is ModernResult.Error -> {
                        emitEffect(
                            TemplateContract.Effect.ShowToast(
                                result.error.uiMessage ?: UiText.DynamicString("复制模板失败"),
                            ),
                        )
                    }

                    is ModernResult.Loading -> {
                        // 复制过程中，暂时不处理
                    }
                }
            } catch (exception: Exception) {
                logger.e("TemplateEffectHandler", "复制模板时出错", exception)
                emitEffect(
                    TemplateContract.Effect.ShowToast(
                        UiText.DynamicString("复制模板时出错"),
                    ),
                )
            }
        }
    }

    /**
     * 切换模板收藏状态
     * ✅ 已更新：使用TemplateManagementUseCase.toggleFavorite()
     */
    private fun toggleTemplateFavorite(
        templateId: String,
        effectScope: CoroutineScope,
        dispatch: (TemplateContract.Intent) -> Unit,
        emitEffect: (TemplateContract.Effect) -> Unit,
    ) {
        effectScope.launch(ioDispatcher) {
            try {
                val result = templateManagementUseCase.ToggleFavorite().invoke(templateId)
                when (result) {
                    is ModernResult.Success -> {
                        emitEffect(
                            TemplateContract.Effect.ShowToast(
                                UiText.DynamicString("收藏状态已更新"),
                            ),
                        )
                        // 🔥 修复：移除重新加载，防止循环加载
                        // dispatch(TemplateContract.Intent.LoadTemplates)
                        logger.d("TemplateEffectHandler", "✅ 收藏状态更新成功，已跳过重新加载防止循环")
                    }

                    is ModernResult.Error -> {
                        emitEffect(
                            TemplateContract.Effect.ShowToast(
                                result.error.uiMessage ?: UiText.DynamicString("更新收藏状态失败"),
                            ),
                        )
                    }

                    is ModernResult.Loading -> {
                        // 更新过程中，暂时不处理
                    }
                }
            } catch (exception: Exception) {
                logger.e("TemplateEffectHandler", "切换收藏状态时出错", exception)
                emitEffect(
                    TemplateContract.Effect.ShowToast(
                        UiText.DynamicString("切换收藏状态失败: ${exception.message}"),
                    ),
                )
            }
        }
    }

    /**
     * 🔧 修复：保存草稿
     * 使用统一的TemplateManagementUseCase进行草稿保存
     */
    private fun saveDraft(
        draft: com.example.gymbro.domain.workout.model.TemplateDraft,
        effectScope: CoroutineScope,
        dispatch: (TemplateContract.Intent) -> Unit,
        emitEffect: (TemplateContract.Effect) -> Unit,
    ) {
        effectScope.launch(ioDispatcher) {
            try {
                // 🔧 关键修复：将草稿转换为模板，使用统一的SaveTemplate UseCase
                val templateToSave = convertDraftToTemplate(draft)
                val result = templateManagementUseCase.SaveTemplate().invoke(templateToSave)

                when (result) {
                    is ModernResult.Success<*> -> {
                        logger.d("TemplateEffectHandler", "✅ 草稿保存成功: ${draft.name}")
                        emitEffect(
                            TemplateContract.Effect.ShowToast(
                                UiText.DynamicString("草稿保存成功"),
                            ),
                        )
                        // 🔥 修复：移除重新加载，防止循环加载
                        // dispatch(TemplateContract.Intent.LoadDrafts)
                        logger.d("TemplateEffectHandler", "✅ 草稿保存成功，已跳过重新加载防止循环")
                    }

                    is ModernResult.Error -> {
                        logger.e("TemplateEffectHandler", "❌ 草稿保存失败: ${result.error}")
                        emitEffect(
                            TemplateContract.Effect.ShowToast(
                                result.error.uiMessage ?: UiText.DynamicString("保存草稿失败"),
                            ),
                        )
                    }

                    is ModernResult.Loading -> {
                        // 保存过程中，暂时不处理
                    }
                }
            } catch (exception: Exception) {
                logger.e("TemplateEffectHandler", "保存草稿时出错", exception)
                emitEffect(
                    TemplateContract.Effect.ShowToast(
                        UiText.DynamicString("保存草稿失败: ${exception.message}"),
                    ),
                )
            }
        }
    }

    /**
     * 🔧 修复：删除草稿
     * 使用统一的TemplateManagementUseCase进行草稿删除
     */
    private fun deleteDraft(
        draftId: String,
        effectScope: CoroutineScope,
        dispatch: (TemplateContract.Intent) -> Unit,
        emitEffect: (TemplateContract.Effect) -> Unit,
    ) {
        effectScope.launch(ioDispatcher) {
            try {
                // 🔧 关键修复：草稿删除使用统一的DeleteTemplate UseCase
                val result = templateManagementUseCase.DeleteTemplate().invoke(draftId)

                when (result) {
                    is ModernResult.Success<*> -> {
                        logger.d("TemplateEffectHandler", "✅ 草稿删除成功: $draftId")
                        emitEffect(
                            TemplateContract.Effect.ShowToast(
                                UiText.DynamicString("草稿删除成功"),
                            ),
                        )
                        // 🔥 修复：移除重新加载，防止循环加载
                        // dispatch(TemplateContract.Intent.LoadDrafts)
                        logger.d("TemplateEffectHandler", "✅ 草稿保存成功，已跳过重新加载防止循环")
                    }

                    is ModernResult.Error -> {
                        logger.e("TemplateEffectHandler", "❌ 草稿删除失败: ${result.error}")
                        emitEffect(
                            TemplateContract.Effect.ShowToast(
                                result.error.uiMessage ?: UiText.DynamicString("删除草稿失败"),
                            ),
                        )
                    }

                    is ModernResult.Loading -> {
                        // 删除过程中，暂时不处理
                    }
                }
            } catch (exception: Exception) {
                logger.e("TemplateEffectHandler", "删除草稿时出错", exception)
                emitEffect(
                    TemplateContract.Effect.ShowToast(
                        UiText.DynamicString("删除草稿失败: ${exception.message}"),
                    ),
                )
            }
        }
    }

    /**
     * 🔧 修复：转正草稿
     * 使用统一的TemplateManagementUseCase，将草稿转换为正式模板
     */
    private fun promoteDraft(
        draftId: String,
        effectScope: CoroutineScope,
        dispatch: (TemplateContract.Intent) -> Unit,
        emitEffect: (TemplateContract.Effect) -> Unit,
    ) {
        effectScope.launch(ioDispatcher) {
            try {
                // 1. 先获取草稿内容
                val getDraftResult = templateManagementUseCase.GetTemplate().invoke(draftId)

                when (getDraftResult) {
                    is ModernResult.Success -> {
                        val draftTemplate = getDraftResult.data
                        if (draftTemplate != null) {
                            // 2. 将草稿转换为正式模板（更新版本控制字段）
                            val templateToPromote =
                                com.example.gymbro.domain.workout.model.template.WorkoutTemplate(
                                    id =
                                    java.util.UUID
                                        .randomUUID()
                                        .toString(),
                                    // 生成新的模板ID
                                    name =
                                    draftTemplate.name
                                        .replace("草稿", "")
                                        .trim()
                                        .takeIf { it.isNotBlank() } ?: "转正模板",
                                    description = draftTemplate.description,
                                    targetMuscleGroups =
                                    draftTemplate.metadata?.targetMuscleGroups
                                        ?: emptyList(),
                                    difficulty =
                                    when (draftTemplate.difficulty) {
                                        com.example.gymbro.shared.models.workout.Difficulty.EASY -> 1
                                        com.example.gymbro.shared.models.workout.Difficulty.MEDIUM -> 2
                                        com.example.gymbro.shared.models.workout.Difficulty.HARD -> 3
                                        com.example.gymbro.shared.models.workout.Difficulty.EXPERT -> 4
                                    },
                                    estimatedDuration = draftTemplate.metadata?.estimatedDuration,
                                    userId = draftTemplate.metadata?.authorId ?: "current_user",
                                    isPublic = false,
                                    isFavorite = false,
                                    tags = draftTemplate.metadata?.tags ?: emptyList(),
                                    exercises = draftTemplate.exercises.mapIndexed { index, exerciseDto ->
                                        com.example.gymbro.domain.workout.model.template.TemplateExercise(
                                            id = exerciseDto.id,
                                            exerciseId = exerciseDto.exerciseId,
                                            name = exerciseDto.exerciseName,
                                            order = index,
                                            sets = exerciseDto.sets,
                                            reps = exerciseDto.reps,
                                            restSeconds = exerciseDto.restTimeSeconds,
                                            weight = exerciseDto.targetWeight,
                                            notes = exerciseDto.notes,
                                            imageUrl = exerciseDto.imageUrl,
                                            videoUrl = exerciseDto.videoUrl,
                                        )
                                    },
                                    createdAt = System.currentTimeMillis(),
                                    updatedAt = System.currentTimeMillis(),
                                    usageCount = 0,
                                    // Phase 1: 版本控制字段 - 转正后为正式发布状态
                                    currentVersion = 1,
                                    isDraft = false,
                                    isPublished = true,
                                    lastPublishedAt = System.currentTimeMillis(),
                                )

                            // 3. 保存新的正式模板
                            val saveResult =
                                templateManagementUseCase.SaveTemplate().invoke(
                                    templateToPromote,
                                )

                            when (saveResult) {
                                is ModernResult.Success<*> -> {
                                    logger.d(
                                        "TemplateEffectHandler",
                                        "✅ 草稿转正成功: ${templateToPromote.name}",
                                    )

                                    // 4. 删除原草稿
                                    templateManagementUseCase.DeleteTemplate().invoke(draftId)

                                    emitEffect(
                                        TemplateContract.Effect.ShowToast(
                                            UiText.DynamicString("草稿已转为正式模板"),
                                        ),
                                    )

                                    // 重新加载模板和草稿列表
                                    // 🔥 修复：移除重新加载，防止循环加载
                                    // dispatch(TemplateContract.Intent.LoadTemplates)
                                    // dispatch(TemplateContract.Intent.LoadDrafts)
                                    logger.d("TemplateEffectHandler", "✅ 草稿转正成功，已跳过重新加载防止循环")
                                }

                                is ModernResult.Error -> {
                                    logger.e("TemplateEffectHandler", "❌ 草稿转正失败: ${saveResult.error}")
                                    emitEffect(
                                        TemplateContract.Effect.ShowToast(
                                            saveResult.error.uiMessage
                                                ?: UiText.DynamicString("草稿转正失败"),
                                        ),
                                    )
                                }

                                is ModernResult.Loading -> {
                                    // 保存过程中
                                }
                            }
                        } else {
                            emitEffect(
                                TemplateContract.Effect.ShowToast(
                                    UiText.DynamicString("草稿不存在"),
                                ),
                            )
                        }
                    }

                    is ModernResult.Error -> {
                        logger.e("TemplateEffectHandler", "❌ 获取草稿内容失败: ${getDraftResult.error}")
                        emitEffect(
                            TemplateContract.Effect.ShowToast(
                                getDraftResult.error.uiMessage ?: UiText.DynamicString("获取草稿内容失败"),
                            ),
                        )
                    }

                    is ModernResult.Loading -> {
                        // 获取过程中
                    }
                }
            } catch (exception: Exception) {
                logger.e("TemplateEffectHandler", "草稿转正时出错", exception)
                emitEffect(
                    TemplateContract.Effect.ShowToast(
                        UiText.DynamicString("草稿转正失败: ${exception.message}"),
                    ),
                )
            }
        }
    }

    /**
     * 🔧 辅助方法：将草稿转换为模板格式
     */
    private fun convertDraftToTemplate(
        draft: com.example.gymbro.domain.workout.model.TemplateDraft,
    ): com.example.gymbro.domain.workout.model.template.WorkoutTemplate =
        com.example.gymbro.domain.workout.model.template.WorkoutTemplate(
            id = draft.id,
            name = draft.name,
            description = draft.description ?: "",
            targetMuscleGroups = draft.targetMuscleGroups,
            difficulty = draft.difficulty,
            estimatedDuration = draft.estimatedDuration?.takeIf { it > 0 },
            userId = draft.userId,
            isPublic = false,
            isFavorite = false,
            tags = draft.tags,
            exercises =
            draft.exercises.mapIndexed { index, exerciseDraft ->
                com.example.gymbro.domain.workout.model.template.TemplateExercise(
                    id =
                    java.util.UUID
                        .randomUUID()
                        .toString(),
                    exerciseId = exerciseDraft.exerciseId,
                    name = exerciseDraft.exerciseName.takeIf { it.isNotBlank() } ?: "未知动作",
                    order = index + 1,
                    sets = exerciseDraft.sets.size,
                    reps = exerciseDraft.sets.firstOrNull()?.reps ?: 10,
                    restSeconds = exerciseDraft.restTimeSeconds ?: 90,
                    weight =
                    exerciseDraft.sets
                        .firstOrNull()
                        ?.weight
                        ?.toFloat(),
                    notes = exerciseDraft.notes,
                )
            },
            createdAt = draft.createdAt.toEpochMilliseconds(),
            updatedAt = draft.updatedAt.toEpochMilliseconds(),
            usageCount = 0,
            // Phase 1: 版本控制字段 - 草稿默认为draft状态
            currentVersion = 1,
            isDraft = true,
            isPublished = false,
            lastPublishedAt = null,
        )

    /**
     * 保存模板排序到数据库
     */
    private fun saveTemplateOrder(
        templateOrder: Map<String, Int>,
        effectScope: CoroutineScope,
        dispatch: (TemplateContract.Intent) -> Unit,
    ) {
        effectScope.launch(ioDispatcher) {
            try {
                logger.d("TemplateEffectHandler", "💾 开始保存模板排序: ${templateOrder.size} 个模板")

                // 🔮 未来功能：实现模板排序保存逻辑
                // 需要调用 TemplateRepository 的排序保存方法
                // 当前先记录日志，后续实现具体的数据库保存逻辑

                templateOrder.forEach { (templateId, order) ->
                    logger.d("TemplateEffectHandler", "模板排序: $templateId -> $order")
                }

                logger.d("TemplateEffectHandler", "✅ 模板排序保存完成")
            } catch (exception: Exception) {
                logger.e("TemplateEffectHandler", "保存模板排序时出错", exception)
            }
        }
    }

    /**
     * 保存草稿排序到数据库
     */
    private fun saveDraftOrder(
        draftOrder: Map<String, Int>,
        effectScope: CoroutineScope,
        dispatch: (TemplateContract.Intent) -> Unit,
    ) {
        effectScope.launch(ioDispatcher) {
            try {
                logger.d("TemplateEffectHandler", "💾 开始保存草稿排序: ${draftOrder.size} 个草稿")

                // 🔮 未来功能：实现草稿排序保存逻辑
                // 需要调用 TemplateRepository 的排序保存方法
                // 当前先记录日志，后续实现具体的数据库保存逻辑

                draftOrder.forEach { (draftId, order) ->
                    logger.d("TemplateEffectHandler", "草稿排序: $draftId -> $order")
                }

                logger.d("TemplateEffectHandler", "✅ 草稿排序保存完成")
            } catch (exception: Exception) {
                logger.e("TemplateEffectHandler", "保存草稿排序时出错", exception)
            }
        }
    }
}

/**
 * 🔥 严格状态管理的Domain->DTO转换方法
 * 核心原则：基于Domain的状态字段，映射到严格的TemplateState枚举
 */
private fun convertDomainToDto(domain: com.example.gymbro.domain.workout.model.template.WorkoutTemplate): com.example.gymbro.shared.models.workout.WorkoutTemplateDto {
    // 🎯 关键逻辑：基于Domain的状态字段计算严格的TemplateState
    val templateState = when {
        // 明确已发布状态
        domain.isPublished == true && domain.isDraft == false ->
            com.example.gymbro.shared.models.workout.TemplateState.PUBLISHED
        // 明确草稿状态
        domain.isDraft == true && domain.isPublished != true ->
            com.example.gymbro.shared.models.workout.TemplateState.DRAFT
        // 兼容性逻辑：根据发布时间判断
        domain.lastPublishedAt != null && domain.lastPublishedAt!! > 0 ->
            com.example.gymbro.shared.models.workout.TemplateState.PUBLISHED
        // 默认情况：草稿状态（符合"创建模版点击后赋予的默认属性=草稿"）
        else -> com.example.gymbro.shared.models.workout.TemplateState.DRAFT
    }

    return com.example.gymbro.shared.models.workout.WorkoutTemplateDto(
        id = domain.id,
        name = domain.name,
        description = domain.description ?: "",
        exercises = emptyList(), // 模板练习转换将在后续阶段处理
        difficulty = when (domain.difficulty ?: 1) {
            1 -> com.example.gymbro.shared.models.workout.Difficulty.EASY
            2 -> com.example.gymbro.shared.models.workout.Difficulty.MEDIUM
            3 -> com.example.gymbro.shared.models.workout.Difficulty.HARD
            4 -> com.example.gymbro.shared.models.workout.Difficulty.EXPERT
            5 -> com.example.gymbro.shared.models.workout.Difficulty.EXPERT
            else -> com.example.gymbro.shared.models.workout.Difficulty.EASY
        },
        category = com.example.gymbro.shared.models.workout.TemplateCategory.STRENGTH,
        source = com.example.gymbro.shared.models.workout.TemplateSource.USER,
        templateState = templateState, // 🔥 核心：使用严格的状态枚举
        currentVersion = domain.currentVersion ?: 1,
        lastPublishedAt = domain.lastPublishedAt ?: 0L,
        createdAt = domain.createdAt,
        updatedAt = domain.updatedAt,
        version = 1,
    )
}
