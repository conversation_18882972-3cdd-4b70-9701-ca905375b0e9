package com.example.gymbro.features.workout.template.edit.di

import com.example.gymbro.features.workout.template.edit.internal.effect.TemplateEditEffectHandler
import com.example.gymbro.features.workout.template.edit.internal.effect.TemplateEditSaveHandler
import com.example.gymbro.features.workout.template.edit.internal.effect.TemplateEditStateManager
import com.example.gymbro.features.workout.template.edit.internal.effect.TemplateEditTextInputHandler
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent
import dagger.hilt.android.scopes.ViewModelScoped

/**
 * TemplateEdit Handler 依赖注入模块
 *
 * 🎯 职责：
 * - 配置Handler类的依赖注入
 * - 管理Handler的生命周期
 * - 确保单例模式正确实现
 *
 * 🔥 重构改进：
 * - 为重构后的Handler类提供DI配置
 * - 使用ViewModelScoped确保与ViewModel生命周期一致
 * - 支持Handler之间的依赖关系
 */
@Module
@InstallIn(ViewModelComponent::class)
object TemplateEditHandlerModule {

    /**
     * 🔥 提供TemplateEditSaveHandler
     * 处理所有保存相关逻辑
     */
    @Provides
    @ViewModelScoped
    fun provideTemplateEditSaveHandler(
        templateTransactionManager:
        com.example.gymbro.features.workout.template.edit.transaction.TemplateTransactionManager,
        autoSaveManager: com.example.gymbro.features.workout.template.cache.TemplateAutoSaveManager,
        resourceProvider: com.example.gymbro.core.resources.ResourceProvider,
        getCurrentUserIdUseCase: com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase,
        templateManagementUseCase:
        com.example.gymbro.domain.workout.usecase.template.TemplateManagementUseCase,
        templatesDataManager:
        com.example.gymbro.domain.workout.usecase.template.TemplatesDataManager,
    ): TemplateEditSaveHandler {
        return TemplateEditSaveHandler(
            templateTransactionManager = templateTransactionManager,
            autoSaveManager = autoSaveManager,
            resourceProvider = resourceProvider,
            getCurrentUserIdUseCase = getCurrentUserIdUseCase,
            templateManagementUseCase = templateManagementUseCase,
            templatesDataManager = templatesDataManager,
        )
    }

    /**
     * 🔥 提供TemplateEditStateManager
     * 处理状态管理和初始化逻辑
     */
    @Provides
    @ViewModelScoped
    fun provideTemplateEditStateManager(
        templateManagementUseCase:
        com.example.gymbro.domain.workout.usecase.template.TemplateManagementUseCase,
        getCurrentUserIdUseCase: com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase,
    ): TemplateEditStateManager {
        return TemplateEditStateManager(
            templateManagementUseCase = templateManagementUseCase,
            getCurrentUserIdUseCase = getCurrentUserIdUseCase,
        )
    }

    /**
     * 🔥 提供TemplateEditTextInputHandler
     * 处理文本输入防抖逻辑
     */
    @Provides
    @ViewModelScoped
    fun provideTemplateEditTextInputHandler(): TemplateEditTextInputHandler {
        return TemplateEditTextInputHandler()
    }

    /**
     * 🔥 提供TemplateEditEffectHandler
     * 处理Effect副作用逻辑
     * 🚨 修复：注入SaveHandler依赖
     */
    @Provides
    @ViewModelScoped
    fun provideTemplateEditEffectHandler(
        saveHandler: TemplateEditSaveHandler,
    ): TemplateEditEffectHandler {
        return TemplateEditEffectHandler(saveHandler)
    }
}
