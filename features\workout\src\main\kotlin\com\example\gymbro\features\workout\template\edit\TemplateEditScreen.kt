package com.example.gymbro.features.workout.template.edit

// 导入UI组件

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.List
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.GymBroSettingsGroup
import com.example.gymbro.designSystem.components.GymBroSettingsItem
import com.example.gymbro.designSystem.components.ToastSeverity
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.features.workout.template.edit.internal.components.EditContent
import com.example.gymbro.features.workout.template.edit.internal.components.LoadingContent
import com.example.gymbro.features.workout.template.edit.internal.components.StatusChip
import com.example.gymbro.features.workout.template.edit.internal.components.TemplateInfoHeader
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import com.example.gymbro.features.workout.template.edit.internal.components.TemplateEditDialogs
import kotlinx.coroutines.flow.collectLatest
import timber.log.Timber

// UI组件已移动到 TemplateEditComponents.kt

/**
 * 模板编辑器Screen - 精简版
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TemplateEditScreen(
    templateId: String? = null,
    onNavigateBack: () -> Unit = {},
    onNavigateToExerciseLibrary: () -> Unit = {},
    onNavigateToPreview: () -> Unit = {},
    viewModel: TemplateEditViewModel = hiltViewModel(),
) {
    val uiState by viewModel.state.collectAsStateWithLifecycle()
    val hapticFeedback = LocalHapticFeedback.current
    val snackbarHostState = remember { SnackbarHostState() }

    // 🔥 调试：监控状态变化和按钮显示逻辑
    LaunchedEffect(uiState.isDraft, uiState.isPublished, uiState.isSaving, uiState.template?.isDraft, uiState.template?.isPublished) {
        val currentState = uiState.getCurrentTemplateState()
        val shouldShowDraftButton = uiState.shouldShowSaveAsDraftButton()
        val buttonText = uiState.getPublishButtonText()
        val canSaveDraft = uiState.canSaveAsDraft()
        val canPublish = uiState.canPublish()

        Timber.tag("TemplateEditScreen").d(
            """
            📊 模板状态监控 - 增强版:
            当前状态: $currentState
            State.isDraft: ${uiState.isDraft}
            State.isPublished: ${uiState.isPublished}
            Template.isDraft: ${uiState.template?.isDraft}
            Template.isPublished: ${uiState.template?.isPublished}
            isSaving: ${uiState.isSaving}
            模板ID: ${uiState.template?.id}

            UI控制逻辑:
            显示保存草稿按钮: $shouldShowDraftButton
            发布按钮文本: $buttonText
            可以保存草稿: $canSaveDraft
            可以发布: $canPublish

            状态描述: ${uiState.getStatusDescription()}
            """.trimIndent(),
        )
    }

    // 🔥 左下角提示状态管理
    var toastMessage by remember { mutableStateOf("") }
    var showToast by remember { mutableStateOf(false) }
    var toastSeverity by remember { mutableStateOf(ToastSeverity.INFO) }

    // 处理副作用
    LaunchedEffect(viewModel) {
        viewModel.effect.collectLatest { effect ->
            when (effect) {
                is TemplateEditContract.Effect.NavigateBack -> onNavigateBack()
                is TemplateEditContract.Effect.NavigateBackInternal -> {
                    // 新增：处理安全的导航返回（保存后）
                    onNavigateBack()
                }
                is TemplateEditContract.Effect.NavigateToPreview -> onNavigateToPreview()
                is TemplateEditContract.Effect.NavigateToExerciseLibrary -> onNavigateToExerciseLibrary()
                is TemplateEditContract.Effect.TriggerHapticFeedback -> {
                    hapticFeedback.performHapticFeedback(effect.type)
                }

                // === 用户反馈Effect处理 - 统一使用左下角小窗口 ===
                is TemplateEditContract.Effect.ShowToast -> {
                    toastMessage = when (val message = effect.message) {
                        is UiText.DynamicString -> message.value
                        is UiText.StringResource -> "操作成功" // 简化处理
                        else -> "操作完成"
                    }
                    toastSeverity = ToastSeverity.SUCCESS
                    showToast = true
                }

                is TemplateEditContract.Effect.ShowError -> {
                    toastMessage = when (val error = effect.message) {
                        is UiText.DynamicString -> error.value
                        is UiText.StringResource -> "操作失败" // 简化处理
                        else -> "发生错误"
                    }
                    toastSeverity = ToastSeverity.ERROR
                    showToast = true
                }

                is TemplateEditContract.Effect.ShowVersionCreated -> {
                    toastMessage = "版本创建成功"
                    toastSeverity = ToastSeverity.SUCCESS
                    showToast = true
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                }

                is TemplateEditContract.Effect.ShowDraftSaved -> {
                    val currentState = uiState.getCurrentTemplateState()
                    toastMessage = when (currentState) {
                        TemplateEditContract.TemplateState.DRAFT -> "草稿已保存"
                        TemplateEditContract.TemplateState.PUBLISHED -> "模板已保存为草稿版本" // 理论上不会出现
                    }
                    toastSeverity = ToastSeverity.SUCCESS
                    showToast = true
                }

                is TemplateEditContract.Effect.ShowTemplatePublished -> {
                    val currentState = uiState.getCurrentTemplateState()
                    toastMessage = when (currentState) {
                        TemplateEditContract.TemplateState.DRAFT -> "草稿已发布为模板"
                        TemplateEditContract.TemplateState.PUBLISHED -> "模板已更新发布"
                    }
                    toastSeverity = ToastSeverity.SUCCESS
                    showToast = true
                }

                is TemplateEditContract.Effect.ShowVersionRestored -> {
                    toastMessage = "版本恢复成功"
                    toastSeverity = ToastSeverity.SUCCESS
                    showToast = true
                }

                else -> { /* 其他副作用处理 */ }
            }
        }
    }

    // 初始化加载
    LaunchedEffect(templateId) {
        if (templateId != null) {
            viewModel.dispatch(TemplateEditContract.Intent.LoadTemplate(templateId))
        }
    }

    // 🔥 修复：监听从动作库返回的选中动作
    // 使用 Lifecycle 监听屏幕重新获得焦点
    val lifecycleOwner = LocalLifecycleOwner.current
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                // 当屏幕重新获得焦点时（从动作库返回），检查选中的动作
                val selectedExercises = com.example.gymbro.features.workout.navigation.ExerciseSelectionHolder.getSelectedExercises()
                if (selectedExercises != null && selectedExercises.isNotEmpty()) {
                    Timber.d("🔧 [TemplateEditScreen] 检测到从动作库返回的 ${selectedExercises.size} 个动作")
                    selectedExercises.forEachIndexed { index, exercise ->
                        Timber.d(
                            "🔧 [TemplateEditScreen] 动作${index + 1}: id=${exercise.id}, name=${exercise.name}",
                        )
                    }

                    // 批量添加选中的动作
                    viewModel.dispatch(TemplateEditContract.Intent.AddExercises(selectedExercises))
                }
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    // 🔥 修复UI重组风暴：使用remember稳定回调函数
    val stableOnSaveAsDraft = remember {
        {
            Timber.d("🔥 草稿保存按钮被点击")
            println("🔥 [DEBUG] Screen层：草稿保存按钮被点击")
            viewModel.dispatch(TemplateEditContract.Intent.SaveAsDraft)
        }
    }

    val stableOnPublishTemplate = remember {
        {
            Timber.d("🔥 保存为模板按钮被点击")
            println("🔥 [DEBUG] Screen层：发布模板按钮被点击")
            viewModel.dispatch(TemplateEditContract.Intent.PublishTemplate)
        }
    }

    val stableOnNavigateBack = remember {
        {
            Timber.d("🚀 [DEBUG] TopBar返回按钮被点击，只触发NavigateBack")
            println("🚀 [DEBUG] TopBar返回按钮被点击，只触发NavigateBack")
            viewModel.dispatch(TemplateEditContract.Intent.NavigateBack)
        }
    }

    val stableOnShowVersionHistory = remember {
        {
            viewModel.dispatch(TemplateEditContract.Intent.ShowVersionHistory)
        }
    }

    // 🔥 Box+LazyColumn+Surface架构 - 遵循designSystem迁移指南
    Box(
        modifier = Modifier.fillMaxSize(),
    ) {
        // === BackgroundLayer (背景层) ===
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = MaterialTheme.workoutColors.cardBackground,
        ) {
            // === ContentBox (内容容器) ===
            Box(
                modifier = Modifier.fillMaxSize(),
            ) {
                // === 主要内容区域 ===
                Column(
                    modifier = Modifier.fillMaxSize(),
                ) {
                    // TopBar作为LazyColumn的第一个item
                    SimplifiedTemplateEditTopBar(
                        uiState = uiState,
                        onNavigateBack = stableOnNavigateBack,
                        onIntent = viewModel::dispatch,
                    )

                    // 🔥 P4: 增强的统一信息头部组件（包含完整Summary UI）
                    TemplateInfoHeader(
                        templateName = uiState.templateName,
                        templateDescription = uiState.templateDescription,
                        totalWeight = uiState.totalWeight,
                        workoutSummary = uiState.workoutSummary,
                        onEditName = {
                            viewModel.dispatch(TemplateEditContract.Intent.ShowTemplateNameDialog)
                        },
                        onEditDescription = {
                            viewModel.dispatch(TemplateEditContract.Intent.ShowTemplateDescriptionDialog)
                        },
                        // P4: 新增Summary UI参数
                        exerciseCount = uiState.exercises.size,
                        estimatedDuration = uiState.estimatedDuration,
                        createdAt = uiState.template?.createdAt ?: 0L,
                        updatedAt = uiState.template?.updatedAt ?: 0L,
                        isDraft = uiState.template?.isDraft ?: true,
                        isPublished = uiState.template?.isPublished ?: false,
                        currentVersion = uiState.currentVersion,
                        templateId = uiState.template?.id ?: "",
                        modifier = Modifier.padding(horizontal = Tokens.Spacing.Medium),
                    )

                    // 主要内容区域
                    when {
                        uiState.isLoading -> {
                            LoadingContent()
                        }
                        else -> {
                            EditContent(
                                uiState = uiState,
                                onIntent = viewModel::dispatch,
                            )
                        }
                    }
                }
            }
        }

        // === OverlayBox (浮动层) ===
        // 🔥 右下角小型提示窗口系统

        // 提示消息 - 右下角小窗口（统一处理成功、错误、信息）
        if (showToast && toastMessage.isNotEmpty()) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(
                        end = Tokens.Spacing.Medium,
                        bottom = Tokens.Spacing.Massive + Tokens.Spacing.XXLarge, // 避免与FAB重叠，在FAB上方显示
                    ),
            ) {
                CompactToast(
                    message = toastMessage,
                    severity = toastSeverity,
                    onDismiss = {
                        showToast = false
                        toastMessage = ""
                    },
                )
            }
        }

        // 通用错误显示 - 右下角小窗口（用于uiState.error）
        uiState.error?.let { error ->
            if (!showToast) { // 避免与上面的Toast重叠
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(
                            end = Tokens.Spacing.Medium,
                            bottom = Tokens.Spacing.Massive + Tokens.Spacing.XXLarge, // 避免与FAB重叠
                        ),
                ) {
                    CompactToast(
                        message = when (error) {
                            is UiText.DynamicString -> error.value
                            is UiText.StringResource -> "操作失败"
                            else -> "发生错误"
                        },
                        severity = ToastSeverity.ERROR,
                        onDismiss = {
                            viewModel.dispatch(TemplateEditContract.Intent.ClearError)
                        },
                    )
                }
            }
        }

        // FloatingActionButton - 右下角浮动按钮
        FloatingActionButton(
            onClick = {
                viewModel.dispatch(TemplateEditContract.Intent.ShowExerciseSelector)
            },
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(Tokens.Spacing.Medium),
            containerColor = MaterialTheme.workoutColors.accentPrimary,
            contentColor = MaterialTheme.workoutColors.textPrimary, // 🔥 Phase 0: 使用 workoutColors 替代 colorScheme
        ) {
            Icon(
                imageVector = Icons.Default.Add,
                contentDescription = "添加动作",
            )
        }

        // 🔥 Profile模式对话框
        TemplateEditDialogs(
            state = uiState,
            onIntent = viewModel::dispatch,
        )

        // 版本历史对话框 (Phase1新增)
        if (uiState.showVersionHistory) {
            VersionHistoryDialog(
                versionHistory = uiState.versionHistory,
                currentVersion = uiState.currentVersion,
                isRestoringVersion = uiState.isRestoringVersion,
                onRestoreVersion = { versionId ->
                    viewModel.dispatch(TemplateEditContract.Intent.RestoreFromVersion(versionId))
                },
                onDismiss = {
                    viewModel.dispatch(TemplateEditContract.Intent.HideVersionHistory)
                },
            )
        }

        // 🔥 移除：Keypad 管理已移至 WorkoutExerciseComponent 内部
        // 组件级别的完全自洽性：每个 WorkoutExerciseComponent 内部处理自己的 Keypad 逻辑
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SimplifiedTemplateEditTopBar(
    uiState: TemplateEditContract.State,
    onNavigateBack: () -> Unit,
    onIntent: (TemplateEditContract.Intent) -> Unit,
) {
    Timber.tag("WK-DEBUG").d("🔥 SimplifiedTemplateEditTopBar正在渲染")
    TopAppBar(
        title = {
            Text(
                text = "编辑模板",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.workoutColors.accentPrimary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
        },
        navigationIcon = {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "返回",
                    tint = MaterialTheme.workoutColors.accentPrimary,
                )
            }
        },
        actions = {
            // 🔥 增强版：使用状态枚举的按钮显示逻辑
            val currentState = uiState.getCurrentTemplateState()
            val shouldShowSaveAsDraft = uiState.shouldShowSaveAsDraftButton()
            val canSaveDraft = uiState.canSaveAsDraft()
            val canPublish = uiState.canPublish()

            // 草稿保存按钮（根据状态显示）
            if (shouldShowSaveAsDraft) {
                TextButton(
                    onClick = {
                        if (canSaveDraft) {
                            onIntent(TemplateEditContract.Intent.SaveAsDraft)
                        } else {
                            // 🔥 状态转换验证失败的处理
                            Timber.w("🚫 无法保存草稿：当前状态为 $currentState")
                        }
                    },
                    enabled = !uiState.isSaving && canSaveDraft,
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = MaterialTheme.workoutColors.accentPrimary,
                    ),
                ) {
                    Text(text = "保存草稿")
                }
            }

            // 发布/更新按钮（始终显示，文本根据状态变化）
            Button(
                onClick = {
                    if (canPublish) {
                        // 🔥 统一使用PublishTemplate Intent，内部根据状态处理
                        onIntent(TemplateEditContract.Intent.PublishTemplate)
                    } else {
                        // 🔥 状态转换验证失败的处理（理论上不会出现，因为所有状态都可以发布）
                        Timber.w("🚫 无法发布：当前状态为 $currentState")
                    }
                },
                enabled = !uiState.isSaving && canPublish,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.workoutColors.accentPrimary,
                    contentColor = MaterialTheme.workoutColors.textPrimary,
                ),
                modifier = Modifier.padding(end = Tokens.Spacing.Small),
            ) {
                if (uiState.isSaving) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(Tokens.Icon.Small),
                        strokeWidth = Tokens.Size.IndicatorSmall,
                        color = MaterialTheme.workoutColors.textPrimary,
                    )
                } else {
                    Text(
                        text = uiState.getPublishButtonText(), // 🔥 基于状态枚举的文本
                    )
                }
            }
        },
        colors =
        TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.workoutColors.cardBackground,
            titleContentColor = MaterialTheme.workoutColors.accentPrimary,
            navigationIconContentColor = MaterialTheme.workoutColors.accentPrimary,
            actionIconContentColor = MaterialTheme.workoutColors.accentPrimary,
        ),
    )
}

// === 版本控制UI组件 - 从TemplateEditVersionUI.kt迁移 ===

/**
 * 版本状态指示器 - 显示模板状态（移除版本号显示）
 * 集成designSystem标准，提供清晰的状态展示
 */
@Composable
fun VersionStatusIndicator(
    currentVersion: Int,
    isDraft: Boolean,
    isPublished: Boolean,
    hasUnsavedChanges: Boolean,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
    ) {
        // 状态指示器（移除版本号显示）
        when {
            hasUnsavedChanges -> {
                StatusChip(
                    text = "未保存",
                    color = MaterialTheme.workoutColors.accentPrimary,
                    backgroundColor = MaterialTheme.workoutColors.cardBackground,
                )
            }
            isDraft -> {
                StatusChip(
                    text = "草稿",
                    color = MaterialTheme.workoutColors.accentPrimary,
                    backgroundColor = MaterialTheme.workoutColors.cardBackground,
                )
            }
            isPublished -> {
                StatusChip(
                    text = "已发布",
                    color = MaterialTheme.workoutColors.accentPrimary,
                    backgroundColor = MaterialTheme.workoutColors.cardBackground,
                )
            }
        }
    }
}

/**
 * 版本历史对话框 - 显示模板的版本历史
 * 支持版本查看和恢复操作
 */
@Composable
fun VersionHistoryDialog(
    versionHistory: List<com.example.gymbro.domain.workout.model.template.TemplateVersion>,
    currentVersion: Int,
    isRestoringVersion: Boolean,
    onRestoreVersion: (String) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    AlertDialog(
        modifier = modifier,
        onDismissRequest = onDismiss,
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
            ) {
                Icon(
                    imageVector = Icons.Default.History,
                    contentDescription = null,
                    tint = MaterialTheme.workoutColors.accentPrimary,
                )
                Text(
                    text = "版本历史",
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.workoutColors.textPrimary,
                )
            }
        },
        text = {
            if (versionHistory.isEmpty()) {
                VersionHistoryEmptyState()
            } else {
                VersionHistoryList(
                    versionHistory = versionHistory,
                    currentVersion = currentVersion,
                    isRestoringVersion = isRestoringVersion,
                    onRestoreVersion = onRestoreVersion,
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = onDismiss,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = MaterialTheme.workoutColors.accentPrimary,
                ),
            ) {
                Text("关闭")
            }
        },
        containerColor = MaterialTheme.workoutColors.cardBackground,
    )
}

/**
 * 版本历史空状态组件
 * 当没有版本历史时显示的占位内容
 */
@Composable
private fun VersionHistoryEmptyState(
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(Tokens.Spacing.Large),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
    ) {
        Icon(
            imageVector = Icons.Default.History,
            contentDescription = null,
            modifier = Modifier.size(Tokens.Icon.XXLarge),
            tint = MaterialTheme.workoutColors.textSecondary,
        )
        Text(
            text = "暂无版本历史",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.workoutColors.textSecondary,
            textAlign = TextAlign.Center,
        )
        Text(
            text = "发布模板后将开始记录版本历史",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.workoutColors.textTertiary,
            textAlign = TextAlign.Center,
        )
    }
}

/**
 * 版本历史列表组件
 * 显示所有历史版本的可滚动列表
 */
@Composable
private fun VersionHistoryList(
    versionHistory: List<com.example.gymbro.domain.workout.model.template.TemplateVersion>,
    currentVersion: Int,
    isRestoringVersion: Boolean,
    onRestoreVersion: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    LazyColumn(
        modifier = modifier
            .fillMaxWidth()
            .heightIn(max = Tokens.Card.HeightLarge * 2),
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
    ) {
        items(
            items = versionHistory,
            key = { it.id },
        ) { version ->
            VersionHistoryItem(
                version = version,
                isCurrentVersion = version.versionNumber == currentVersion,
                isRestoring = isRestoringVersion,
                onRestore = { onRestoreVersion(version.id) },
            )
        }
    }
}

/**
 * 版本历史项 - 单个版本显示组件
 * 展示版本详细信息，支持版本恢复操作
 */
@Composable
fun VersionHistoryItem(
    version: com.example.gymbro.domain.workout.model.template.TemplateVersion,
    isCurrentVersion: Boolean,
    isRestoring: Boolean,
    onRestore: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (isCurrentVersion) {
                MaterialTheme.workoutColors.cardBackground
            } else {
                MaterialTheme.workoutColors.cardBackground
            },
        ),
        border = if (isCurrentVersion) {
            androidx.compose.foundation.BorderStroke(
                width = Tokens.Size.IndicatorSmall,
                color = MaterialTheme.workoutColors.accentPrimary,
            )
        } else {
            null
        },
        elevation = CardDefaults.cardElevation(defaultElevation = Tokens.Elevation.XSmall),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
        ) {
            // 版本头部信息
            VersionItemHeader(
                version = version,
                isCurrentVersion = isCurrentVersion,
                isRestoring = isRestoring,
                onRestore = onRestore,
            )

            // 创建时间
            Text(
                text = formatVersionTime(version.createdAt.toEpochMilli()),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.workoutColors.textSecondary,
            )

            // 版本描述
            version.description?.let { description ->
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.workoutColors.textPrimary,
                )
            }
        }
    }
}

/**
 * 版本项头部组件
 * 显示版本号、状态和操作按钮
 */
@Composable
private fun VersionItemHeader(
    version: com.example.gymbro.domain.workout.model.template.TemplateVersion,
    isCurrentVersion: Boolean,
    isRestoring: Boolean,
    onRestore: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
        ) {
            if (isCurrentVersion) {
                StatusChip(
                    text = "当前版本",
                    color = MaterialTheme.workoutColors.accentPrimary,
                    backgroundColor = MaterialTheme.workoutColors.cardBackground,
                )
            } else {
                StatusChip(
                    text = "历史版本",
                    color = MaterialTheme.workoutColors.textSecondary,
                    backgroundColor = MaterialTheme.workoutColors.cardBackground,
                )
            }
        }

        if (!isCurrentVersion) {
            TextButton(
                onClick = onRestore,
                enabled = !isRestoring,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = MaterialTheme.workoutColors.accentPrimary,
                ),
            ) {
                if (isRestoring) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(Tokens.Icon.Small),
                        strokeWidth = Tokens.Size.IndicatorSmall,
                        color = MaterialTheme.workoutColors.accentPrimary,
                    )
                } else {
                    Text("恢复")
                }
            }
        }
    }
}

/**
 * 格式化版本时间显示
 */
private fun formatVersionTime(timestamp: Long): String {
    return try {
        val date = java.util.Date(timestamp)
        val formatter = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault())
        formatter.format(date)
    } catch (e: Exception) {
        "时间未知"
    }
}

/**
 * 模板基础信息区域组件
 * 参考 Profile 模块的 PersonalInfoScreen.kt 实现模式
 * 使用 GymBroSettingsGroup + GymBroSettingsItem 展示可编辑项
 */
@Composable
private fun TemplateBasicInfoSection(
    template: WorkoutTemplate?,
    onIntent: (TemplateEditContract.Intent) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = Tokens.Spacing.Medium)
            .padding(top = Tokens.Spacing.Medium),
    ) {
        // 基本信息组
        GymBroSettingsGroup {
            // 模板名称 - 可点击编辑
            GymBroSettingsItem(
                icon = Icons.Default.Edit,
                title = UiText.DynamicString("模板名称"),
                subtitle = UiText.DynamicString(
                    template?.name?.takeIf { it.isNotBlank() } ?: "未设置",
                ),
                onClick = {
                    onIntent(TemplateEditContract.Intent.ShowTemplateNameDialog)
                },
            )

            // 模板描述 - 可点击编辑
            GymBroSettingsItem(
                icon = Icons.Default.Description,
                title = UiText.DynamicString("模板描述"),
                subtitle = UiText.DynamicString(
                    template?.description?.takeIf { it.isNotBlank() } ?: "未设置",
                ),
                onClick = {
                    onIntent(TemplateEditContract.Intent.ShowTemplateDescriptionDialog)
                },
            )
        }

        // 训练进度信息组
        template?.let {
            TrainingProgressInfoGroup(template = it)
        }
    }
}

/**
 * 训练进度信息组件
 * 显示总重量、总组数、当前进度状态
 *
 * 根据 Template_数据流详细文档.md 的数据结构定义：
 * - Domain 层 WorkoutTemplate 使用基础字段：sets: Int, reps: Int, weight: Float?
 * - customSets 数据序列化在 notes 字段中，需要解析获取详细数据
 * - 统计计算基于基础字段进行，确保与现有数据结构兼容
 */
@Composable
private fun TrainingProgressInfoGroup(
    template: WorkoutTemplate,
    modifier: Modifier = Modifier,
) {
    // 使用 Domain 层 WorkoutTemplate 的基础字段进行统计计算
    // 根据文档：Domain 层使用 sets: Int, reps: Int, weight: Float? 基础字段
    val totalVolume = template.exercises.sumOf { exercise ->
        val weight = exercise.weight ?: 0f
        val reps = exercise.reps.toDouble()
        val sets = exercise.sets.toDouble()
        weight.toDouble() * reps * sets
    }.toFloat()

    val totalSets = template.exercises.sumOf { exercise ->
        exercise.sets
    }

    val totalExercises = template.exercises.size

    Column(
        modifier = modifier.padding(top = Tokens.Spacing.Medium),
    ) {
        // 标题
        Text(
            text = "训练统计",
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.workoutColors.textPrimary,
            modifier = Modifier.padding(
                horizontal = Tokens.Spacing.Medium,
                vertical = Tokens.Spacing.Small,
            ),
        )

        // 统计信息组
        GymBroSettingsGroup {
            // 总重量
            GymBroSettingsItem(
                icon = Icons.Default.FitnessCenter,
                title = UiText.DynamicString("总重量"),
                subtitle = UiText.DynamicString("${"%.1f".format(totalVolume)} kg"),
                onClick = { /* 只读信息，不可点击 */ },
                showArrow = false,
            )

            // 动作和组数
            GymBroSettingsItem(
                icon = Icons.AutoMirrored.Filled.List,
                title = UiText.DynamicString("训练内容"),
                subtitle = UiText.DynamicString("$totalExercises 个动作 • $totalSets 组"),
                onClick = { /* 只读信息，不可点击 */ },
                showArrow = false,
            )
        }
    }
}

/**
 * 紧凑型Toast组件 - 右下角小型提示窗口
 *
 * 🎯 特点：
 * - 小尺寸设计，不占用过多空间
 * - 3秒自动消失
 * - 修正颜色配置
 * - 流畅的动画效果
 */
@Composable
private fun CompactToast(
    message: String,
    severity: ToastSeverity,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    var isVisible by remember { mutableStateOf(false) }

    // 自动消失逻辑
    LaunchedEffect(message) {
        isVisible = true
        kotlinx.coroutines.delay(3000L) // 固定3秒消失
        isVisible = false
        onDismiss()
    }

    // 颜色配置修正
    val (backgroundColor, textColor, iconColor) = when (severity) {
        ToastSeverity.SUCCESS -> Triple(
            MaterialTheme.workoutColors.completedState, // 使用workout主题的成功色
            MaterialTheme.workoutColors.textPrimary,
            MaterialTheme.workoutColors.textPrimary,
        )
        ToastSeverity.ERROR -> Triple(
            MaterialTheme.workoutColors.errorPrimary, // 使用workout主题的错误色
            MaterialTheme.workoutColors.textPrimary,
            MaterialTheme.workoutColors.textPrimary,
        )
        ToastSeverity.WARNING -> Triple(
            Tokens.Color.Warning, // 使用Token中的警告色
            MaterialTheme.workoutColors.textPrimary,
            MaterialTheme.workoutColors.textPrimary,
        )
        ToastSeverity.INFO -> Triple(
            MaterialTheme.workoutColors.accentPrimary, // 使用workout主题的强调色
            MaterialTheme.workoutColors.textPrimary,
            MaterialTheme.workoutColors.textPrimary,
        )
    }

    AnimatedVisibility(
        visible = isVisible,
        enter = slideInVertically(
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            initialOffsetY = { it / 2 },
        ) + fadeIn(animationSpec = tween(300)),
        exit = slideOutVertically(
            animationSpec = tween(300, easing = FastOutSlowInEasing),
            targetOffsetY = { it / 2 },
        ) + fadeOut(animationSpec = tween(300)),
        modifier = modifier,
    ) {
        Surface(
            color = backgroundColor,
            shape = RoundedCornerShape(Tokens.Radius.Large),
            shadowElevation = Tokens.Elevation.Medium,
            modifier = Modifier.wrapContentSize(),
        ) {
            Row(
                modifier = Modifier.padding(
                    horizontal = Tokens.Spacing.Small,
                    vertical = Tokens.Spacing.Small,
                ),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
            ) {
                // 图标
                Icon(
                    imageVector = severity.icon,
                    contentDescription = null,
                    tint = iconColor,
                    modifier = Modifier.size(Tokens.Icon.Small),
                )

                // 消息文本
                Text(
                    text = message,
                    style = MaterialTheme.typography.labelMedium,
                    color = textColor,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.widthIn(max = Tokens.Card.HeightMin + Tokens.Spacing.XXLarge), // 限制最大宽度保持紧凑
                )
            }
        }
    }
}
