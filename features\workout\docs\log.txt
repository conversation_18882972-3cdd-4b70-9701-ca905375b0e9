18:47:03.796 WK-TEMPLATE-UI           I  🔄 [强制刷新] OnResume触发，强制刷新当前Tab数据
18:47:03.798 WK-TEMPLATE-UI           I  🔧 [DEBUG] 当前Tab: TEMPLATES, 强制刷新所有Tab数据
18:47:03.816 WK-TEMPLATE-UI           I  📋 [TAB] 当前在模板Tab，重复刷新确保同步
18:47:05.599 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-PROCESS] 训练模版 - 调用立即保存处理器
18:47:05.603 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-VALIDATE] 训练模版 - 验证保存参数和状态
18:47:05.604 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-CANCEL] 训练模版 - 取消待处理的自动保存
18:47:05.774 WK-TEMPLAT...OSS-MODULE  E  ❌ 跨模块通知检查异常
                                         androidx.compose.runtime.LeftCompositionCancellationException: The coroutine scope left the composition
18:47:05.804 TemplateEditSaveHandler  I  🔍 [TEMPLATE-SAVE] 数据库中未找到有效模板数据，判定为新建
18:47:05.808 WK-TEMPLATE-CORE         I  🔥 [SAVE-OPERATION] 完整保存信息:
                                           参数: isDraft=true, isPublishing=false
                                           操作类型: CREATE
                                           模板ID: ad288446-eeb0-4818-a7ea-b4202c3ec091
                                           模板状态: isDraft=true, isPublished=false
                                           模板名称: 训练模版
                                           动作数量: 0
18:47:05.810 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-DETERMINE] 训练模版 - 操作类型: CREATE
18:47:05.814 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-CREATE-MODE] 训练模版 - 新建模板操作
18:47:05.814 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-NAME-FINAL] 训练模版 - 确定最终模板名称
18:47:05.814 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-BUILD] 训练模版 - 构建Domain模型
18:47:05.823 WK-TEMPLATE-CORE         I  🔥 [MAPPER-START] 训练模版 (0动作)
18:47:05.829 WK-TEMPLATE-DATA         I  🔥 [P0-ID-PROCESS] 模板ID处理JSON:
                                           原始ID: ad288446-eeb0-4818-a7ea-b4202c3ec091
                                           最终ID: ad288446-eeb0-4818-a7ea-b4202c3ec091
                                           操作类型: 保持原ID
18:47:05.851 WK-TEMPLATE-CORE         I  🔥 [TEMPLATE-STATUS] 构建完成的模板状态JSON:
                                           最终状态: isDraft=true, isPublished=false
                                           模板信息: id=ad288446-eeb0-4818-a7ea-b4202c3ec091, name=训练模版
                                           用户ID: 0cff5ba5-b11e-4d09-9766-cc494f3ba88a
                                           动作数量: 0
                                           版本信息: currentVersion=1
18:47:05.851 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-EXECUTE] 训练模版 - 执行保存操作
18:47:05.855 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-TRANSACTION] 训练模版 - 开始事务保存
18:47:05.891 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-TX-START] 训练模版 - 开始事务保存 - ID: tx_1754218025859_6200
18:47:05.893 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-TX-VALIDATE] 训练模版 - 开始预验证阶段
18:47:05.900 WK-TEMPLATE-CORE         E  ❌ [WK-SAVE-TX-VALIDATE-FAILED] 训练模版 - ERROR: 预验证失败
18:47:05.903 WK-TEMPLATE-CORE         E  ❌ [WK-SAVE-TRANSACTION-FAILED] 训练模版 - ERROR: 模板必须包含至少一个动作
18:47:05.903 WK-TEMPLATE-CORE         E  ❌ [WK-SAVE-FAILED] 训练模版 - ERROR: DynamicString(value=保存失败: 模板必须包含至少一个动作)
18:47:05.903 WK-TEMPLATE-CORE         E  🔥 保存失败回调: DynamicString(value=保存失败: 模板必须包含至少一个动作)
18:47:05.910 TemplateEd...ectHandler  E  ❌ 显示错误: 保存失败: 模板必须包含至少一个动作
18:47:06.081 .example.gymbro          I  Background concurrent mark compact GC freed 21MB AllocSpace bytes, 5(172KB) LOS objects, 19% free, 98MB/122MB, paused 3.899ms,14.782ms total 752.127ms
18:47:08.441 Choreographer            I  Skipped 66 frames!  The application may be doing too much work on its main thread.
18:47:08.599 HWUI                     I  Davey! duration=1196ms; Flags=0, FrameTimelineVsyncId=572055, IntendedVsync=14205917510004, Vsync=14207017509960, InputEventId=0, HandleInputStart=14207031721700, AnimationStart=14207031778000, PerformTraversalsStart=14207070272600, DrawStart=14207070410700, FrameDeadline=14207150843288, FrameStartTime=14207027706200, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=14207087090700, SyncStart=14207131785100, IssueDrawCommandsStart=14207132227900, SwapBuffers=14207136159000, FrameCompleted=14207158604000, DequeueBufferDuration=7805400, QueueBufferDuration=2592300, GpuCompleted=14207158604000, SwapBuffersCompleted=14207158451600, DisplayPresentTime=0, CommandSubmissionCompleted=14207136159000,
18:47:10.680 Choreographer            I  Skipped 34 frames!  The application may be doing too much work on its main thread.
18:47:10.766 HWUI                     I  Davey! duration=780ms; Flags=0, FrameTimelineVsyncId=572971, IntendedVsync=14208550843232, Vsync=14208634176562, InputEventId=0, HandleInputStart=14208642966200, AnimationStart=14208643000200, PerformTraversalsStart=14208680338500, DrawStart=14208680420100, FrameDeadline=14208667509894, FrameStartTime=14208642947900, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=14209258879500, SyncStart=14209261241400, IssueDrawCommandsStart=14209261845400, SwapBuffers=14209325468700, FrameCompleted=14209334096300, DequeueBufferDuration=61100, QueueBufferDuration=582500, GpuCompleted=14209334096300, SwapBuffersCompleted=14209327287000, DisplayPresentTime=0, CommandSubmissionCompleted=14209325468700,
18:47:12.407 Choreographer            I  Skipped 36 frames!  The application may be doing too much work on its main thread.
18:47:16.235 System.out               I  🔧 [DEBUG] KeypadInputField(重量) 被点击: exerciseId=6, setIndex=2
18:47:17.212 InsetsController         D  hide(ime(), fromIme=false)
18:47:17.212 ImeTracker               I  com.example.gymbro:8a2f8867: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
18:47:18.528 WindowOnBackDispatcher   W  sendCancelIfRunning: isInProgress=false callback=androidx.compose.material3.ModalBottomSheetDialogLayout$Api34Impl$createBackCallback$1@2008635
18:47:18.934 InsetsController         D  hide(ime(), fromIme=false)
18:47:18.934 ImeTracker               I  com.example.gymbro:4627088e: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
18:47:19.585 WK-TEMPLATE-SAVE         I  🔥 [PUBLISH] handlePublishTemplate 被调用，模板='训练模版'
18:47:19.586 WK-TEMPLATE-SAVE         I  🔥 [PUBLISH] 状态详情: templateDescription='', currentUserId='0cff5ba5-b11e-4d09-9766-cc494f3ba88a'
18:47:19.586 WK-TEMPLATE-SAVE         I  🔥 [PUBLISH] 动作数=2
18:47:19.598 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-START] 训练模版 - 发布模板
18:47:19.599 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-PROCESS] 训练模版 - 调用发布处理器
18:47:19.599 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-VALIDATE] 训练模版 - 验证保存参数和状态
18:47:19.599 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-CANCEL] 训练模版 - 取消待处理的自动保存
18:47:19.691 TemplateEditSaveHandler  I  🔍 [TEMPLATE-SAVE] 数据库中未找到有效模板数据，判定为新建
18:47:19.691 WK-TEMPLATE-CORE         I  🔥 [SAVE-OPERATION] 完整保存信息:
                                           参数: isDraft=false, isPublishing=true
                                           操作类型: CREATE
                                           模板ID: ad288446-eeb0-4818-a7ea-b4202c3ec091
                                           模板状态: isDraft=true, isPublished=false
                                           模板名称: 训练模版
                                           动作数量: 2
18:47:19.692 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-DETERMINE] 训练模版 - 操作类型: CREATE
18:47:19.693 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-CREATE-MODE] 训练模版 - 新建模板操作
18:47:19.693 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-NAME-FINAL] 训练模版 - 确定最终模板名称
18:47:19.693 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-BUILD] 训练模版 - 构建Domain模型
18:47:19.693 WK-TEMPLATE-CORE         I  🔥 [MAPPER-START] 训练模版 (2动作)
18:47:19.694 WK-TEMPLATE-DATA         I  🔥 [P0-ID-PROCESS] 模板ID处理JSON:
                                           原始ID: ad288446-eeb0-4818-a7ea-b4202c3ec091
                                           最终ID: ad288446-eeb0-4818-a7ea-b4202c3ec091
                                           操作类型: 保持原ID
18:47:19.694 WK-TEMPLATE-DATA         I  🔥 [P0-IMAGE-DATA] 动作1: imageUrl=https://example.com/default_exercise.jpg, videoUrl=https://example.com/default_exercise.mp4
18:47:19.694 WK-TEMPLATE-DATA         I  🔥 [P0-IMAGE-DATA] 动作2: imageUrl=https://example.com/default_exercise.jpg, videoUrl=https://example.com/default_exercise.mp4
18:47:19.819 WK-TEMPLATE-CORE         I  🔥 [TEMPLATE-STATUS] 构建完成的模板状态JSON:
                                           最终状态: isDraft=false, isPublished=true
                                           模板信息: id=ad288446-eeb0-4818-a7ea-b4202c3ec091, name=训练模版
                                           用户ID: 0cff5ba5-b11e-4d09-9766-cc494f3ba88a
                                           动作数量: 2
                                           版本信息: currentVersion=1
                                           动作概况:
                                             1. 窄距杠铃卧推 (3组)
                                             2. 哑铃飞鸟 (3组)
18:47:19.820 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-EXECUTE] 训练模版 - 执行保存操作
18:47:19.820 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-TRANSACTION] 训练模版 - 开始事务保存
18:47:19.828 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-TX-START] 训练模版 - 开始事务保存 - ID: tx_1754218039823_9881
18:47:19.828 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-TX-VALIDATE] 训练模版 - 开始预验证阶段
18:47:19.828 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-TX-VALIDATE-OK] 训练模版 - 预验证通过
18:47:19.828 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-TX-PREPARE] 训练模版 - 数据准备阶段
18:47:19.828 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-TX-JSON-CHECK] 训练模版 - JSON兼容性验证
18:47:19.889 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-TX-FC-CHECK] 训练模版 - Function Call兼容性验证
18:47:19.973 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-TX-FC-OK] 训练模版 - Function Call兼容性验证通过
18:47:19.974 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-TX-ATOMIC] 训练模版 - 执行原子性保存
18:47:19.989 WK-TEMPLATE-SAVE         I  🔥 [SAVE-START] 保存模板: 训练模版, isDraft=false, 动作数量=2
18:47:20.176 WK-TEMPLATE-SAVE         I  ✅ 保存成功: 训练模版 8, JSON长度: 946
18:47:20.177 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-TX-SUCCESS] 训练模版 - 事务保存成功 - 模板ID: ad288446-eeb0-4818-a7ea-b4202c3ec091
18:47:20.327 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-COMPLETE] 训练模版 - 保存成功 - ID: ad288446-eeb0-4818-a7ea-b4202c3ec091
18:47:20.328 WK-TEMPLATE-AUTOSAVE     I  🔥 [P3-AUTOSAVE] 手动保存完成，设置冷却期: 5000ms
18:47:20.328 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-NOTIFY] 训练模版 - 通知自动保存管理器
18:47:20.329 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-DATA-REFRESH] 训练模版 - 通知数据管理器刷新
18:47:20.477 System.out               I  🔍 UseCase.GetTemplates: 查询用户ID = 0cff5ba5-b11e-4d09-9766-cc494f3ba88a
18:47:20.479 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-DATA-REFRESH-SUCCESS] 训练模版 - 数据管理器通知成功
18:47:20.479 WK-TEMPLATE-CORE         I  🔥 [WK-SAVE-SUCCESS] 训练模版 - 模板发布成功 - ID: ad288446-eeb0-4818-a7ea-b4202c3ec091
18:47:20.479 WK-TEMPLATE-CORE         I  🔥 保存成功回调: templateId=ad288446-eeb0-4818-a7ea-b4202c3ec091
18:47:20.535 WK-TEMPLATE-CORE         I  🔥 [PHASE1-NEW] mapDtoToState开始 - 单向映射JSON:
                                           模板名称: 训练模版
                                           动作数量: 2
                                           模板ID: ad288446-eeb0-4818-a7ea-b4202c3ec091
                                           版本信息: isDraft=null, isPublished=null
18:47:20.559 CROSS-MODULE             I  🔄 模板保存成功，通知主界面刷新: templateId=E54F9K, isDraft=false
18:47:20.566 REFRESH_TRIGGER          I  REFRESH_NEEDED:E54F9K:false:1754218040559
18:47:24.519 System.out               I  🚀 [DEBUG] TopBar返回按钮被点击，只触发NavigateBack
18:47:25.013 WK-TEMPLATE-UI           I  🔄 [强制刷新] OnResume触发，强制刷新当前Tab数据
18:47:25.014 WK-TEMPLATE-UI           I  🔧 [DEBUG] 当前Tab: TEMPLATES, 强制刷新所有Tab数据
18:47:25.029 WK-TEMPLATE-UI           I  📋 [TAB] 当前在模板Tab，重复刷新确保同步
