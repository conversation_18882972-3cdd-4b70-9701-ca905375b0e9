package com.example.gymbro.core.monitoring

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.datetime.Clock
import timber.log.Timber

/**
 * 🔥 【PLAN B 重构】性能监控扩展函数
 *
 * 提供便捷的性能监控方法，简化在各个模块中的集成
 */

/**
 * 为 Flow 添加性能监控
 *
 * @param monitor 性能监控器
 * @param sessionId 会话ID
 * @param stage 阶段名称
 * @param metadata 附加元数据
 */
fun <T> Flow<T>.withPerformanceTracking(
    monitor: ArchitecturePerformanceMonitor,
    sessionId: String,
    stage: String,
    metadata: Map<String, Any> = emptyMap()
): Flow<T> {
    return this
        .onStart {
            monitor.startTracking(sessionId, stage, metadata)
        }
        .onCompletion { exception ->
            monitor.endTracking(
                sessionId = sessionId,
                success = exception == null,
                errorMessage = exception?.message
            )
        }
}

/**
 * 测量代码块执行时间并记录到性能监控器
 *
 * @param monitor 性能监控器
 * @param sessionId 会话ID
 * @param stage 阶段名称
 * @param metadata 附加元数据
 * @param block 要执行的代码块
 */
suspend inline fun <T> measurePerformance(
    monitor: ArchitecturePerformanceMonitor,
    sessionId: String,
    stage: String,
    metadata: Map<String, Any> = emptyMap(),
    crossinline block: suspend () -> T
): T {
    val startTime = Clock.System.now().toEpochMilliseconds()

    return try {
        val result = block()
        val endTime = Clock.System.now().toEpochMilliseconds()
        val latency = endTime - startTime

        monitor.recordStageMetrics(sessionId, stage, latency, metadata)
        result
    } catch (e: Exception) {
        val endTime = Clock.System.now().toEpochMilliseconds()
        val latency = endTime - startTime

        monitor.recordStageMetrics(
            sessionId = sessionId,
            stage = stage,
            latencyMs = latency,
            metadata = metadata + mapOf("error" to (e.message ?: "Unknown error"))
        )
        throw e
    }
}

/**
 * 性能监控常量
 */
object PerformanceStages {
    // Coach 模块阶段
    const val COACH_REQUEST_PROCESSING = "coach-request-processing"
    const val COACH_EFFECT_HANDLING = "coach-effect-handling"
    const val COACH_STATE_REDUCTION = "coach-state-reduction"

    // Core-Network 模块阶段
    const val CORE_NETWORK_AI_REQUEST = "core-network-ai-request"
    const val CORE_NETWORK_SSE_PARSING = "core-network-sse-parsing"
    const val CORE_NETWORK_TOKEN_PROCESSING = "core-network-token-processing"
    const val CORE_NETWORK_OUTPUT_CHANNEL = "core-network-output-channel"

    // ThinkingBox 模块阶段
    const val THINKINGBOX_SUBSCRIPTION = "thinkingbox-subscription"
    const val THINKINGBOX_PARSING = "thinkingbox-parsing"
    const val THINKINGBOX_RENDERING = "thinkingbox-rendering"
    const val THINKINGBOX_UI_UPDATE = "thinkingbox-ui-update"

    // 端到端阶段
    const val E2E_FULL_PIPELINE = "e2e-full-pipeline"
    const val E2E_COACH_TO_CORE = "e2e-coach-to-core"
    const val E2E_CORE_TO_THINKINGBOX = "e2e-core-to-thinkingbox"
}

/**
 * 性能监控日志标签
 */
object PerformanceLogTags {
    const val MONITOR = "ArchPerformanceMonitor"
    const val COACH_PERF = "CoachPerformance"
    const val CORE_NETWORK_PERF = "CoreNetworkPerformance"
    const val THINKINGBOX_PERF = "ThinkingBoxPerformance"
    const val E2E_PERF = "E2EPerformance"
}

/**
 * 创建性能监控元数据的便捷函数
 */
fun createPerformanceMetadata(
    messageId: String? = null,
    sessionId: String? = null,
    userInput: String? = null,
    tokenCount: Int? = null,
    contentLength: Int? = null,
    additionalData: Map<String, Any> = emptyMap()
): Map<String, Any> {
    val metadata = mutableMapOf<String, Any>()

    messageId?.let { metadata["messageId"] = it }
    sessionId?.let { metadata["sessionId"] = it }
    userInput?.let { metadata["userInputLength"] = it.length }
    tokenCount?.let { metadata["tokenCount"] = it }
    contentLength?.let { metadata["contentLength"] = it }

    metadata.putAll(additionalData)

    return metadata
}

/**
 * 性能监控器的便捷访问扩展
 */
fun ArchitecturePerformanceMonitor.trackCoachRequest(
    sessionId: String,
    messageId: String,
    userInput: String
) {
    startTracking(
        sessionId = messageId,
        stage = PerformanceStages.COACH_REQUEST_PROCESSING,
        metadata = createPerformanceMetadata(
            messageId = messageId,
            sessionId = sessionId,
            userInput = userInput
        )
    )
}

fun ArchitecturePerformanceMonitor.trackCoreNetworkProcessing(
    messageId: String,
    requestSize: Int
) {
    startTracking(
        sessionId = messageId,
        stage = PerformanceStages.CORE_NETWORK_AI_REQUEST,
        metadata = createPerformanceMetadata(
            messageId = messageId,
            contentLength = requestSize
        )
    )
}

fun ArchitecturePerformanceMonitor.trackThinkingBoxRendering(
    messageId: String,
    tokenCount: Int
) {
    startTracking(
        sessionId = messageId,
        stage = PerformanceStages.THINKINGBOX_RENDERING,
        metadata = createPerformanceMetadata(
            messageId = messageId,
            tokenCount = tokenCount
        )
    )
}

/**
 * 性能基准测试辅助函数
 */
object PerformanceBenchmark {

    /**
     * 运行性能基准测试
     *
     * @param monitor 性能监控器
     * @param testName 测试名称
     * @param iterations 迭代次数
     * @param warmupIterations 预热迭代次数
     * @param testBlock 测试代码块
     */
    suspend fun runBenchmark(
        monitor: ArchitecturePerformanceMonitor,
        testName: String,
        iterations: Int = 100,
        warmupIterations: Int = 10,
        testBlock: suspend (iteration: Int) -> Unit
    ): BenchmarkResult {
        Timber.tag(PerformanceLogTags.MONITOR).i("🏁 开始性能基准测试: $testName")

        // 预热
        repeat(warmupIterations) { iteration ->
            testBlock(iteration)
        }

        // 正式测试
        val startTime = Clock.System.now().toEpochMilliseconds()
        val latencies = mutableListOf<Long>()

        repeat(iterations) { iteration ->
            val iterationStart = Clock.System.now().toEpochMilliseconds()
            testBlock(iteration)
            val iterationEnd = Clock.System.now().toEpochMilliseconds()
            latencies.add(iterationEnd - iterationStart)
        }

        val endTime = Clock.System.now().toEpochMilliseconds()
        val totalTime = endTime - startTime

        val sortedLatencies = latencies.sorted()
        val result = BenchmarkResult(
            testName = testName,
            iterations = iterations,
            totalTimeMs = totalTime,
            averageLatencyMs = latencies.average(),
            medianLatencyMs = sortedLatencies[iterations / 2].toDouble(),
            p95LatencyMs = sortedLatencies[(iterations * 0.95).toInt()].toDouble(),
            p99LatencyMs = sortedLatencies[(iterations * 0.99).toInt()].toDouble(),
            minLatencyMs = sortedLatencies.first().toDouble(),
            maxLatencyMs = sortedLatencies.last().toDouble(),
            throughputPerSecond = (iterations.toDouble() / totalTime.toDouble()) * 1000.0
        )

        Timber.tag(PerformanceLogTags.MONITOR).i("🏆 基准测试完成: $testName, 平均延迟: ${result.averageLatencyMs}ms")

        return result
    }

    /**
     * 基准测试结果
     */
    data class BenchmarkResult(
        val testName: String,
        val iterations: Int,
        val totalTimeMs: Long,
        val averageLatencyMs: Double,
        val medianLatencyMs: Double,
        val p95LatencyMs: Double,
        val p99LatencyMs: Double,
        val minLatencyMs: Double,
        val maxLatencyMs: Double,
        val throughputPerSecond: Double
    ) {
        fun toLogString(): String {
            return """
                |基准测试结果: $testName
                |迭代次数: $iterations
                |总时间: ${totalTimeMs}ms
                |平均延迟: ${"%.2f".format(averageLatencyMs)}ms
                |中位数延迟: ${"%.2f".format(medianLatencyMs)}ms
                |P95延迟: ${"%.2f".format(p95LatencyMs)}ms
                |P99延迟: ${"%.2f".format(p99LatencyMs)}ms
                |最小延迟: ${"%.2f".format(minLatencyMs)}ms
                |最大延迟: ${"%.2f".format(maxLatencyMs)}ms
                |吞吐量: ${"%.2f".format(throughputPerSecond)} req/s
            """.trimMargin()
        }
    }
}
