package com.example.gymbro.core.conversation

import com.example.gymbro.core.util.CompactIdGenerator
import com.example.gymbro.core.util.Constants
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * Plan B架构重构集成测试
 * 
 * 验证ID统一化方案的完整性：
 * - ConversationIdManager的核心功能
 * - ID传递的正确性和一致性
 * - 智能匹配和容错机制
 * - 性能和内存管理
 * - 多轮对话支持
 */
class PlanBIntegrationTest {
    
    private lateinit var conversationIdManager: ConversationIdManager
    private lateinit var mockCompactIdGenerator: CompactIdGenerator
    
    @Before
    fun setup() {
        mockCompactIdGenerator = mockk<CompactIdGenerator>()
        
        // Mock CompactIdGenerator行为
        every { mockCompactIdGenerator.generateCompactId(any<String>()) } answers {
            val uuid = firstArg<String>()
            "C${uuid.take(5).uppercase()}" // 生成类似 "CABCD1" 的压缩ID
        }
        
        every { mockCompactIdGenerator.getOriginalUuid(any<String>()) } answers {
            val compactId = firstArg<String>()
            if (compactId.startsWith("C") && compactId.length == 6) {
                "test-uuid-${compactId.drop(1).lowercase()}" // 反向解析
            } else null
        }
        
        conversationIdManager = ConversationIdManager(mockCompactIdGenerator)
    }
    
    @Test
    fun `Plan B架构 - 完整的ID传递流程测试`() = runTest {
        // === 阶段1: 创建会话和消息上下文 ===
        val userId = "test-user-123"
        val sessionContext = conversationIdManager.createSessionContext(userId)
        
        assertNotNull(sessionContext)
        assertEquals(userId, sessionContext.userId)
        assertTrue(sessionContext.sessionId.startsWith("session_"))
        
        // === 阶段2: 创建多个消息上下文 ===
        val userMessageContext = conversationIdManager.createMessageContext(sessionContext.sessionId)
        val aiMessageContext = conversationIdManager.createMessageContext(sessionContext.sessionId)
        
        // 验证消息上下文
        assertNotNull(userMessageContext)
        assertNotNull(aiMessageContext)
        assertEquals(sessionContext.sessionId, userMessageContext.sessionId)
        assertEquals(sessionContext.sessionId, aiMessageContext.sessionId)
        
        // 验证压缩ID生成
        assertTrue(userMessageContext.compactId.startsWith("C"))
        assertTrue(aiMessageContext.compactId.startsWith("C"))
        assertEquals(6, userMessageContext.compactId.length)
        assertEquals(6, aiMessageContext.compactId.length)
        
        // === 阶段3: 验证ID验证功能 ===
        val userValidation = conversationIdManager.validateMessageId(userMessageContext.messageId)
        val aiValidation = conversationIdManager.validateMessageId(aiMessageContext.messageId)
        
        assertTrue(userValidation is ConversationIdManager.ValidationResult.Valid)
        assertTrue(aiValidation is ConversationIdManager.ValidationResult.Valid)
        
        // === 阶段4: 验证智能匹配功能 ===
        
        // 4.1 精确匹配
        val exactMatch = conversationIdManager.findBestMatchingMessage(userMessageContext.messageId)
        assertNotNull(exactMatch)
        assertEquals(userMessageContext.messageId, exactMatch!!.messageId)
        
        // 4.2 压缩ID匹配
        val compactMatch = conversationIdManager.findBestMatchingMessage(userMessageContext.compactId)
        assertNotNull(compactMatch)
        assertEquals(userMessageContext.messageId, compactMatch!!.messageId)
        
        // 4.3 前缀匹配
        val prefix = userMessageContext.messageId.take(8)
        val prefixMatch = conversationIdManager.findBestMatchingMessage(prefix)
        assertNotNull(prefixMatch)
        assertEquals(userMessageContext.messageId, prefixMatch!!.messageId)
        
        // === 阶段5: 验证会话管理功能 ===
        val sessionMessages = conversationIdManager.getSessionMessages(sessionContext.sessionId)
        assertEquals(2, sessionMessages.size)
        assertTrue(sessionMessages.any { it.messageId == userMessageContext.messageId })
        assertTrue(sessionMessages.any { it.messageId == aiMessageContext.messageId })
        
        // === 阶段6: 验证统计功能 ===
        val stats = conversationIdManager.getStatistics()
        assertEquals(2, stats.totalMessages)
        assertEquals(1, stats.totalSessions)
        assertEquals(2, stats.compactIdMappings)
        assertTrue(stats.memoryUsageEstimate > 0)
    }
    
    @Test
    fun `Plan B架构 - 多轮对话场景测试`() = runTest {
        // === 创建多轮对话场景 ===
        val userId = "multi-turn-user"
        val sessionContext = conversationIdManager.createSessionContext(userId)
        
        val messageContexts = mutableListOf<ConversationIdManager.MessageContext>()
        
        // 模拟5轮对话（用户消息 + AI回复）
        repeat(5) { round ->
            val userMessage = conversationIdManager.createMessageContext(sessionContext.sessionId)
            val aiMessage = conversationIdManager.createMessageContext(sessionContext.sessionId)
            
            messageContexts.add(userMessage)
            messageContexts.add(aiMessage)
        }
        
        // === 验证会话消息链 ===
        val sessionMessages = conversationIdManager.getSessionMessages(sessionContext.sessionId)
        assertEquals(10, sessionMessages.size) // 5轮 * 2消息/轮
        
        // 验证消息顺序（应该按创建时间排序）
        for (i in 1 until sessionMessages.size) {
            assertTrue(sessionMessages[i].timestamp >= sessionMessages[i-1].timestamp)
        }
        
        // === 验证会话上下文更新 ===
        val updatedSession = conversationIdManager.getSessionContext(sessionContext.sessionId)
        assertNotNull(updatedSession)
        assertEquals(10, updatedSession!!.messageChain.size)
        assertTrue(updatedSession.lastActiveAt >= updatedSession.createdAt)
    }
    
    @Test
    fun `Plan B架构 - 容错机制测试`() = runTest {
        // === 测试无效ID处理 ===
        val invalidIdResult = conversationIdManager.validateMessageId("invalid-id")
        assertTrue(invalidIdResult is ConversationIdManager.ValidationResult.Invalid)
        
        val emptyIdResult = conversationIdManager.validateMessageId("")
        assertTrue(emptyIdResult is ConversationIdManager.ValidationResult.Invalid)
        
        // === 测试不存在的ID ===
        val validButNotExistingId = Constants.MessageId.generate()
        val notFoundResult = conversationIdManager.validateMessageId(validButNotExistingId)
        assertTrue(notFoundResult is ConversationIdManager.ValidationResult.NotFound)
        
        // === 测试智能匹配的容错 ===
        val noMatch1 = conversationIdManager.findBestMatchingMessage("")
        assertNull(noMatch1)
        
        val noMatch2 = conversationIdManager.findBestMatchingMessage("non-existing-id")
        assertNull(noMatch2)
        
        val noMatch3 = conversationIdManager.findBestMatchingMessage("short") // 太短的前缀
        assertNull(noMatch3)
    }
    
    @Test
    fun `Plan B架构 - 性能和内存管理测试`() = runTest {
        // === 创建大量消息测试性能 ===
        val userId = "performance-test-user"
        val sessionContext = conversationIdManager.createSessionContext(userId)
        
        val startTime = System.currentTimeMillis()
        val messageContexts = mutableListOf<ConversationIdManager.MessageContext>()
        
        // 创建100个消息上下文
        repeat(100) {
            val messageContext = conversationIdManager.createMessageContext(sessionContext.sessionId)
            messageContexts.add(messageContext)
        }
        
        val creationTime = System.currentTimeMillis() - startTime
        assertTrue("创建100个消息上下文应该在1秒内完成", creationTime < 1000)
        
        // === 验证批量查询性能 ===
        val queryStartTime = System.currentTimeMillis()
        
        messageContexts.forEach { context ->
            val retrieved = conversationIdManager.getMessageContext(context.messageId)
            assertNotNull(retrieved)
            assertEquals(context.messageId, retrieved!!.messageId)
        }
        
        val queryTime = System.currentTimeMillis() - queryStartTime
        assertTrue("查询100个消息上下文应该在500ms内完成", queryTime < 500)
        
        // === 验证内存使用估算 ===
        val stats = conversationIdManager.getStatistics()
        assertEquals(100, stats.totalMessages)
        assertEquals(1, stats.totalSessions)
        assertTrue("内存使用估算应该合理", stats.memoryUsageEstimate > 0 && stats.memoryUsageEstimate < 100000)
    }
    
    @Test
    fun `Plan B架构 - 清理功能测试`() = runTest {
        // === 创建测试数据 ===
        val userId = "cleanup-test-user"
        val sessionContext = conversationIdManager.createSessionContext(userId)
        val messageContext = conversationIdManager.createMessageContext(sessionContext.sessionId)
        
        // 验证数据存在
        assertNotNull(conversationIdManager.getMessageContext(messageContext.messageId))
        assertNotNull(conversationIdManager.getSessionContext(sessionContext.sessionId))
        
        // === 执行清理（使用很短的过期时间） ===
        Thread.sleep(10) // 确保时间过去
        conversationIdManager.cleanupExpiredContexts(1L) // 1ms过期时间
        
        // === 验证清理结果 ===
        val retrievedMessage = conversationIdManager.getMessageContext(messageContext.messageId)
        assertNull("过期消息应该被清理", retrievedMessage)
        
        val stats = conversationIdManager.getStatistics()
        assertEquals("清理后消息数量应该为0", 0, stats.totalMessages)
    }
    
    @Test
    fun `Plan B架构 - 扩展函数测试`() {
        // === 测试扩展函数 ===
        val userId = "extension-test-user"
        val sessionContext = conversationIdManager.createSessionContextWithLogging(userId)
        val messageContext = conversationIdManager.createMessageContextWithLogging(sessionContext.sessionId)
        
        // 测试安全获取方法
        val safeMessage = conversationIdManager.getMessageContextSafely(messageContext.messageId)
        assertNotNull(safeMessage)
        assertEquals(messageContext.messageId, safeMessage!!.messageId)
        
        val safeSession = conversationIdManager.getSessionContextSafely(sessionContext.sessionId)
        assertNotNull(safeSession)
        assertEquals(sessionContext.sessionId, safeSession!!.sessionId)
        
        // 测试验证方法
        val (isValid, message) = conversationIdManager.validateMessageIdWithMessage(messageContext.messageId)
        assertTrue(isValid)
        assertEquals("消息ID有效", message)
        
        // 测试智能查找
        val smartFound = conversationIdManager.findMessageSmart(messageContext.compactId)
        assertNotNull(smartFound)
        assertEquals(messageContext.messageId, smartFound!!.messageId)
        
        // 测试会话统计方法
        val messageCount = conversationIdManager.getSessionMessageCount(sessionContext.sessionId)
        assertEquals(1, messageCount)
        
        val isEmpty = conversationIdManager.isSessionEmpty(sessionContext.sessionId)
        assertFalse(isEmpty)
        
        val lastActiveTime = conversationIdManager.getSessionLastActiveTime(sessionContext.sessionId)
        assertNotNull(lastActiveTime)
        assertTrue(lastActiveTime!! > 0)
        
        // 测试消息归属检查
        val belongsToSession = conversationIdManager.isMessageInSession(messageContext.messageId, sessionContext.sessionId)
        assertTrue(belongsToSession)
        
        // 测试统计字符串
        val statsString = conversationIdManager.getStatsString()
        assertTrue(statsString.contains("messages=1"))
        assertTrue(statsString.contains("sessions=1"))
    }
}
