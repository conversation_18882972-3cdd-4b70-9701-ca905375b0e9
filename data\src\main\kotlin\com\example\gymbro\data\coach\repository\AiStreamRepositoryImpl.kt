package com.example.gymbro.data.coach.repository

import com.example.gymbro.core.ai.prompt.builder.CoreChatMessage
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.GymBroLogTags
import com.example.gymbro.data.coach.service.AiRequestSender
// 🧹 REMOVED: import com.example.gymbro.data.coach.service.AiResponseReceiver (架构简化)
import com.example.gymbro.domain.coach.model.AiTaskType
import com.example.gymbro.domain.coach.model.StreamEvent
import com.example.gymbro.domain.coach.repository.AiStreamRepository
import com.example.gymbro.domain.coach.repository.TaskCapabilities
import com.example.gymbro.shared.models.ai.ChatRequest
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 【架构重构】AI流式仓库实现 - 直接调用Core-Network
 *
 * 新职责（简化后）：
 * - 请求构建（通过AiRequestSender）
 * - 直接委托给Core-Network统一处理
 * - 消除中间层，建立唯一数据链路
 *
 * 🚫 移除的职责：
 * - ❌ 已移除AiResponseReceiver中间层 (架构简化)
 * - ❌ 消除重复的请求发送逻辑
 */
@Singleton
class AiStreamRepositoryImpl @Inject constructor(
    private val aiRequestSender: AiRequestSender,
    private val unifiedAiResponseService: com.example.gymbro.core.network.service.UnifiedAiResponseService,
) : AiStreamRepository {

    override suspend fun insertThinking(
        sessionId: String,
        prompt: String,
    ): ModernResult<String> = aiRequestSender.insertThinking(sessionId, prompt)

    /**
     * 接收已构建的消息列表，避免重复prompt构建
     */
    override suspend fun streamAiResponse(
        sessionId: String,
        messageId: String, // 🔥 【ID统一】使用统一的messageId
        messages: List<CoreChatMessage>,
        taskType: AiTaskType,
    ): Flow<StreamEvent> = kotlinx.coroutines.flow.flow {
        Timber.d("🔄 [PLAN B重构] 开始流式AI响应 - sessionId=$sessionId, messageId=$messageId")

        try {
            // 发送首包 Thinking 事件
            emit(
                StreamEvent.Thinking(
                    sessionId = sessionId,
                    messageId = messageId,
                    timestamp = System.currentTimeMillis(),
                )
            )
            Timber.d("✅ 发送首包Thinking事件: messageId=$messageId")

            // 构建 ChatRequest
            val chatRequest = com.example.gymbro.shared.models.ai.ChatRequest(
                model = "deepseek-chat",
                messages = messages.map { coreMsg ->
                    com.example.gymbro.shared.models.ai.ChatMessage(
                        role = coreMsg.role,
                        content = coreMsg.content,
                    )
                },
                stream = true,
                maxTokens = 4000,
                temperature = 0.7,
            )

            // 委托给 streamChatWithMessageId 处理实际的AI请求
            var contentBuilder = StringBuilder()
            streamChatWithMessageId(chatRequest, messageId, taskType)
                .collect { outputToken ->
                    contentBuilder.append(outputToken.content)

                    // 转换为 StreamEvent.Chunk
                    emit(
                        StreamEvent.Chunk(
                            sessionId = sessionId,
                            messageId = messageId,
                            content = outputToken.content,
                            timestamp = outputToken.timestamp,
                        )
                    )
                }

            // 发送完成事件
            emit(
                StreamEvent.Done(
                    sessionId = sessionId,
                    messageId = messageId,
                    fullText = contentBuilder.toString(),
                    timestamp = System.currentTimeMillis(),
                )
            )
            Timber.d("✅ 流式AI响应完成: messageId=$messageId")

        } catch (e: Exception) {
            Timber.e(e, "❌ 流式AI响应失败: messageId=$messageId")

            // 发送错误事件
            emit(
                StreamEvent.Error(
                    sessionId = sessionId,
                    messageId = messageId,
                    error = e,
                    timestamp = System.currentTimeMillis(),
                )
            )
        }
    }

    /**
     * 兼容性方法：使用prompt字符串（会导致重复prompt构建）
     */
    @Deprecated(
        message = "使用streamAiResponse(messages)避免重复prompt构建",
        replaceWith = ReplaceWith(
            "streamAiResponse(sessionId, userMessageId, aiResponseId, messages, taskType)",
        ),
        level = DeprecationLevel.WARNING,
    )
    override suspend fun streamAiResponseLegacy(
        sessionId: String,
        messageId: String, // 🔥 【ID统一】使用统一的messageId
        prompt: String,
        taskType: AiTaskType,
    ): Flow<StreamEvent> {
        Timber.d("🔄 协调器：兼容性方法 - sessionId=$sessionId, messageId=$messageId") // 🔥 【ID统一】
        Timber.w("⚠️ 警告：使用兼容性方法会导致重复prompt构建，请迁移到streamAiResponse(messages)")

        // 使用AiRequestSender构建消息列表
        val messages = aiRequestSender.buildRequestMessages(
            sessionId = sessionId,
            prompt = prompt,
            taskType = taskType,
        )

        // 委托给主要方法
        return streamAiResponse(
            sessionId = sessionId,
            messageId = messageId, // 🔥 【ID统一】使用统一的messageId
            messages = messages,
            taskType = taskType,
        )
    }

    /**
     * 兼容性方法：保持向后兼容
     */
    @Deprecated("使用带完整上下文的streamAiResponse方法替代")
    override fun streamAi(
        prompt: String,
        sessionId: String,
        userMessageId: String,
        aiResponseId: String,
    ): Flow<StreamEvent> {
        Timber.w("⚠️ 使用已废弃的streamAi方法")

        // 由于是suspend函数，这里需要在协程中调用
        return kotlinx.coroutines.flow.flow {
            val messages = aiRequestSender.buildRequestMessages(
                sessionId = sessionId,
                prompt = prompt,
                taskType = AiTaskType.CHAT,
            )

            streamAiResponse(
                sessionId = sessionId,
                messageId = aiResponseId, // 🔥 【ID统一修复】使用aiResponseId作为统一messageId
                messages = messages,
                taskType = AiTaskType.CHAT,
            ).collect { event ->
                emit(event)
            }
        }
    }

    /**
     * 🔥 【PLAN B 重构】基于任务类型的流式聊天
     *
     * 委托给 streamChatWithMessageId 并提取文本内容
     */
    override suspend fun streamChatWithTaskType(
        request: ChatRequest,
        taskType: AiTaskType,
    ): Flow<String> = kotlinx.coroutines.flow.flow {
        Timber.d("🔄 [PLAN B重构] 任务类型流式聊天 - taskType=$taskType")

        try {
            // 优化请求参数
            val optimizedRequest = aiRequestSender.optimizeRequestForTask(request, taskType)

            // 生成临时 messageId 用于内部处理
            val tempMessageId = "task_${taskType.name.lowercase()}_${System.currentTimeMillis()}"

            // 委托给 streamChatWithMessageId 并提取文本内容
            streamChatWithMessageId(optimizedRequest, tempMessageId, taskType)
                .collect { outputToken ->
                    // 只发送文本内容，不包含元数据
                    emit(outputToken.content)
                }

            Timber.d("✅ 任务类型流式聊天完成: taskType=$taskType")

        } catch (e: Exception) {
            Timber.e(e, "❌ 任务类型流式聊天失败: taskType=$taskType")
            emit("处理请求时发生错误: ${e.message}")
        }
    }

    /**
     * 🔥 【架构重构】直接调用Core-Network，消除中间层
     */
    override suspend fun streamChatWithMessageId(
        request: ChatRequest,
        messageId: String,
        taskType: AiTaskType,
    ): Flow<com.example.gymbro.core.network.output.OutputToken> {
        Timber.d("🔄 [简化架构] 直接调用Core-Network - messageId=$messageId, taskType=$taskType")

        // 优化请求参数
        val optimizedRequest = aiRequestSender.optimizeRequestForTask(request, taskType)

        // 🔥 【数据流验证】记录 AiStreamRepository 调用
        Timber.tag(GymBroLogTags.Data.REPOSITORY).d("🔍 [数据流] AiStreamRepository调用: messageId=$messageId")

        // 🔥 【消除重复路径】直接调用Core-Network，跳过AiResponseReceiver中间层
        return unifiedAiResponseService.processAiStreamingResponse(optimizedRequest, messageId)
            .map { processedToken ->
                // 转换为OutputToken格式（向后兼容）
                com.example.gymbro.core.network.output.OutputToken(
                    content = processedToken,
                    messageId = messageId, // 🔥 【Plan B重构】使用messageId字段
                    contentType = com.example.gymbro.core.network.detector.ContentType.JSON_SSE,
                    timestamp = System.currentTimeMillis(),
                    metadata = mapOf("source" to "unified-ai-service-direct"),
                )
            }
    }

    /**
     * 获取任务类型支持的功能
     */
    override suspend fun getTaskCapabilities(taskType: AiTaskType): TaskCapabilities {
        return aiRequestSender.getTaskCapabilities(taskType)
    }

    /**
     * 获取监控指标
     * 🧹 REMOVED: aiResponseReceiver调用 (架构简化)
     */
    fun getMetrics(): String {
        return "Metrics temporarily unavailable - migrated to event bus architecture"
    }
}
