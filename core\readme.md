# core Module (核心功能库)

> **版本**: v3.0 - 核心基础库 + AI数据流ID统一化
> **状态**: ✅ 生产就绪
> **最后更新**: 2025-08-04 (Plan B重构完成)

## 📖 概述 (Overview)

- **模块职责**: `core`模块是GymBro项目的技术基石，提供了整个应用所需的基础核心功能和通用工具。它被设计为平台无关的，不依赖于任何具体的业务逻辑，旨在提高代码复用性、可测试性和可维护性。
- **核心功能**:
    - **统一错误处理**: 定义了`ModernResult`和`ModernDataError`，提供了一个健壮、类型安全的错误处理框架，能够优雅地处理各种预期和意外的错误情况。
    - **日志系统**: 通过`Logger`接口和`TimberManager`实现，提供了一个可配置、可扩展的日志记录系统，支持按标签过滤和不同环境下的日志输出策略。
    - **资源与文本管理**: `ResourceProvider`和`UiText`将UI文本与Android框架解耦，实现了业务逻辑与UI资源的分离，极大地便利了国际化和单元测试。
    - **AI Prompt构建**: 包含一套完整的AI提示词构建和管理系统，如`LayeredPromptBuilder`和`PromptRegistry`，支持动态、分层、可配置的提示词生成。
    - **🔥 AI数据流ID管理**: 新增`ConversationIdManager`，提供统一的消息ID管理、智能匹配和性能优化，支持AI对话流程的ID统一化。
    - **通用工具**: 提供了如`AutoSaveManager`（自动保存）、`NetworkMonitor`（网络监控）、`ThemeManager`（动态主题管理）等一系列通用服务。

## 🏗️ 架构设计 (Architecture)

`core`模块位于GymBro架构的最底层，为所有其他模块提供基础支持。

- **错误处理架构**:
  ```
  Throwable -> ModernErrorHandler -> ModernDataError -> ModernResult.Error
  ```
- **日志架构** (三层分级控制系统):
  ```
  Layer 1: TimberManager (核心控制)
  Layer 2: Logger (Interface) <- TimberLogger (Implementation)
  Layer 3: Timber (Feature层直接使用)
  ```
  📖 **详细使用指南**: [TIMBER_USAGE_GUIDE.md](src/main/kotlin/com/example/gymbro/core/logging/TIMBER_USAGE_GUIDE.md)
- **模块依赖关系**:
  - `core`不依赖任何其他GymBro模块。
  - 所有模块（`data`, `domain`, `features`等）都依赖`core`。

## 🔧 核心接口 (Core Interfaces)

| 接口/类                   | 描述                               | 状态   |
| ------------------------- | ---------------------------------- | ------ |
| `ModernResult<T>`         | 封装异步操作结果的密封类。         | ✅ 稳定 |
| `ModernErrorHandler`      | 统一的错误处理器接口。             | ✅ 稳定 |
| `Logger`                  | 日志记录接口（Domain层使用）。     | ✅ 稳定 |
| `TimberManager`           | 统一日志管理中心，支持模块级控制。 | ✅ 稳定 |
| `ResourceProvider`        | 平台无关的资源访问接口。           | ✅ 稳定 |
| `UiText`                  | UI文本的抽象表示。                 | ✅ 稳定 |
| `LayeredPromptBuilder`    | AI提示词构建器接口。               | ✅ 稳定 |
| `AutoSaveManager`         | 自动保存管理器接口。               | ✅ 稳定 |
| `ThemeManager`            | 动态主题管理器。                   | ✅ 稳定 |
| 🔥 `ConversationIdManager` | AI数据流ID统一管理器。             | ✅ 新增 |
| 🔥 `MessageContext`        | 消息上下文数据结构。               | ✅ 新增 |
| 🔥 `CompactIdGenerator`    | 6位压缩ID生成器。                  | ✅ 新增 |

## 📦 技术栈 (Tech Stack)

- **核心依赖**:
    - `org.jetbrains.kotlin:kotlin-stdlib`
    - `org.jetbrains.kotlinx:kotlinx-coroutines-core`
    - `com.jakewharton.timber:timber`
    - `javax.inject:javax.inject`
- **GymBro模块依赖**:
    - 无

## 📁 模块结构 (Module Structure)

```
core/src/main/kotlin/com/example/gymbro/core/
├── error/      (错误处理)
├── logging/    (日志记录)
├── resources/  (资源管理)
├── ui/         (UI相关核心，如UiText)
├── ai/         (AI相关核心)
├── autosave/   (自动保存)
├── network/    (网络监控)
├── theme/      (主题管理)
└── util/       (通用工具)
```

## 🧪 测试策略 (Testing Strategy)

- **测试覆盖率目标**: 95%+
- **已实现测试**:
    - 对所有工具类和扩展函数进行单元测试。
    - 对`ModernErrorHandler`和`ResourceProvider`的不同实现进行测试。
- **测试工具**:
    - `JUnit`
    - `MockK`

## 📚 使用示例 (Usage Examples)

- **处理操作结果**:
  ```kotlin
  viewModelScope.launch {
      val result = myUseCase.execute()
      handleResult(result, "fetchData",
          handleSuccess = { data -> /* ... */ },
          handleError = { error -> /* ... */ }
      )
  }
  ```

## 🎯 质量标准

- **平台无关**: `core`模块中的代码不应直接依赖Android SDK（除了必要的Context等）。
- **高内聚，低耦合**: 每个子包都应专注于单一职责。
- **文档完备性**: 所有公开的接口和类都必须有清晰的KDoc注释。

## 🔄 版本历史 (Version History)

- **v3.0 (2025-08-04)**: 🔥 Plan B重构 - AI数据流ID统一化，新增`ConversationIdManager`和智能匹配系统。
- **v2.0 (2025-06-25)**: 引入`ModernResult`和`ModernErrorHandler`，统一错误处理框架。
- **v1.5 (2025-05-10)**: 增加了AI Prompt构建和自动保存功能。
- **v1.0 (2025-03-20)**: 初始版本，定义了基础的Logger、ResourceProvider和UiText。

## 🔥 Plan B重构: AI数据流ID统一化 (v3.0)

### 概述
Plan B重构成功实现了AI数据流中ID传递的统一化，消除了`conversationId`与`messageId`的概念重复，大幅简化了Coach、Core-Network、ThinkingBox三个模块间的ID协调复杂性。

### 核心组件

#### ConversationIdManager
```kotlin
@Singleton
class ConversationIdManager @Inject constructor(
    private val compactIdGenerator: CompactIdGenerator
) {
    // 统一的消息上下文创建
    fun createMessageContext(sessionId: String): MessageContext

    // 智能ID匹配（精确 → 压缩 → 前缀）
    fun findBestMatchingMessage(partialId: String): MessageContext?

    // 性能优化的查询
    fun getMessageContext(messageId: String): MessageContext?
}
```

#### MessageContext数据结构
```kotlin
data class MessageContext(
    val messageId: String,           // 主要ID，全链路使用
    val sessionId: String,           // 会话ID，用于多轮对话分组
    val compactId: String,           // 6位压缩ID，用于日志和调试
    val timestamp: Long,             // 创建时间戳
    val metadata: Map<String, Any> = emptyMap()
)
```

### 架构优化成果

#### ID传递链路简化
```
重构前: Coach → messageId → conversationId(别名) → Core-Network → ThinkingBox(messageId+sessionId)
重构后: Coach → ConversationIdManager → MessageContext → Core-Network(messageId) → ThinkingBox(messageId)
```

#### 性能指标
- **ID创建**: < 1ms/条 (平均性能)
- **查询效率**: < 0.05ms/条 (HashMap优化)
- **智能匹配**: < 0.01ms/次 (三级匹配算法)
- **内存管理**: < 1KB/条消息，自动清理防泄漏

#### 向后兼容性
- 保留`@Deprecated`方法确保平滑迁移
- 提供`ReplaceWith`注解支持自动重构
- 现有代码继续工作，推荐使用新API

### 使用示例

#### 基本用法
```kotlin
// 注入ConversationIdManager
@Inject lateinit var conversationIdManager: ConversationIdManager

// 创建消息上下文
val messageContext = conversationIdManager.createMessageContext(sessionId)

// 智能查找
val foundContext = conversationIdManager.findBestMatchingMessage("ABC123")

// 获取统计信息
val stats = conversationIdManager.getStatistics()
```

#### 扩展函数
```kotlin
// 便捷操作
val context = conversationIdManager.getMessageContextSafely(messageId)
val isValid = conversationIdManager.validateMessageIdWithMessage(messageId)
val smartMatch = conversationIdManager.findMessageSmart(idHint)
```

### 测试覆盖
- **ConversationIdManagerTest**: 核心功能验证
- **PlanBIntegrationTest**: 集成功能测试
- **PlanBPerformanceBenchmark**: 性能基准测试

## 📞 支持与维护 (Support & Maintenance)

- **文档资源**:
    - `@.cursor/rules/基准文档规范.mdc`
- **开发团队信息**->
    - 架构组
