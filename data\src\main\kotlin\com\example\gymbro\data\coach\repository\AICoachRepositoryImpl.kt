package com.example.gymbro.data.coach.repository

import com.example.gymbro.core.conversation.ConversationIdManager
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.coach.model.CoachMessage
import com.example.gymbro.domain.coach.model.StreamEvent
import com.example.gymbro.domain.coach.repository.AICoachRepository
import com.example.gymbro.domain.coach.repository.AiStreamRepository
import com.example.gymbro.domain.coach.repository.ChatRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * AI Coach Repository 实现
 *
 * 基于现有的 AiStreamRepository 和 ChatRepository 实现 AICoachRepository 接口
 * 提供 AI 教练功能的数据访问层实现
 */
@Singleton
class AICoachRepositoryImpl
@Inject
constructor(
    private val aiStreamRepository: AiStreamRepository,
    private val chatRepository: ChatRepository,
    private val conversationIdManager: ConversationIdManager,
) : AICoachRepository {

    // 🔥 RAW 信息累积器，100字符分块记录，避免噪音
    private val rawAccumulator = StringBuilder()
    private var chunkCount = 0
    private val CHUNK_SIZE = 100

    /**
     * 🔥 蓝图修复：移除重复的RAW日志，统一使用RawTokenBus
     */
    private fun logRawContent(content: String, prefix: String = "📄") {
        // 不再记录RAW日志，避免与RawTokenBus重复
    }

    /**
     * 刷新剩余的 RAW 内容
     */
    private fun flushRawContent(prefix: String = "📄") {
        if (rawAccumulator.isNotEmpty()) {
            chunkCount++
            val chunk = rawAccumulator.toString().replace("\n", "\\n")
            // 🔥 蓝图优化：移除AI-RAW重复日志，统一使用TB-RAW
            rawAccumulator.clear()
        }
    }

    /**
     * 重置 RAW 累积器
     */
    private fun resetRawAccumulator() {
        rawAccumulator.clear()
        chunkCount = 0
    }

    /**
     * 向AI教练发送消息（支持多轮对话）
     */
    override fun sendMessageToCoach(
        sessionId: String,
        userInput: String,
    ): Flow<ModernResult<CoachMessage>> =
        flow {
            try {
                Timber.d("🤖 AICoach: 发送消息到会话 $sessionId")
                emit(ModernResult.Loading)

                // 生成消息ID
                val userMessageId = generateMessageId()
                val aiResponseId = generateMessageId()

                // 🔥 修复：使用新的streamAiResponse方法，避免重复prompt构建
                // 将userInput解析为消息列表（如果是JSON格式）或创建简单的用户消息
                val messages = try {
                    // 尝试解析为消息列表JSON
                    Json.decodeFromString<List<com.example.gymbro.core.ai.prompt.builder.CoreChatMessage>>(
                        userInput,
                    )
                } catch (e: Exception) {
                    // 如果不是JSON，创建简单的用户消息
                    listOf(com.example.gymbro.core.ai.prompt.builder.CoreChatMessage("user", userInput))
                }

                // 使用新的streamAiResponse方法
                // 创建或获取消息上下文
                val messageContext = conversationIdManager.getMessageContext(aiResponseId)
                    ?: conversationIdManager.createMessageContext(sessionId)

                aiStreamRepository
                    .streamAiResponse(
                        messageContext = messageContext,
                        messages = messages,
                        taskType = com.example.gymbro.domain.coach.model.AiTaskType.CHAT,
                    ).collect { streamEvent ->
                        // 将 StreamEvent 转换为 CoachMessage
                        when (streamEvent) {
                            is StreamEvent.Chunk -> {
                                val coachMessage =
                                    CoachMessage.AiMessage(
                                        id = aiResponseId,
                                        content = streamEvent.content,
                                        timestamp = System.currentTimeMillis(),
                                    )
                                emit(ModernResult.Success(coachMessage))
                            }
                            is StreamEvent.Thinking -> {
                                // 创建一个简单的AI消息表示思考状态
                                val thinkingMessage =
                                    CoachMessage.AiMessage(
                                        id = streamEvent.messageId, // 🔥 【ID统一修复】使用统一的messageId
                                        content = "正在思考...",
                                        timestamp = System.currentTimeMillis(),
                                    )
                                emit(ModernResult.Success(thinkingMessage))
                            }
                            is StreamEvent.Done -> {
                                val finalMessage =
                                    CoachMessage.AiMessage(
                                        id = aiResponseId,
                                        content = streamEvent.fullText,
                                        timestamp = System.currentTimeMillis(),
                                    )
                                emit(ModernResult.Success(finalMessage))
                            }
                            is StreamEvent.Error -> {
                                emit(
                                    ModernResult.Error(
                                        ModernDataError(
                                            operationName = "sendMessageToCoach",
                                            errorType = GlobalErrorType.Network.General,
                                            uiMessage = UiText.DynamicString(
                                                streamEvent.error.message ?: "网络错误",
                                            ),
                                        ),
                                    ),
                                )
                            }
                            // 添加 else 分支以使 when 表达式完整
                            else -> {
                                // 处理其他 StreamEvent 类型，如果有的话
                            }
                        }
                    }
            } catch (e: Exception) {
                Timber.e(e, "🤖 AICoach: 发送消息失败")
                emit(
                    ModernResult.Error(
                        ModernDataError(
                            operationName = "sendMessageToCoach",
                            errorType = GlobalErrorType.Unknown,
                            cause = e,
                        ),
                    ),
                )
            }
        }

    /**
     * 向AI教练发送消息并获取原始的流式响应
     *
     * 🔥 新增：接收已构建的消息列表，避免重复prompt构建
     */
    override fun getStreamingResponse(
        sessionId: String,
        messages: List<com.example.gymbro.core.ai.prompt.builder.CoreChatMessage>,
    ): Flow<String> = flow {
        try {
            Timber.d("🚀 AICoachRepositoryImpl.getStreamingResponse() 开始 - sessionId=$sessionId")
            Timber.d("📝 接收到消息列表: ${messages.size}条消息")

            // 🔥 【Token流修复】添加详细的调试日志
            Timber
                .tag("TOKEN-FLOW")
                .d("🔥 [AICoachRepositoryImpl] getStreamingResponse开始: sessionId=$sessionId")

            // 生成消息ID
            val userMessageId = generateMessageId()
            val aiResponseId = generateMessageId()
            Timber.d("🆔 生成消息ID - userMessageId=$userMessageId, aiResponseId=$aiResponseId")
            Timber
                .tag("TOKEN-FLOW")
                .d(
                    "🔥 [AICoachRepositoryImpl] 生成消息ID: userMessageId=$userMessageId, aiResponseId=$aiResponseId",
                )

            // 🔥 修复：直接传递消息列表，不再重复构建prompt
            Timber.d("🔄 开始调用 aiStreamRepository.streamAiResponse() - 传递${messages.size}条消息")
            Timber
                .tag("TOKEN-FLOW")
                .d("🔥 [AICoachRepositoryImpl] 开始调用aiStreamRepository.streamAiResponse")

            // 创建或获取消息上下文
            val messageContext = conversationIdManager.getMessageContext(aiResponseId)
                ?: conversationIdManager.createMessageContext(sessionId)

            aiStreamRepository
                .streamAiResponse(
                    messageContext = messageContext,
                    messages = messages, // 🔥 直接传递消息列表
                    taskType = com.example.gymbro.domain.coach.model.AiTaskType.CHAT,
                ).collect { streamEvent ->
                    // 🔥 重置 RAW 累积器（新的流开始）
                    if (streamEvent is StreamEvent.Thinking) {
                        resetRawAccumulator()
                    }

                    // 🔥 【Token流修复】添加StreamEvent接收日志
                    Timber
                        .tag("TOKEN-FLOW")
                        .d("🔥 [AICoachRepositoryImpl] 收到StreamEvent: ${streamEvent::class.simpleName}")

                    // 🔥 修复：处理所有相关事件类型
                    when (streamEvent) {
                        is StreamEvent.Thinking -> {
                            // Thinking事件：发送空字符串表示开始思考
                            // 🔥 蓝图优化：移除AI-RAW重复日志，统一使用TB-RAW
                            Timber
                                .tag("TOKEN-FLOW")
                                .d("🔥 [AICoachRepositoryImpl] 处理Thinking事件，emit空字符串")
                            emit("") // 发送空字符串触发ThinkingBox显示
                        }

                        is StreamEvent.Chunk -> {
                            // Chunk事件：发送实际内容
                            // 🔥 蓝图优化：移除AI-RAW重复日志，统一使用TB-RAW
                            // 🔥 记录接收到的 RAW 内容
                            Timber.tag("TOKEN-FLOW").d(
                                "🔥 [AICoachRepositoryImpl] 处理Chunk事件: content='${
                                    streamEvent.content.take(50)
                                }...', length=${streamEvent.content.length}",
                            )
                            logRawContent(streamEvent.content, "📥")
                            emit(streamEvent.content)
                            Timber.tag("TOKEN-FLOW").d("🔥 [AICoachRepositoryImpl] Chunk内容已emit")
                        }
                        is StreamEvent.Done -> {
                            // Done事件：可以选择发送完成标记或忽略
                            // 🔥 蓝图优化：移除AI-RAW重复日志，统一使用TB-RAW
                            // 🔥 刷新剩余的 RAW 内容
                            flushRawContent("📥")
                            // 不需要emit，流会自然结束
                        }
                        is StreamEvent.Error -> {
                            // Error事件：抛出异常
                            // 🔥 蓝图优化：移除AI-RAW重复日志，统一使用TB-RAW
                            throw streamEvent.error
                        }
                        is StreamEvent.Phase -> {
                            // Phase事件：可以选择处理或忽略
                            Timber.tag("AI-COACH-RAW").e("🔄 收到Phase事件: phase=${streamEvent.phase}")
                            // 暂时忽略Phase事件
                        }
                    }
                }
        } catch (e: Exception) {
            Timber.e(e, "🤖 AICoach: 获取流式响应失败")
            // 在Flow中，错误应该通过抛出异常来传播
            throw e
        }
    }

    /**
     * 向AI教练发送消息并获取原始的流式响应（兼容性方法）
     *
     * @deprecated 使用getStreamingResponse(messages)避免重复prompt构建
     */
    @Deprecated(
        message = "使用getStreamingResponse(messages)避免重复prompt构建",
        replaceWith = ReplaceWith("getStreamingResponse(sessionId, messages)"),
        level = DeprecationLevel.WARNING,
    )
    override fun getStreamingResponseLegacy(sessionId: String, userInput: String): Flow<String> = flow {
        try {
            Timber.d("🚀 AICoachRepositoryImpl.getStreamingResponseLegacy() 开始 - sessionId=$sessionId")
            Timber.d("📝 userInput内容: ${userInput.take(200)}...")

            // 生成消息ID
            val userMessageId = generateMessageId()
            val aiResponseId = generateMessageId()
            Timber.d("🆔 生成消息ID - userMessageId=$userMessageId, aiResponseId=$aiResponseId")

            // 🔥 【阶段3重构】创建简单的消息列表来适配新接口
            val messages = listOf(
                com.example.gymbro.core.ai.prompt.builder.CoreChatMessage(
                    role = "user",
                    content = userInput
                )
            )

            // 创建或获取消息上下文
            val messageContext = conversationIdManager.getMessageContext(aiResponseId)
                ?: conversationIdManager.createMessageContext(sessionId)

            // 使用新的 streamAiResponse 方法
            Timber.d("🔄 开始调用 aiStreamRepository.streamAiResponse()")
            aiStreamRepository
                .streamAiResponse(
                    messageContext = messageContext,
                    messages = messages,
                    taskType = com.example.gymbro.domain.coach.model.AiTaskType.CHAT,
                ).collect { streamEvent ->
                    // 🔥 重置 RAW 累积器（新的流开始）
                    if (streamEvent is StreamEvent.Thinking) {
                        resetRawAccumulator()
                    }

                    // 🔥 修复：处理所有相关事件类型（与主方法保持一致）
                    when (streamEvent) {
                        is StreamEvent.Thinking -> {
                            // Thinking事件：发送空字符串表示开始思考
                            Timber.tag(
                                "AI-COACH-RAW",
                            ).e("🧠 收到Thinking事件: messageId=${streamEvent.messageId}") // 🔥 【ID统一修复】
                            emit("") // 发送空字符串触发ThinkingBox显示
                        }
                        is StreamEvent.Chunk -> {
                            // Chunk事件：发送实际内容
                            Timber.tag(
                                "AI-COACH-RAW",
                            ).e("📄 收到Chunk事件: content长度=${streamEvent.content.length}")
                            // 🔥 记录接收到的 RAW 内容
                            logRawContent(streamEvent.content, "📥")
                            emit(streamEvent.content)
                        }
                        is StreamEvent.Done -> {
                            // Done事件：可以选择发送完成标记或忽略
                            Timber.tag(
                                "AI-COACH-RAW",
                            ).e("✅ 收到Done事件: fullText长度=${streamEvent.fullText.length}")
                            // 🔥 刷新剩余的 RAW 内容
                            flushRawContent("📥")
                            // 不需要emit，流会自然结束
                        }
                        is StreamEvent.Error -> {
                            // Error事件：抛出异常
                            Timber.tag("AI-COACH-RAW").e(streamEvent.error, "❌ 收到Error事件")
                            throw streamEvent.error
                        }
                        is StreamEvent.Phase -> {
                            // Phase事件：可以选择处理或忽略
                            Timber.tag("AI-COACH-RAW").e("🔄 收到Phase事件: phase=${streamEvent.phase}")
                            // 暂时忽略Phase事件
                        }
                    }
                }
        } catch (e: Exception) {
            Timber.e(e, "🤖 AICoach: 获取流式响应失败")
            // 在Flow中，错误应该通过抛出异常来传播
            throw e
        }
    }

    /**
     * 获取会话的对话历史记录
     */
    override fun getCoachingHistory(
        sessionId: String,
        limit: Int,
    ): Flow<ModernResult<List<CoachMessage>>> =
        flow {
            try {
                Timber.d("🤖 AICoach: 获取会话历史 $sessionId, limit=$limit")
                emit(ModernResult.Loading)

                // 🔥 修复：使用chatRepository获取历史记录
                val historyResult = chatRepository.getRecentHistory(sessionId, limit)
                val messages =
                    when (historyResult) {
                        is ModernResult.Success -> historyResult.data
                        is ModernResult.Error -> {
                            emit(ModernResult.Error(historyResult.error))
                            return@flow
                        }
                        is ModernResult.Loading -> emptyList()
                    }

                Timber.d("🤖 AICoach: 获取到 ${messages.size} 条历史消息")
                emit(ModernResult.Success(messages))
            } catch (e: Exception) {
                Timber.e(e, "🤖 AICoach: 获取历史记录失败")
                emit(
                    ModernResult.Error(
                        ModernDataError(
                            operationName = "getCoachingHistory",
                            errorType = GlobalErrorType.Database.QueryFailed,
                            cause = e,
                        ),
                    ),
                )
            }
        }

    /**
     * 清空指定会话的对话历史
     */
    override suspend fun clearCoachingHistory(sessionId: String): ModernResult<Unit> =
        try {
            Timber.d("🤖 AICoach: 清空会话历史 $sessionId")

            // 🔥 修复：使用chatRepository清空历史记录
            // TODO: 实现清空会话消息的功能
            // chatRepository.clearSessionMessages(sessionId)
            Timber.d("🤖 AICoach: 会话历史清空成功")
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            Timber.e(e, "🤖 AICoach: 清空历史记录失败")
            ModernResult.Error(
                ModernDataError(
                    operationName = "clearCoachingHistory",
                    errorType = GlobalErrorType.Database.QueryFailed,
                    cause = e,
                ),
            )
        }

    /**
     * 生成消息ID - 修复：使用统一的UUID格式确保ID一致性
     */
    private fun generateMessageId(): String =
        com.example.gymbro.core.util.Constants.MessageId
            .generate()
}
