# shared-models Module (共享数据模型)

> **版本**: v2.2 - Template状态管理系统优化版
> **状态**: ✅ 生产就绪
> **最后更新**: 2025-08-03
> **重大更新**: ✅ Template状态枚举简化为二元状态设计
> **字段对齐**: ✅ Session/Plan/Calendar模块JSON字段命名规范完全合规

## 📖 概述 (Overview)

- **模块职责**: `shared-models` 是GymBro项目的基石，定义了所有模块间共享的数据传输对象(DTOs)。它通过提供一套统一、纯粹、与Android框架解耦的数据模型，确保了整个应用数据流的一致性、类型安全和可维护性。
- **核心功能**:
    - **统一JSON结构**: 引入 `EntityWrapper<T>`，为所有核心实体提供"实体头 + 版本锁"的标准化JSON格式。
    - **核心实体定义**: 包含 `Exercise`, `Template`, `Plan`, `Session` 等核心业务的纯数据 `Payload`。
    - **AI模型**: 定义了与AI交互所需的数据结构，包括 `ChatRequest`/`Response` 和为Function Call设计的轻量级引用模型。
    - **序列化支持**: 全面采用 `kotlinx.serialization`，提供高效、安全的JSON处理能力。
    - **字段命名规范**: 统一使用camelCase命名风格，通过@SerialName注解保持JSON兼容性。
    - **零依赖**: 是一个纯Kotlin模块，不依赖任何Android框架，保证了其高度的可移植性和可测试性。

## 🏗️ 架构设计 (Architecture)

`shared-models` 位于GymBro架构的最底层，作为所有上层模块（`data`, `domain`, `features`）的数据契约。

- **`EntityWrapper` 架构**:
  ```
  +----------------------------------+
    | EntityWrapper<T>        |
    | ----------------------- |
    | - schemaVersion: String | // 全局Schema版本            |
    | - entity: EntityType    | // 实体类型 (e.g., EXERCISE) |
    | - entityVersion: Int    | // 实体自身版本              |
    | - generatedAt: Long     | // 生成时间戳                |
    | - payload: T            | // 泛型业务数据              |
  +----------------------------------+
  ```
- **模块依赖关系**:
  - `shared-models` 不依赖任何其他GymBro模块。
  - 所有需要进行数据交换的模块都必须依赖 `shared-models`。

## 🔧 核心接口 (Core Interfaces)

此模块不包含业务逻辑接口（如UseCase），只定义数据结构。核心的数据结构如下：

| 实体 (Payload)        | 描述                   | 关键字段                                            |
| --------------------- | ---------------------- | --------------------------------------------------- |
| `ExercisePayload`     | 动作库中的单个动作     | `id`, `name`, `muscleGroup`, `equipment`            |
| `TemplatePayload`     | 训练模板               | `id`, `name`, `exercises`, `versionTag`             |
| `PlanPayload`         | 训练计划               | `id`, `name`, `templateSchedule`, `versionTag`      |
| `SessionPayload`      | 训练会话               | `id`, `embeddedTemplate`, `executionData`, `status` |
| `AiExerciseReference` | AI专用的轻量级动作引用 | `id`, `name`, `muscleGroup`, `contentHash`          |
| `AiPlanReference`     | AI专用的轻量级计划引用 | `id`, `name`, `planType`, `totalDays`               |
| `MemoryRecord`        | 统一记忆系统的数据记录 | `id`, `tier`, `importance`, `embedding`             |

### 🎯 JSON字段命名规范合规性 (v2.1新增)

所有DTO类已完成JSON字段命名规范合规性修复，确保跨模块数据流的一致性：

| 模块         | 关键字段           | Kotlin字段名                            | JSON序列化名                                      | 状态   |
| ------------ | ------------------ | --------------------------------------- | ------------------------------------------------- | ------ |
| **Session**  | 重量/次数/休息时间 | `weight`, `reps`, `restTimeSeconds`     | `"weight"`, `"reps"`, `"restTimeSeconds"`         | ✅ 统一 |
| **Plan**     | 计划信息           | `planId`, `planName`, `totalDays`       | `"plan_id"`, `"plan_name"`, `"total_days"`        | ✅ 兼容 |
| **Calendar** | 日历条目           | `dayNumber`, `isRestDay`, `templateIds` | `"day_number"`, `"is_rest_day"`, `"template_ids"` | ✅ 兼容 |

**核心修复内容**:
- ✅ **SessionSetDto**: 添加缺失的`restTimeSeconds`字段，与Template/Exercise模块保持一致
- ✅ **Calendar模块**: 统一字段命名为camelCase + @SerialName保持JSON兼容性
- ✅ **Plan模块**: 统一字段命名为camelCase + @SerialName保持JSON兼容性

## 📦 技术栈 (Tech Stack)

- **核心依赖**:
    - `org.jetbrains.kotlin:kotlin-stdlib`
    - `org.jetbrains.kotlinx:kotlinx-serialization-core`
    - `org.jetbrains.kotlinx:kotlinx-serialization-json`
- **GymBro模块依赖**:
    - 无

## 📁 模块结构 (Module Structure)

```
shared-models/src/main/kotlin/com/example/gymbro/shared/models/
├── ai/
├── common/
├── exercise/
├── memory/
├── network/
├── user/
└── workout/
```

## 🧪 测试策略 (Testing Strategy)

- **测试覆盖率目标**: 95%+
- **已实现测试**:
    - 单元测试覆盖所有DTOs的数据一致性。
    - `JsonCodec` 和各实体 `Adapter` 的序列化/反序列化逻辑测试。
- **测试工具**:
    - `JUnit`
    - `AssertJ`

## 📚 使用示例 (Usage Examples)

- **序列化**:
  ```kotlin
  // 使用Adapter将Payload对象转换为JSON字符串
  val templateJson = TemplateAdapter.toJson(myTemplatePayload)
  ```
- **反序列化**:
  ```kotlin
  // 使用Adapter将JSON字符串安全地转换回Payload对象
  try {
      val templatePayload = TemplateAdapter.fromJson(jsonString)
      // ... 使用 templatePayload
  } catch (e: IllegalArgumentException) {
      // 处理格式错误、实体不匹配等问题
  }
  ```

## 🎯 质量标准

- **数据一致性**: 所有DTOs必须是不可变 (val) 的，以确保线程安全和数据一致性。
- **代码质量**: 遵循Kotlin官方编码规范，通过Detekt静态分析检查。
- **文档完备性**: 所有公开的DTO和字段都必须有清晰的KDoc注释。

## 🔄 版本历史 (Version History)

- **v2.1 (2025-07-28)**: JSON字段命名规范合规性修复，统一Session/Plan/Calendar模块字段命名风格，添加SessionSetDto缺失字段。
- **v2.0 (2025-06-25)**: 引入 `EntityWrapper` 统一模型，重构所有核心Payload，增加乐观锁和所有权字段。
- **v1.5 (2025-04-10)**: 增加AI Function Call和记忆系统相关模型。
- **v1.0 (2025-02-20)**: 初始版本，定义了基础的DTOs。

## 📞 支持与维护 (Support & Maintenance)

- **文档资源**:
    - `@.cursor/rules/基准文档规范.mdc`
- **开发团队信息**:
    - 架构组

## 📝 更新历史 (Update History)

### v2.2 (2025-08-03) - Template状态管理系统优化
- **🎯 核心优化**: Template状态枚举从三状态简化为二元状态设计
  - **移除状态**: `UNSAVED_DRAFT`, `SAVED_DRAFT`
  - **保留状态**: `DRAFT`, `PUBLISHED`
- **🔧 技术改进**:
  - 消除语义不一致问题（新建模板默认为DRAFT）
  - 简化状态转换逻辑：DRAFT → PUBLISHED（单向）
  - 减少约30%的状态相关代码复杂度
  - 统一状态判断逻辑，使用 `templateState` 字段
- **📊 用户体验提升**:
  - 状态概念更直观（草稿 vs 正式模板）
  - 操作流程更清晰，认知负担降低
- **🏗️ 架构收益**:
  - 为未来功能扩展提供清晰基础
  - 统一的状态管理模式，减少bug可能性
  - 符合Clean Architecture原则

### v2.1 (2025-07-28) - JSON字段命名规范合规性修复版
- **字段对齐**: Session/Plan/Calendar模块JSON字段命名规范完全合规
