# 📊 测试验证和性能监控计划

## 🎯 验证目标

确保 PLAN B 架构重构后系统的功能完整性、性能提升和稳定性，建立完善的监控体系。

## 🧪 测试策略

### 1. **单元测试更新**

#### ConversationIdManager 测试
```kotlin
class ConversationIdManagerTest {
    
    @Test
    fun `createMessageContext should generate valid context`() {
        // Given
        val sessionId = "test_session"
        
        // When
        val context = conversationIdManager.createMessageContext(sessionId)
        
        // Then
        assertThat(context.messageId).isNotEmpty()
        assertThat(context.sessionId).isEqualTo(sessionId)
        assertThat(context.compactId).hasLength(6)
        assertThat(context.timestamp).isGreaterThan(0)
    }
    
    @Test
    fun `findBestMatchingMessage should handle partial IDs`() {
        // Given
        val context = conversationIdManager.createMessageContext("session1")
        val partialId = context.messageId.take(8)
        
        // When
        val found = conversationIdManager.findBestMatchingMessage(partialId)
        
        // Then
        assertThat(found).isEqualTo(context)
    }
    
    @Test
    fun `cleanupExpiredMessages should remove old messages`() {
        // Given
        val oldContext = conversationIdManager.createMessageContext("session1")
        Thread.sleep(100)
        val newContext = conversationIdManager.createMessageContext("session2")
        
        // When
        conversationIdManager.cleanupExpiredMessages(50) // 50ms 过期时间
        
        // Then
        assertThat(conversationIdManager.getMessageContext(oldContext.messageId)).isNull()
        assertThat(conversationIdManager.getMessageContext(newContext.messageId)).isNotNull()
    }
}
```

#### Repository 层测试
```kotlin
class AiStreamRepositoryImplTest {
    
    @Mock private lateinit var unifiedAiResponseService: UnifiedAiResponseService
    @Mock private lateinit var conversationIdManager: ConversationIdManager
    @Mock private lateinit var aiRequestSender: AiRequestSender
    
    @Test
    fun `streamAiResponse should map OutputToken to StreamEvent correctly`() = runTest {
        // Given
        val messageContext = MessageContext.create("session1", compactIdGenerator)
        val messages = listOf(CoreChatMessage.user("Hello"))
        val outputToken = OutputToken(
            content = "Hi there!",
            messageId = messageContext.messageId,
            contentType = ContentType.JSON_SSE
        )
        
        whenever(unifiedAiResponseService.processAiStreamingResponse(any(), any()))
            .thenReturn(flowOf(outputToken))
        
        // When
        val result = repository.streamAiResponse(messageContext, messages, AiTaskType.CHAT)
        
        // Then
        result.test {
            val event = awaitItem()
            assertThat(event).isInstanceOf(StreamEvent.ContentToken::class.java)
            assertThat((event as StreamEvent.ContentToken).content).isEqualTo("Hi there!")
            awaitComplete()
        }
    }
}
```

### 2. **集成测试**

#### 端到端数据流测试
```kotlin
@Test
fun `complete message flow should work end to end`() = runTest {
    // Given
    val sessionId = "integration_test_session"
    val userMessage = "Hello AI"
    
    // When - 用户发送消息
    val sendResult = coachRepository.sendMessage(sessionId, userMessage)
    
    // Then - 验证消息保存
    assertThat(sendResult).isInstanceOf(ModernResult.Success::class.java)
    
    // And - 验证 AI 响应启动
    verify(unifiedAiResponseService).processAiStreamingResponse(any(), any())
    
    // And - 验证 ThinkingBox 订阅
    verify(directOutputChannel).subscribeToMessage(any())
    
    // And - 模拟 AI 响应
    val messageId = (sendResult as ModernResult.Success).data.id
    val tokens = listOf("Hello", " ", "there", "!")
    
    tokens.forEach { token ->
        directOutputChannel.sendToken(
            OutputToken(
                content = token,
                messageId = messageId,
                contentType = ContentType.JSON_SSE
            )
        )
    }
    
    // Then - 验证 ThinkingBox 接收
    advanceTimeBy(1000) // 等待处理完成
    val thinkingBoxState = thinkingBoxViewModel.state.value
    assertThat(thinkingBoxState.tokenCount).isEqualTo(4)
    assertThat(thinkingBoxState.finalContent).contains("Hello there!")
}
```

#### 错误场景测试
```kotlin
@Test
fun `should handle network errors gracefully`() = runTest {
    // Given
    whenever(unifiedAiResponseService.processAiStreamingResponse(any(), any()))
        .thenThrow(IOException("Network error"))
    
    // When
    val result = repository.streamAiResponse(messageContext, messages, AiTaskType.CHAT)
    
    // Then
    result.test {
        val event = awaitItem()
        assertThat(event).isInstanceOf(StreamEvent.Error::class.java)
        assertThat((event as StreamEvent.Error).error).contains("Network error")
        awaitComplete()
    }
}
```

### 3. **性能测试**

#### 响应时间基准测试
```kotlin
@Test
fun `message processing should complete within acceptable time`() = runTest {
    val startTime = System.currentTimeMillis()
    
    // 发送消息并等待完成
    val result = coachRepository.sendMessage("perf_test", "Performance test message")
    
    val endTime = System.currentTimeMillis()
    val duration = endTime - startTime
    
    // 验证响应时间 < 500ms
    assertThat(duration).isLessThan(500)
    assertThat(result).isInstanceOf(ModernResult.Success::class.java)
}
```

#### 内存使用测试
```kotlin
@Test
fun `should not leak memory during long conversations`() = runTest {
    val initialMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory()
    
    // 模拟长对话（100 条消息）
    repeat(100) { i ->
        coachRepository.sendMessage("memory_test", "Message $i")
        delay(10) // 短暂延迟
    }
    
    // 强制垃圾回收
    System.gc()
    delay(100)
    
    val finalMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory()
    val memoryIncrease = finalMemory - initialMemory
    
    // 验证内存增长 < 50MB
    assertThat(memoryIncrease).isLessThan(50 * 1024 * 1024)
}
```

## 📈 性能监控

### 1. **关键指标定义**

```kotlin
data class PerformanceMetrics(
    val messageProcessingTime: Long,      // 消息处理时间
    val tokenDeliveryLatency: Long,       // Token 传递延迟
    val subscriptionSetupTime: Long,      // 订阅建立时间
    val memoryUsage: Long,                // 内存使用量
    val activeSubscriptions: Int,         // 活跃订阅数
    val errorRate: Float,                 // 错误率
    val throughput: Float                 // 吞吐量 (messages/second)
)
```

### 2. **监控实现**

```kotlin
@Singleton
class ArchitecturePerformanceMonitor @Inject constructor() {
    
    private val metrics = mutableMapOf<String, MutableList<Long>>()
    private val startTimes = mutableMapOf<String, Long>()
    
    fun startMeasurement(operation: String) {
        startTimes[operation] = System.currentTimeMillis()
    }
    
    fun endMeasurement(operation: String) {
        val startTime = startTimes.remove(operation) ?: return
        val duration = System.currentTimeMillis() - startTime
        
        metrics.getOrPut(operation) { mutableListOf() }.add(duration)
        
        // 记录到日志
        Timber.tag("PerformanceMonitor").d("$operation 耗时: ${duration}ms")
        
        // 检查性能阈值
        checkPerformanceThresholds(operation, duration)
    }
    
    private fun checkPerformanceThresholds(operation: String, duration: Long) {
        val thresholds = mapOf(
            "message_processing" to 1000L,      // 1秒
            "token_delivery" to 100L,           // 100ms
            "subscription_setup" to 200L        // 200ms
        )
        
        thresholds[operation]?.let { threshold ->
            if (duration > threshold) {
                Timber.tag("PerformanceMonitor").w(
                    "⚠️ 性能警告: $operation 耗时 ${duration}ms 超过阈值 ${threshold}ms"
                )
            }
        }
    }
    
    fun getAverageTime(operation: String): Long {
        return metrics[operation]?.average()?.toLong() ?: 0L
    }
    
    fun generateReport(): PerformanceReport {
        return PerformanceReport(
            messageProcessingAvg = getAverageTime("message_processing"),
            tokenDeliveryAvg = getAverageTime("token_delivery"),
            subscriptionSetupAvg = getAverageTime("subscription_setup"),
            totalOperations = metrics.values.sumOf { it.size },
            timestamp = System.currentTimeMillis()
        )
    }
}
```

### 3. **实时监控集成**

```kotlin
// 在关键组件中集成监控
class CoachEffectHandler @Inject constructor(
    private val performanceMonitor: ArchitecturePerformanceMonitor
) {
    
    suspend fun handle(effect: Effect.StartAiStream): Flow<Intent> = flow {
        performanceMonitor.startMeasurement("message_processing")
        
        try {
            // 执行业务逻辑
            unifiedAiResponseService.processAiStreamingResponse(...)
            
            performanceMonitor.endMeasurement("message_processing")
        } catch (e: Exception) {
            performanceMonitor.endMeasurement("message_processing")
            throw e
        }
    }
}
```

## 🔍 监控仪表板

### 实时指标显示
```kotlin
@Composable
fun PerformanceMonitoringScreen(
    performanceMonitor: ArchitecturePerformanceMonitor
) {
    val report by remember { 
        derivedStateOf { performanceMonitor.generateReport() }
    }
    
    LazyColumn {
        item {
            MetricCard(
                title = "消息处理平均时间",
                value = "${report.messageProcessingAvg}ms",
                threshold = 1000L,
                current = report.messageProcessingAvg
            )
        }
        
        item {
            MetricCard(
                title = "Token 传递延迟",
                value = "${report.tokenDeliveryAvg}ms",
                threshold = 100L,
                current = report.tokenDeliveryAvg
            )
        }
        
        // 更多指标...
    }
}
```

## 📋 验证清单

### 功能验证
- [ ] 用户可以正常发送消息
- [ ] AI 响应正常显示在 ThinkingBox
- [ ] 错误处理机制正常工作
- [ ] 会话管理功能完整
- [ ] 历史记录正常加载

### 性能验证
- [ ] 消息处理时间 < 1秒
- [ ] Token 传递延迟 < 100ms
- [ ] 内存使用稳定，无泄漏
- [ ] CPU 使用率优化
- [ ] 网络请求效率提升

### 架构验证
- [ ] 模块间依赖关系简化
- [ ] 代码复杂度降低
- [ ] 测试覆盖率 > 85%
- [ ] 文档完整更新
- [ ] 监控体系完善

## 🎯 成功标准

1. **功能完整性**: 100% 现有功能正常工作
2. **性能提升**: 响应时间减少 30%，内存使用降低 20%
3. **代码质量**: 复杂度降低 40%，bug 数量减少 50%
4. **维护性**: 新功能开发时间减少 25%
5. **稳定性**: 错误率 < 1%，可用性 > 99.5%
