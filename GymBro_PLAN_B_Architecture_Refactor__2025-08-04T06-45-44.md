[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[/] NAME:GymBro PLAN B 架构重构总体项目 DESCRIPTION:基于PLAN B架构重构文档，彻底解耦三大模块，消除冗余中间层，建立统一数据流，实现统一ID管理。每个阶段完成后必须编译通过。
--[x] NAME:阶段1: 核心基础设施建设 DESCRIPTION:完善ConversationIdManager、重构Coach Contract、统一数据流设计验证、搭建基础监控体系。预计40小时，风险等级：中等
---[x] NAME:1.1 ConversationIdManager 完善 DESCRIPTION:实现统一ID管理的单一真实来源，包括 createMessageContext、validateMessageId、findBestMatchingMessage 方法。预计8小时
---[x] NAME:1.2 Coach Contract 重构 DESCRIPTION:基于 ConversationIdManager 的简化设计，更新 State、Intent、Effect 定义，消除 conversationId 概念。预计16小时
---[x] NAME:1.3 统一数据流设计验证 DESCRIPTION:验证 Coach → Core-Network → ThinkingBox 直接通信链路，确保数据流设计的正确性。预计8小时
---[x] NAME:1.4 基础监控体系搭建 DESCRIPTION:建立性能监控框架，包括 ArchitecturePerformanceMonitor 和关键指标定义。预计8小时
--[x] NAME:阶段2: 冗余组件清理 DESCRIPTION:删除AiResponseReceiver、清理空实现方法、更新依赖注入、编译和基础测试。预计24小时，风险等级：高
---[x] NAME:2.1 AiResponseReceiver 删除 DESCRIPTION:完全删除 AiResponseReceiver.kt 文件，分析依赖关系，确保无隐藏依赖。预计8小时
---[x] NAME:2.2 空实现方法清理 DESCRIPTION:清理 AiStreamRepositoryImpl 中的空实现方法，实现完整功能或删除不需要的接口。预计4小时
---[/] NAME:2.3 依赖注入更新 DESCRIPTION:更新 Hilt DI 配置，移除 AiResponseReceiver 的绑定，更新相关的 import 语句。预计4小时
---[ ] NAME:2.4 编译和基础测试 DESCRIPTION:修复编译错误，运行单元测试和集成测试，确保系统可正常编译。预计8小时
--[ ] NAME:阶段3: Repository层重构 DESCRIPTION:AiStreamRepository接口简化、AiStreamRepositoryImpl重构、AICoachRepositoryImpl优化。预计32小时，风险等级：中等
---[ ] NAME:3.1 AiStreamRepository 接口简化 DESCRIPTION:基于 MessageContext 的简化接口设计，删除废弃方法，统一方法签名。预计8小时
---[ ] NAME:3.2 AiStreamRepositoryImpl 重构 DESCRIPTION:实现统一的直接调用模式，集成 ConversationIdManager，实现完整的 streamAiResponse 方法。预计16小时
---[ ] NAME:3.3 AICoachRepositoryImpl 优化 DESCRIPTION:集成 ConversationIdManager，更新消息发送逻辑，简化ID管理和参数传递。预计8小时
--[ ] NAME:阶段4: ThinkingBox集成优化 DESCRIPTION:ThinkingBox Contract简化、直接订阅机制实现、错误处理统一、UI优化和测试。预计28小时，风险等级：中等
---[ ] NAME:4.1 ThinkingBox Contract 简化 DESCRIPTION:优化 ThinkingBox Contract 定义，移除冗余字段，新增订阅状态管理，简化 Intent 和 Effect。预计8小时
---[ ] NAME:4.2 直接订阅机制实现 DESCRIPTION:实现 ThinkingBox 直接订阅 DirectOutputChannel，移除中间层，实现预订阅机制。预计12小时
---[ ] NAME:4.3 错误处理统一 DESCRIPTION:实现统一的错误处理机制，定义 ThinkingBoxError 类型，优化错误显示。预计4小时
---[ ] NAME:4.4 UI优化和测试 DESCRIPTION:优化 ThinkingBox UI 性能，实现内存管理，编写集成测试验证功能。预计4小时
--[ ] NAME:阶段5: 测试验证和监控 DESCRIPTION:端到端集成测试、性能基准测试、监控仪表板完善、文档更新和培训。预计32小时，风险等级：低
---[ ] NAME:5.1 端到端集成测试 DESCRIPTION:编写完整的消息流程测试，验证 Coach → Core-Network → ThinkingBox 数据流。预计12小时
---[ ] NAME:5.2 性能基准测试 DESCRIPTION:建立性能基准，测试响应时间、内存使用、吞吐量等指标，验证性能提升。预计8小时
---[ ] NAME:5.3 监控仪表板完善 DESCRIPTION:完善 ArchitecturePerformanceMonitor，实现实时指标显示，建立告警机制。预计8小时
---[ ] NAME:5.4 文档更新和培训 DESCRIPTION:更新技术文档、API文档，编写重构总结报告，完成团队培训。预计4小时