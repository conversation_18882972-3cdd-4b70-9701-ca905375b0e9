package com.example.gymbro.core.util

/**
 * 日志标签优化工具 - 日志格式优化
 *
 * 🎯 功能特点：
 * - 优化日志标签显示，移除冗余前缀
 * - 限制标签长度，提高可读性
 * - 支持多种日志格式优化
 * - 🔄 重构：移除UUID压缩功能（现在使用CompactIdGenerator直接生成压缩ID）
 *
 * 🏗️ 使用场景：
 * - Timber日志输出优化
 * - 调试信息简化
 * - 标签格式统一
 * - 性能监控日志
 *
 * 🔧 示例用法：
 * ```kotlin
 * val optimizedTag = LogTagOptimizer.optimizeTag("com.example.gymbro.features.workout.WK-TEMPLATE")
 * // 输出: "WK-TEMPLATE"
 * ```
 */
object LogTagOptimizer {

    /**
     * 优化日志消息格式
     *
     * 🔄 重构：移除UUID压缩功能，现在主要用于消息格式优化
     *
     * @param message 原始日志消息
     * @return 优化后的日志消息
     */
    fun optimizeMessage(message: String): String {
        if (message.isBlank()) return message

        // 目前主要是直接返回，未来可以添加其他消息优化逻辑
        return message
    }

    /**
     * 优化日志标签，移除或简化冗余信息
     *
     * @param tag 原始日志标签
     * @return 优化后的日志标签
     */
    fun optimizeTag(tag: String): String {
        if (tag.isBlank()) return tag

        // 移除常见的冗余前缀
        var optimizedTag = tag
            .removePrefix("com.example.gymbro.")
            .removePrefix("features.")
            .removePrefix("WK-TEMPLATE-")
            .removePrefix("TEMPLATE-")
            .removePrefix("COA-AI-") // 简化Coach AI标签
            .removePrefix("COA-MVI-") // 简化Coach MVI标签
            .removePrefix("COA-DATA-") // 简化Coach Data标签
            .removePrefix("COA-HISTORY-") // 简化Coach History标签

        // 限制标签长度
        if (optimizedTag.length > 20) {
            optimizedTag = optimizedTag.substring(0, 17) + "..."
        }

        return optimizedTag
    }



    /**
     * 创建简化的错误标签
     *
     * @param className 类名
     * @param methodName 方法名（可选）
     * @return 简化的标签
     */
    fun createSimpleTag(className: String, methodName: String? = null): String {
        val simpleName = className.substringAfterLast(".")
        return if (methodName != null) {
            "${simpleName}.$methodName"
        } else {
            simpleName
        }
    }

    /**
     * 日志优化统计信息
     * 🔄 重构：简化统计信息，移除UUID相关统计
     */
    data class OptimizationStats(
        val originalLength: Int,
        val optimizedLength: Int,
        val compressionRatio: Double
    ) {
        fun toLogString(): String {
            return "日志优化: 原长度=${originalLength}, 优化后=${optimizedLength}, " +
                    "压缩比=${String.format("%.2f", compressionRatio)}"
        }
    }

    /**
     * 获取优化统计信息
     *
     * @param originalMessage 原始消息
     * @param optimizedMessage 优化后消息
     * @return 优化统计信息
     */
    fun getOptimizationStats(originalMessage: String, optimizedMessage: String): OptimizationStats {
        val compressionRatio = if (originalMessage.isNotEmpty()) {
            optimizedMessage.length.toDouble() / originalMessage.length.toDouble()
        } else {
            1.0
        }

        return OptimizationStats(
            originalLength = originalMessage.length,
            optimizedLength = optimizedMessage.length,
            compressionRatio = compressionRatio
        )
    }
}

/**
 * 字符串扩展函数，便于直接优化日志消息
 */
fun String.optimizeForLog(): String = LogTagOptimizer.optimizeMessage(this)

/**
 * 字符串扩展函数，便于优化日志标签
 */
fun String.optimizeTag(): String = LogTagOptimizer.optimizeTag(this)
