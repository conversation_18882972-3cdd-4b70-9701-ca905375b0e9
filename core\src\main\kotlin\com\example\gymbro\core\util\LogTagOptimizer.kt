package com.example.gymbro.core.util

/**
 * 日志标签优化工具 - 减少UUID显示次数
 *
 * 🎯 功能特点：
 * - 自动压缩日志中的UUID为6位字母数字混合ID
 * - 智能检测和替换UUID字符串
 * - 保持日志可读性和简洁性，同时保持基础安全性
 * - 支持多种UUID格式识别
 *
 * 🏗️ 使用场景：
 * - Timber日志输出优化
 * - 调试信息简化
 * - 错误日志压缩
 * - 性能监控日志
 *
 * 🔧 示例用法：
 * ```kotlin
 * val optimizedMessage = LogTagOptimizer.optimizeMessage(
 *     "模板保存成功: templateId=db63dda7-2ea7-4186-b643-942b48ee99ef"
 * )
 * // 输出: "模板保存成功: templateId=A3B9C7" (字母数字混合)
 * ```
 */
object LogTagOptimizer {

    // UUID正则表达式模式
    private val UUID_PATTERN = Regex(
        pattern = "[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}",
        option = RegexOption.IGNORE_CASE
    )

    // 简化的UUID模式（无连字符）
    private val UUID_SIMPLE_PATTERN = Regex(
        pattern = "[0-9a-fA-F]{32}",
        option = RegexOption.IGNORE_CASE
    )

    /**
     * 优化日志消息，将UUID压缩为6位字母数字混合ID
     * 
     * @param message 原始日志消息
     * @return 优化后的日志消息
     */
    fun optimizeMessage(message: String): String {
        if (message.isBlank()) return message

        var optimizedMessage = message

        // 处理标准UUID格式（带连字符）
        optimizedMessage = UUID_PATTERN.replace(optimizedMessage) { matchResult ->
            val uuid = matchResult.value
            CompactIdGenerator.generateCompactId(uuid)
        }

        // 处理简化UUID格式（无连字符，长度32）
        optimizedMessage = UUID_SIMPLE_PATTERN.replace(optimizedMessage) { matchResult ->
            val uuid = matchResult.value
            // 转换为标准UUID格式再压缩
            val standardUuid = formatSimpleUuid(uuid)
            CompactIdGenerator.generateCompactId(standardUuid)
        }

        return optimizedMessage
    }

    /**
     * 优化日志标签，移除或简化冗余信息
     * 
     * @param tag 原始日志标签
     * @return 优化后的日志标签
     */
    fun optimizeTag(tag: String): String {
        if (tag.isBlank()) return tag

        // 移除常见的冗余前缀
        var optimizedTag = tag
            .removePrefix("com.example.gymbro.")
            .removePrefix("features.")
            .removePrefix("WK-TEMPLATE-")
            .removePrefix("TEMPLATE-")

        // 限制标签长度
        if (optimizedTag.length > 20) {
            optimizedTag = optimizedTag.substring(0, 17) + "..."
        }

        return optimizedTag
    }

    /**
     * 检测字符串中是否包含UUID
     * 
     * @param text 待检测的文本
     * @return true如果包含UUID，false如果不包含
     */
    fun containsUuid(text: String): Boolean {
        return UUID_PATTERN.containsMatchIn(text) || UUID_SIMPLE_PATTERN.containsMatchIn(text)
    }

    /**
     * 提取文本中的所有UUID
     * 
     * @param text 源文本
     * @return UUID列表
     */
    fun extractUuids(text: String): List<String> {
        val uuids = mutableListOf<String>()
        
        // 提取标准格式UUID
        uuids.addAll(UUID_PATTERN.findAll(text).map { it.value })
        
        // 提取简化格式UUID
        uuids.addAll(UUID_SIMPLE_PATTERN.findAll(text).map { formatSimpleUuid(it.value) })
        
        return uuids.distinct()
    }

    /**
     * 将简化UUID格式转换为标准格式
     * 
     * @param simpleUuid 32位无连字符UUID
     * @return 标准格式UUID
     */
    private fun formatSimpleUuid(simpleUuid: String): String {
        if (simpleUuid.length != 32) return simpleUuid
        
        return "${simpleUuid.substring(0, 8)}-" +
                "${simpleUuid.substring(8, 12)}-" +
                "${simpleUuid.substring(12, 16)}-" +
                "${simpleUuid.substring(16, 20)}-" +
                "${simpleUuid.substring(20, 32)}"
    }

    /**
     * 创建简化的错误标签
     * 
     * @param className 类名
     * @param methodName 方法名（可选）
     * @return 简化的标签
     */
    fun createSimpleTag(className: String, methodName: String? = null): String {
        val simpleName = className.substringAfterLast(".")
        return if (methodName != null) {
            "${simpleName}.$methodName"
        } else {
            simpleName
        }
    }

    /**
     * 日志优化统计信息
     */
    data class OptimizationStats(
        val originalLength: Int,
        val optimizedLength: Int,
        val uuidCount: Int,
        val compressionRatio: Double
    ) {
        fun toLogString(): String {
            return "日志优化: 原长度=${originalLength}, 优化后=${optimizedLength}, " +
                    "UUID数量=${uuidCount}, 压缩比=${String.format("%.2f", compressionRatio)}"
        }
    }

    /**
     * 获取优化统计信息
     * 
     * @param originalMessage 原始消息
     * @param optimizedMessage 优化后消息
     * @return 优化统计信息
     */
    fun getOptimizationStats(originalMessage: String, optimizedMessage: String): OptimizationStats {
        val uuidCount = extractUuids(originalMessage).size
        val compressionRatio = if (originalMessage.isNotEmpty()) {
            optimizedMessage.length.toDouble() / originalMessage.length.toDouble()
        } else {
            1.0
        }

        return OptimizationStats(
            originalLength = originalMessage.length,
            optimizedLength = optimizedMessage.length,
            uuidCount = uuidCount,
            compressionRatio = compressionRatio
        )
    }
}

/**
 * 字符串扩展函数，便于直接优化日志消息
 */
fun String.optimizeForLog(): String = LogTagOptimizer.optimizeMessage(this)

/**
 * 字符串扩展函数，便于优化日志标签
 */
fun String.optimizeTag(): String = LogTagOptimizer.optimizeTag(this)