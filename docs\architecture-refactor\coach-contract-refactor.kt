// 🎯 【PLAN B 重构】Coach 模块 Contract 优化方案
// 基于 ConversationIdManager 的简化设计

package com.example.gymbro.features.coach.internal.contract

import androidx.compose.runtime.Immutable
import com.example.gymbro.core.conversation.ConversationIdManager
import com.example.gymbro.core.ui.UiEffect
import com.example.gymbro.core.ui.UiIntent
import com.example.gymbro.core.ui.UiState
import com.example.gymbro.core.ui.UiText
import com.example.gymbro.features.coach.shared.model.CoachMessage
import com.example.gymbro.features.coach.shared.model.Conversation

/**
 * 🔥 【PLAN B 优化】Coach Contract - 基于统一ID管理的简化设计
 *
 * 核心改进：
 * 1. 使用 ConversationIdManager.MessageContext 替代多个ID参数
 * 2. 简化 Effect 定义，减少跨模块传递复杂性
 * 3. 统一错误处理和状态管理
 * 4. 消除 conversationId 概念，统一使用 messageId
 */
object CoachContract {
    
    /**
     * 🔥 【简化】Coach 状态定义
     * 移除冗余字段，专注核心功能
     */
    @Immutable
    data class State(
        val currentSessionId: String = "",
        val conversations: List<Conversation> = emptyList(),
        val messages: List<CoachMessage> = emptyList(),
        val isLoading: Boolean = false,
        val isStreaming: Boolean = false,
        val error: UiText? = null,
        
        // 🔥 【新增】当前活跃的消息上下文
        val activeMessageContext: ConversationIdManager.MessageContext? = null,
        
        // UI 状态
        val inputText: String = "",
        val isInputEnabled: Boolean = true,
        val showHistory: Boolean = false,
        
        // 性能优化
        val lastMessageTimestamp: Long = 0L,
        val messageCount: Int = 0
    ) : UiState {
        
        /**
         * 获取当前会话的消息数量
         */
        val currentSessionMessageCount: Int
            get() = messages.count { it.sessionId == currentSessionId }
        
        /**
         * 检查是否有活跃的AI处理
         */
        val hasActiveAiProcessing: Boolean
            get() = isStreaming || activeMessageContext != null
        
        /**
         * 获取最后一条消息
         */
        val lastMessage: CoachMessage?
            get() = messages.lastOrNull()
    }
    
    /**
     * 🔥 【简化】Coach Intent 定义
     * 基于 MessageContext 的统一设计
     */
    sealed interface Intent : UiIntent {
        
        // 🎯 核心消息处理
        data class SendMessage(val content: String) : Intent
        data class ResendMessage(val messageId: String) : Intent
        data object StopGeneration : Intent
        
        // 🎯 会话管理
        data class StartNewSession(val userId: String) : Intent
        data class LoadSession(val sessionId: String) : Intent
        data class DeleteSession(val sessionId: String) : Intent
        data object ClearCurrentSession : Intent
        
        // 🎯 UI 交互
        data class UpdateInputText(val text: String) : Intent
        data object ToggleHistory : Intent
        data object ClearError : Intent
        
        // 🎯 系统事件
        data class MessageProcessingCompleted(
            val messageContext: ConversationIdManager.MessageContext,
            val finalContent: String
        ) : Intent
        
        data class MessageProcessingFailed(
            val messageContext: ConversationIdManager.MessageContext,
            val error: UiText
        ) : Intent
        
        // 🎯 历史记录
        data object LoadConversations : Intent
        data class SearchConversations(val query: String) : Intent
    }
    
    /**
     * 🔥 【ID统一化】优化的Effect定义
     * 基于 MessageContext 的简化传递
     */
    sealed interface Effect : UiEffect {
        
        // 🎯 AI 流处理 - 使用 MessageContext
        data class StartAiStream(
            val messageContext: ConversationIdManager.MessageContext,
            val prompt: String,
            val sessionMessages: List<CoachMessage> = emptyList()
        ) : Effect
        
        // 🎯 ThinkingBox 启动 - 简化参数
        data class LaunchThinkingBoxDisplay(
            val messageId: String  // 保持简单，只传递必要的ID
        ) : Effect
        
        // 🎯 消息保存 - 使用 MessageContext
        data class SaveMessage(
            val messageContext: ConversationIdManager.MessageContext,
            val content: String,
            val role: String, // "user" or "assistant"
            val metadata: Map<String, Any> = emptyMap()
        ) : Effect
        
        // 🎯 会话管理
        data class CreateNewSession(
            val userId: String,
            val initialMessage: String? = null
        ) : Effect
        
        data class LoadSessionHistory(val sessionId: String) : Effect
        
        // 🎯 导航和UI
        data class NavigateToHistory(val sessionId: String? = null) : Effect
        data class ShowError(val error: UiText) : Effect
        data class ShowToast(val message: UiText) : Effect
        
        // 🎯 性能优化
        data object OptimizeMessageHistory : Effect
        data object CleanupExpiredSessions : Effect
    }
}

/**
 * 🔥 【新增】Coach 业务逻辑辅助类
 * 封装复杂的业务规则和验证逻辑
 */
object CoachBusinessLogic {
    
    /**
     * 验证消息发送条件
     */
    fun canSendMessage(state: CoachContract.State, content: String): Boolean {
        return content.isNotBlank() && 
               !state.hasActiveAiProcessing && 
               state.isInputEnabled &&
               state.currentSessionId.isNotEmpty()
    }
    
    /**
     * 计算会话优先级（用于历史记录排序）
     */
    fun calculateSessionPriority(conversation: Conversation): Int {
        var priority = 0
        
        // 最近活跃的会话优先级更高
        val daysSinceUpdate = (System.currentTimeMillis() - conversation.updatedAt) / (24 * 60 * 60 * 1000)
        priority += when {
            daysSinceUpdate < 1 -> 100
            daysSinceUpdate < 7 -> 50
            daysSinceUpdate < 30 -> 20
            else -> 0
        }
        
        // 消息数量多的会话优先级更高
        priority += minOf(conversation.messageCount * 2, 50)
        
        // 未读消息的会话优先级更高
        if (conversation.unread) priority += 30
        
        return priority
    }
    
    /**
     * 生成会话标题
     */
    fun generateSessionTitle(firstMessage: String): String {
        val cleanMessage = firstMessage.trim().take(50)
        return if (cleanMessage.length < firstMessage.trim().length) {
            "$cleanMessage..."
        } else {
            cleanMessage
        }.ifBlank { "新对话" }
    }
    
    /**
     * 检查是否需要优化消息历史
     */
    fun shouldOptimizeHistory(messageCount: Int): Boolean {
        return messageCount > 100 // 超过100条消息时建议优化
    }
}

/**
 * 🔥 【新增】Coach 性能监控
 * 用于跟踪和优化性能指标
 */
data class CoachPerformanceMetrics(
    val sessionCount: Int,
    val messageCount: Int,
    val averageResponseTime: Long,
    val memoryUsage: Long,
    val lastOptimizationTime: Long
) {
    val needsOptimization: Boolean
        get() = messageCount > 200 || 
                memoryUsage > 50 * 1024 * 1024 || // 50MB
                System.currentTimeMillis() - lastOptimizationTime > 60 * 60 * 1000 // 1小时
}
