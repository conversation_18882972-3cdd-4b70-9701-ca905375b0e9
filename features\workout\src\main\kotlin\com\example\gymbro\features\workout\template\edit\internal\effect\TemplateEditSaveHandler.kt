package com.example.gymbro.features.workout.template.edit.internal.effect

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.resources.ResourceProvider
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.model.template.toDomain
import com.example.gymbro.domain.workout.usecase.template.TemplateManagementUseCase
import com.example.gymbro.domain.workout.usecase.template.TemplatesDataManager
import com.example.gymbro.features.workout.logging.WorkoutLogUtils
import com.example.gymbro.features.workout.template.cache.TemplateAutoSaveManager
import com.example.gymbro.features.workout.template.edit.config.TemplateEditConfig
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import com.example.gymbro.features.workout.template.edit.data.TemplateDataMapper
import com.example.gymbro.features.workout.template.edit.transaction.TemplateTransactionManager
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 保存操作类型枚举
 */
enum class SaveOperationType {
    CREATE, // 创建新模板
    UPDATE, // 更新现有模板
}

/**
 * TemplateEdit 保存处理器
 *
 * 🎯 职责：
 * - 统一的模板保存逻辑
 * - 自动保存管理
 * - 保存状态协调
 * - 错误处理和重试
 *
 * 🔥 重构改进：
 * - 从ViewModel中提取保存逻辑
 * - 移除过时的JSON处理（已由JSON系统处理）
 * - 简化保存流程
 * - 统一错误处理
 */
@Singleton
class TemplateEditSaveHandler @Inject constructor(
    private val templateTransactionManager: TemplateTransactionManager,
    private val autoSaveManager: TemplateAutoSaveManager,
    private val resourceProvider: ResourceProvider,
    private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
    private val templateManagementUseCase: TemplateManagementUseCase,
    private val templatesDataManager: TemplatesDataManager,
) {
    // 🔥 添加互斥锁防止并发保存操作导致的竞态条件
    private val saveMutex = Mutex()

    /**
     * 🔥 确定保存操作类型 - 增强版修复核心逻辑
     * 根据模板状态和数据库存在性判断是新建还是更新
     */
    private suspend fun determineOperationType(currentState: TemplateEditContract.State): SaveOperationType {
        val template = currentState.template

        // 情况1：无模板对象或ID为空 -> 新建
        if (template?.id.isNullOrBlank()) {
            Timber.i("🔍 [TEMPLATE-SAVE] 无模板ID，判定为新建操作")
            return SaveOperationType.CREATE
        }

        // 情况2：ID以temp_开头的临时模板 -> 新建
        if (template?.id?.startsWith("temp_") == true) {
            Timber.i("🔍 [TEMPLATE-SAVE] 临时模板ID，判定为新建操作: ${template.id}")
            return SaveOperationType.CREATE
        }

        // 情况3：增强数据库存在性检查
        return try {
            Timber.d("🔍 [TEMPLATE-SAVE] 开始检查模板存在性: ${template.id}")
            val getResult = templateManagementUseCase.getTemplate.invoke(template.id)

            when (getResult) {
                is ModernResult.Success -> {
                    // 🔥 修复：不仅检查result是否成功，还要检查数据是否真实存在且有效
                    val templateData = getResult.data
                    if (templateData != null && templateData.id.isNotBlank() && templateData.name.isNotBlank()) {
                        // 数据库中存在有效数据
                        Timber.i(
                            "🔍 [TEMPLATE-SAVE] 数据库中找到现有模板: ${templateData.name} (ID: ${templateData.id})",
                        )
                        SaveOperationType.UPDATE
                    } else {
                        // 数据库中没有有效数据
                        Timber.i("🔍 [TEMPLATE-SAVE] 数据库中未找到有效模板数据，判定为新建")
                        Timber.d(
                            "🔍 [TEMPLATE-SAVE] 查询结果: data=${templateData?.let { "${it.id}/${it.name}" } ?: "null"}",
                        )
                        SaveOperationType.CREATE
                    }
                }
                is ModernResult.Error -> {
                    Timber.w("🔍 [TEMPLATE-SAVE] 查询模板失败，判定为新建: ${getResult.error}")
                    SaveOperationType.CREATE
                }
                is ModernResult.Loading -> {
                    Timber.w("🔍 [TEMPLATE-SAVE] 查询模板返回Loading状态，默认为新建")
                    SaveOperationType.CREATE
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "🔍 [TEMPLATE-SAVE] 检查模板存在性时异常，默认为新建操作")
            SaveOperationType.CREATE
        }
    }

    /**
     * 🔥 核心保存方法 - 增强版本识别逻辑
     * 修复：正确区分新建和编辑模式，避免重复创建
     */
    suspend fun handleSave(
        currentState: TemplateEditContract.State,
        isDraft: Boolean,
        isPublishing: Boolean,
        onSuccess: (String, WorkoutTemplate) -> Unit,
        onError: (UiText) -> Unit,
    ) = saveMutex.withLock { // 🔥 使用互斥锁防止并发保存
        // 🔥 WK前缀保存链路跟踪开始
        WorkoutLogUtils.logSaveStep(
            "VALIDATE",
            currentState.templateName,
            "验证保存参数和状态",
        )

        // 取消任何待处理的自动保存
        autoSaveManager.cancelPendingSaves()
        WorkoutLogUtils.logSaveStep(
            "CANCEL",
            currentState.templateName,
            "取消待处理的自动保存",
        )

        // 🔥 修复：增强的模板识别逻辑
        val operationType = determineOperationType(currentState)

        // 🔥 添加关键调试日志：记录完整的保存操作JSON信息
        val saveOperationInfo = buildString {
            appendLine("🔥 [SAVE-OPERATION] 完整保存信息:")
            appendLine("  参数: isDraft=$isDraft, isPublishing=$isPublishing")
            appendLine("  操作类型: $operationType")
            appendLine("  模板ID: ${currentState.template?.id}")
            appendLine("  模板状态: isDraft=${currentState.template?.isDraft}, isPublished=${currentState.template?.isPublished}")
            appendLine("  模板名称: ${currentState.templateName}")
            appendLine("  动作数量: ${currentState.exercises.size}")
        }
        WorkoutLogUtils.Template.info(saveOperationInfo)

        WorkoutLogUtils.logSaveStep(
            "DETERMINE",
            currentState.templateName,
            "操作类型: $operationType",
        )

        when (operationType) {
            SaveOperationType.CREATE -> {
                WorkoutLogUtils.logSaveStep(
                    "CREATE-MODE",
                    currentState.templateName,
                    "新建模板操作",
                )
            }
            SaveOperationType.UPDATE -> {
                WorkoutLogUtils.logSaveStep(
                    "UPDATE-MODE",
                    currentState.templateName,
                    "更新模板操作",
                )
                // 🔥 修复：已发布模板不能保存为草稿
                if (isDraft && currentState.isPublished == true) {
                    WorkoutLogUtils.logSaveError(
                        "STATE-ERROR",
                        currentState.templateName,
                        "尝试将已发布模板保存为草稿",
                    )
                    onError(UiText.DynamicString("已发布的模板不能退回到草稿状态"))
                    return@withLock
                }
            }
        }

        // 🔥 修改：自动生成默认名称，而不是直接报错
        val finalTemplateName = if (currentState.templateName.isBlank()) {
            if (!isDraft) {
                // 发布时模板名称不能为空，生成默认名称
                generateDefaultTemplateName()
            } else {
                // 草稿可以使用默认名称
                generateDefaultTemplateName()
            }
        } else {
            currentState.templateName
        }

        WorkoutLogUtils.logSaveStep(
            "NAME-FINAL",
            finalTemplateName,
            "确定最终模板名称",
        )

        // 🔥 新增：新建模板时的额外验证
        val isNewTemplate = currentState.template?.id.isNullOrBlank() ||
            currentState.template?.id?.startsWith("temp_") == true
        if (isNewTemplate && currentState.exercises.isEmpty()) {
            WorkoutLogUtils.logSaveStep(
                "VALIDATE-EMPTY",
                finalTemplateName,
                "新建空模板，允许保存",
            )
        }

        // 构建Domain模型
        WorkoutLogUtils.logSaveStep(
            "BUILD",
            finalTemplateName,
            "构建Domain模型",
        )
        val templateToSave = buildTemplateFromState(currentState, isDraft, isPublishing, finalTemplateName)

        // 执行保存
        WorkoutLogUtils.logSaveStep(
            "EXECUTE",
            finalTemplateName,
            "执行保存操作",
        )
        executeSave(templateToSave, onSuccess, onError)
    }

    /**
     * 🔥 自动保存处理
     */
    suspend fun handleAutoSave(
        template: WorkoutTemplate,
        onSuccess: () -> Unit,
        onError: (UiText) -> Unit,
    ) = saveMutex.withLock { // 🔥 使用互斥锁防止与手动保存冲突
        executeSave(
            template = template.copy(isDraft = true),
            onSuccess = { _, _ -> onSuccess() },
            onError = onError,
        )
    }

    /**
     * 🔥 构建Domain模板对象
     * 简化的构建逻辑，移除复杂的JSON转换步骤
     */
    private suspend fun buildTemplateFromState(
        state: TemplateEditContract.State,
        isDraft: Boolean,
        isPublishing: Boolean,
        finalTemplateName: String, // 🔥 新增：使用最终确定的模板名称
    ): WorkoutTemplate {
        // 使用TemplateDataMapper进行状态到DTO的转换，但使用新的模板名称
        val modifiedState = state.copy(templateName = finalTemplateName)
        val templateDto = TemplateDataMapper.mapStateToDto(modifiedState)

        // 🔥 修复：获取当前用户ID，确保与查询时一致
        val currentUserId = try {
            val userIdResult = getCurrentUserIdUseCase().firstOrNull()
            Timber.tag("WK-TEMPLATE").d("🔥 [USER-ID-DEBUG] getUserIdUseCase result: $userIdResult")

            when (userIdResult) {
                is ModernResult.Success -> {
                    val userId = userIdResult.data
                    Timber.tag("WK-TEMPLATE").d("🔥 [USER-ID-DEBUG] 成功获取用户ID: $userId")
                    userId
                }
                is ModernResult.Error -> {
                    Timber.tag("WK-TEMPLATE").e("🔥 [USER-ID-DEBUG] 获取用户ID失败: ${userIdResult.error}")
                    null
                }
                is ModernResult.Loading -> {
                    Timber.tag("WK-TEMPLATE").w("🔥 [USER-ID-DEBUG] 用户ID仍在加载中")
                    null
                }
                null -> {
                    Timber.tag("WK-TEMPLATE").w("🔥 [USER-ID-DEBUG] getUserIdUseCase返回null")
                    null
                }
            }
        } catch (e: Exception) {
            Timber.tag("WK-TEMPLATE").e(e, "🔥 [USER-ID-DEBUG] 获取用户ID异常")
            null
        } ?: "system" // fallback到system作为最后手段

        Timber.tag("WK-TEMPLATE").d("🔥 [USER-ID-DEBUG] 最终使用用户ID: $currentUserId")

        // 🔥 关键修复：构建Domain模型时正确设置草稿和发布状态
        val finalTemplate = templateDto.toDomain(currentUserId).copy(
            isDraft = isDraft,
            isPublished = isPublishing,
            updatedAt = System.currentTimeMillis(),
        )

        // 🔥 记录构建完成的模板状态JSON信息
        val templateStatusInfo = buildString {
            appendLine("🔥 [TEMPLATE-STATUS] 构建完成的模板状态JSON:")
            appendLine("  最终状态: isDraft=${finalTemplate.isDraft}, isPublished=${finalTemplate.isPublished}")
            appendLine("  模板信息: id=${finalTemplate.id}, name=${finalTemplate.name}")
            appendLine("  用户ID: ${finalTemplate.userId}")
            appendLine("  动作数量: ${finalTemplate.exercises.size}")
            appendLine("  版本信息: currentVersion=${finalTemplate.currentVersion}")
            if (finalTemplate.exercises.isNotEmpty()) {
                appendLine("  动作概况:")
                finalTemplate.exercises.forEachIndexed { index, exercise ->
                    appendLine("    ${index + 1}. ${exercise.name} (${exercise.customSets.size}组)")
                }
            }
        }
        WorkoutLogUtils.Template.info(templateStatusInfo)

        // 转换为Domain对象，使用正确的用户ID
        return finalTemplate
    }

    /**
     * 🔥 执行保存操作
     */
    private suspend fun executeSave(
        template: WorkoutTemplate,
        onSuccess: (String, WorkoutTemplate) -> Unit,
        onError: (UiText) -> Unit,
    ) {
        try {
            WorkoutLogUtils.logSaveStep(
                "TRANSACTION",
                template.name,
                "开始事务保存",
            )

            // 使用事务管理器保存
            val saveResult = templateTransactionManager.saveTemplateAtomically(
                template = template,
                validateBeforeSave = true,
            )

            when (saveResult) {
                is ModernResult.Success -> {
                    val savedTemplateId = saveResult.data
                    WorkoutLogUtils.logSaveStep(
                        "COMPLETE",
                        template.name,
                        "保存成功 - ID: $savedTemplateId",
                    )

                    // 通知自动保存管理器
                    autoSaveManager.notifyManualSaveCompleted()
                    WorkoutLogUtils.logSaveStep(
                        "NOTIFY",
                        template.name,
                        "通知自动保存管理器",
                    )

                    // 🔥 修复：通知TemplatesDataManager数据已更改，确保UI能显示最新模板
                    WorkoutLogUtils.logSaveStep(
                        "DATA-REFRESH",
                        template.name,
                        "通知数据管理器刷新",
                    )
                    
                    try {
                        templatesDataManager.notifyDataChanged()
                        WorkoutLogUtils.logSaveStep(
                            "DATA-REFRESH-SUCCESS",
                            template.name,
                            "数据管理器通知成功",
                        )
                    } catch (e: Exception) {
                        // 数据通知失败不应该影响保存成功的流程
                        WorkoutLogUtils.logSaveError(
                            "DATA-REFRESH-FAILED",
                            template.name,
                            "数据管理器通知失败: ${e.message}",
                        )
                    }

                    onSuccess(savedTemplateId, template)
                }
                is ModernResult.Error -> {
                    val errorMsg = saveResult.error.uiMessage?.asString(resourceProvider) ?: "保存失败"
                    WorkoutLogUtils.logSaveError(
                        "TRANSACTION-FAILED",
                        template.name,
                        errorMsg,
                    )
                    onError(UiText.DynamicString("保存失败: $errorMsg"))
                }
                is ModernResult.Loading -> {
                    WorkoutLogUtils.logSaveError(
                        "STATE-INVALID",
                        template.name,
                        "保存操作返回Loading状态",
                    )
                    onError(UiText.DynamicString("保存状态异常"))
                }
            }
        } catch (e: Exception) {
            WorkoutLogUtils.logSaveError(
                "EXCEPTION",
                template.name,
                "保存过程异常: ${e.message}",
            )
            onError(UiText.DynamicString("保存过程中发生异常: ${e.message}"))
        }
    }

    /**
     * 🔥 生成默认模板名称 - P1修复
     * 实现递增序号命名逻辑：
     * - 第一个模板："训练模板"
     * - 后续模板："训练模板1"、"训练模板2"...
     * - 查询现有模板，找到最大序号并+1
     */
    private suspend fun generateDefaultTemplateName(): String {
        return try {
            // 获取当前用户的所有模板
            val templatesResult = templateManagementUseCase.GetTemplates().invoke(Unit).firstOrNull()

            when (templatesResult) {
                is ModernResult.Success -> {
                    val existingTemplates = templatesResult.data

                    // 过滤出以"训练模板"开头的模板名称
                    val templateNames = existingTemplates
                        .map { it.name }
                        .filter { it.startsWith(TemplateEditConfig.DEFAULT_TEMPLATE_NAME) }

                    if (templateNames.isEmpty()) {
                        // 没有现有模板，返回基础名称
                        TemplateEditConfig.DEFAULT_TEMPLATE_NAME
                    } else {
                        // 提取序号并找到最大值
                        val maxNumber = templateNames.mapNotNull { name ->
                            when {
                                name == TemplateEditConfig.DEFAULT_TEMPLATE_NAME -> 0 // "训练模板" = 序号0
                                name.startsWith(TemplateEditConfig.DEFAULT_TEMPLATE_NAME) -> {
                                    // 提取"训练模板"后面的数字
                                    val suffix = name.substring(TemplateEditConfig.DEFAULT_TEMPLATE_NAME.length)
                                    suffix.toIntOrNull()
                                }
                                else -> null
                            }
                        }.maxOrNull() ?: 0

                        // 生成下一个序号
                        val nextNumber = maxNumber + 1
                        if (nextNumber == 1) {
                            // 如果下一个是1，检查"训练模板"是否已存在
                            if (templateNames.contains(TemplateEditConfig.DEFAULT_TEMPLATE_NAME)) {
                                "${TemplateEditConfig.DEFAULT_TEMPLATE_NAME}1"
                            } else {
                                TemplateEditConfig.DEFAULT_TEMPLATE_NAME
                            }
                        } else {
                            "${TemplateEditConfig.DEFAULT_TEMPLATE_NAME}$nextNumber"
                        }
                    }
                }
                is ModernResult.Error -> {
                    Timber.w("获取模板列表失败，使用时间戳后缀: ${templatesResult.error}")
                    // 降级方案：使用时间戳后缀确保唯一性
                    val timestamp = System.currentTimeMillis()
                    val suffix = (timestamp % 10000).toString().padStart(4, '0')
                    "${TemplateEditConfig.DEFAULT_TEMPLATE_NAME}$suffix"
                }
                else -> {
                    // Loading状态或其他情况，使用默认名称
                    TemplateEditConfig.DEFAULT_TEMPLATE_NAME
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "生成默认模板名称异常，使用基础名称")
            TemplateEditConfig.DEFAULT_TEMPLATE_NAME
        }
    }

    /**
     * 🔥 清理资源
     */
    fun cleanup() {
        // 取消所有待处理的保存操作
        autoSaveManager.cancelPendingSaves()
        Timber.d("🧹 TemplateEditSaveHandler 清理完成")
    }
}
