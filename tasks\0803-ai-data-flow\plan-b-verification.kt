/**
 * Plan B重构验证脚本
 * 
 * 验证AI数据流ID统一化重构的核心功能
 */

import com.example.gymbro.core.conversation.ConversationIdManager
import com.example.gymbro.core.conversation.MessageContext
import com.example.gymbro.core.network.output.OutputToken
import com.example.gymbro.core.network.detector.ContentType
import com.example.gymbro.core.util.CompactIdGenerator

/**
 * 验证ConversationIdManager核心功能
 */
fun verifyConversationIdManager() {
    println("🔍 验证ConversationIdManager...")
    
    val compactIdGenerator = CompactIdGenerator()
    val manager = ConversationIdManager(compactIdGenerator)
    
    // 1. 验证消息上下文创建
    val sessionId = "test-session-123"
    val messageContext = manager.createMessageContext(sessionId)
    
    assert(messageContext.sessionId == sessionId) { "SessionId不匹配" }
    assert(messageContext.messageId.isNotEmpty()) { "MessageId为空" }
    assert(messageContext.compactId.length == 6) { "CompactId长度不正确" }
    assert(messageContext.timestamp > 0) { "时间戳无效" }
    
    println("✅ 消息上下文创建验证通过")
    
    // 2. 验证智能匹配功能
    val foundContext = manager.findBestMatchingMessage(messageContext.compactId)
    assert(foundContext != null) { "智能匹配失败" }
    assert(foundContext!!.messageId == messageContext.messageId) { "匹配结果不正确" }
    
    println("✅ 智能匹配功能验证通过")
    
    // 3. 验证统计功能
    val stats = manager.getStatistics()
    assert(stats.totalMessages >= 1) { "统计信息不正确" }
    assert(stats.totalSessions >= 1) { "会话统计不正确" }
    
    println("✅ 统计功能验证通过")
}

/**
 * 验证OutputToken数据结构
 */
fun verifyOutputToken() {
    println("🔍 验证OutputToken数据结构...")
    
    val messageId = "test-message-456"
    val content = "测试内容"
    val contentType = ContentType.JSON_SSE
    
    // 创建OutputToken
    val outputToken = OutputToken(
        content = content,
        messageId = messageId,
        contentType = contentType,
        timestamp = System.currentTimeMillis()
    )
    
    // 验证主要字段
    assert(outputToken.content == content) { "内容不匹配" }
    assert(outputToken.messageId == messageId) { "MessageId不匹配" }
    assert(outputToken.contentType == contentType) { "ContentType不匹配" }
    
    // 验证向后兼容性
    @Suppress("DEPRECATION")
    assert(outputToken.conversationId == messageId) { "向后兼容性失败" }
    
    println("✅ OutputToken数据结构验证通过")
}

/**
 * 验证ID传递链路
 */
fun verifyIdPassingChain() {
    println("🔍 验证ID传递链路...")
    
    val compactIdGenerator = CompactIdGenerator()
    val manager = ConversationIdManager(compactIdGenerator)
    
    // 1. Coach层：创建消息上下文
    val sessionId = "chain-test-session"
    val messageContext = manager.createMessageContext(sessionId)
    
    // 2. Core-Network层：创建OutputToken
    val outputToken = OutputToken(
        content = "测试链路内容",
        messageId = messageContext.messageId,
        contentType = ContentType.JSON_SSE,
        timestamp = System.currentTimeMillis()
    )
    
    // 3. ThinkingBox层：验证ID一致性
    assert(outputToken.messageId == messageContext.messageId) { "ID传递链路断裂" }
    
    // 4. 验证会话ID可以通过messageId获取
    val retrievedContext = manager.getMessageContext(messageContext.messageId)
    assert(retrievedContext != null) { "无法通过messageId获取上下文" }
    assert(retrievedContext!!.sessionId == sessionId) { "SessionId传递失败" }
    
    println("✅ ID传递链路验证通过")
}

/**
 * 验证性能指标
 */
fun verifyPerformance() {
    println("🔍 验证性能指标...")
    
    val compactIdGenerator = CompactIdGenerator()
    val manager = ConversationIdManager(compactIdGenerator)
    
    // 创建大量消息测试性能
    val sessionId = "performance-test-session"
    val messageCount = 1000
    val startTime = System.currentTimeMillis()
    
    val messageContexts = mutableListOf<MessageContext>()
    repeat(messageCount) {
        val context = manager.createMessageContext(sessionId)
        messageContexts.add(context)
    }
    
    val creationTime = System.currentTimeMillis() - startTime
    val avgCreationTime = creationTime.toDouble() / messageCount
    
    assert(avgCreationTime < 1.0) { "ID创建性能不达标: ${avgCreationTime}ms/条" }
    println("✅ ID创建性能: ${avgCreationTime}ms/条")
    
    // 测试查询性能
    val queryStartTime = System.currentTimeMillis()
    messageContexts.forEach { context ->
        val found = manager.getMessageContext(context.messageId)
        assert(found != null) { "查询失败" }
    }
    
    val queryTime = System.currentTimeMillis() - queryStartTime
    val avgQueryTime = queryTime.toDouble() / messageCount
    
    assert(avgQueryTime < 0.1) { "查询性能不达标: ${avgQueryTime}ms/条" }
    println("✅ 查询性能: ${avgQueryTime}ms/条")
    
    // 测试智能匹配性能
    val matchStartTime = System.currentTimeMillis()
    messageContexts.forEach { context ->
        val found = manager.findBestMatchingMessage(context.compactId)
        assert(found != null) { "智能匹配失败" }
    }
    
    val matchTime = System.currentTimeMillis() - matchStartTime
    val avgMatchTime = matchTime.toDouble() / messageCount
    
    assert(avgMatchTime < 0.1) { "智能匹配性能不达标: ${avgMatchTime}ms/条" }
    println("✅ 智能匹配性能: ${avgMatchTime}ms/条")
}

/**
 * 主验证函数
 */
fun main() {
    println("🚀 开始Plan B重构验证...")
    println("=" * 50)
    
    try {
        verifyConversationIdManager()
        verifyOutputToken()
        verifyIdPassingChain()
        verifyPerformance()
        
        println("=" * 50)
        println("🎉 Plan B重构验证全部通过！")
        println("✅ ConversationIdManager功能正常")
        println("✅ OutputToken数据结构正确")
        println("✅ ID传递链路完整")
        println("✅ 性能指标达标")
        println("✅ 向后兼容性保持")
        
    } catch (e: AssertionError) {
        println("❌ 验证失败: ${e.message}")
        throw e
    } catch (e: Exception) {
        println("❌ 验证异常: ${e.message}")
        throw e
    }
}
