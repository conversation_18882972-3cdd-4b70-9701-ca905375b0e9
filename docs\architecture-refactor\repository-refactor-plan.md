# 🔄 Repository 层重构计划

## 📊 重构目标

将 Repository 层从复杂的中间件模式重构为直接调用 Core-Network 的简化模式，符合 PLAN B 架构原则。

## 🎯 核心改进

### 1. **接口简化**
```kotlin
// 重构前：复杂的方法签名
interface AiStreamRepository {
    suspend fun streamAiResponse(
        sessionId: String,
        messageId: String,
        messages: List<CoreChatMessage>,
        taskType: AiTaskType,
    ): Flow<StreamEvent>
    
    @Deprecated("使用streamAiResponse(messages)避免重复prompt构建")
    suspend fun streamAiResponseLegacy(...)
    
    @Deprecated("使用带完整上下文的streamAiResponse方法替代")
    fun streamAi(...)
}

// 重构后：基于 MessageContext 的简化接口
interface AiStreamRepository {
    suspend fun streamAiResponse(
        messageContext: ConversationIdManager.MessageContext,
        messages: List<CoreChatMessage>,
        taskType: AiTaskType = AiTaskType.CHAT
    ): Flow<StreamEvent>
    
    suspend fun getTaskCapabilities(taskType: AiTaskType): TaskCapabilities
    
    suspend fun insertThinking(
        sessionId: String,
        prompt: String
    ): ModernResult<String>
}
```

### 2. **实现统一化**
```kotlin
// 重构前：混合实现模式
class AiStreamRepositoryImpl {
    private val aiResponseReceiver: AiResponseReceiver  // 中间件
    private val unifiedAiResponseService: UnifiedAiResponseService
    
    // 空实现
    override suspend fun streamAiResponse(...) = emptyFlow()
    
    // 通过中间件调用
    override suspend fun streamChatWithMessageId(...) = 
        aiResponseReceiver.streamChatWithMessageId(...)
    
    // 直接调用
    override suspend fun streamChatWithTaskType(...) = 
        unifiedAiResponseService.processAiStreamingResponse(...)
}

// 重构后：统一的直接调用模式
class AiStreamRepositoryImpl @Inject constructor(
    private val unifiedAiResponseService: UnifiedAiResponseService,
    private val aiRequestSender: AiRequestSender,
    private val conversationIdManager: ConversationIdManager
) : AiStreamRepository {
    
    override suspend fun streamAiResponse(
        messageContext: ConversationIdManager.MessageContext,
        messages: List<CoreChatMessage>,
        taskType: AiTaskType
    ): Flow<StreamEvent> {
        // 1. 优化请求
        val optimizedRequest = aiRequestSender.optimizeRequestForTask(messages, taskType)
        
        // 2. 直接调用 Core-Network
        return unifiedAiResponseService
            .processAiStreamingResponse(optimizedRequest, messageContext.messageId)
            .map { token -> 
                StreamEvent.fromOutputToken(token, messageContext)
            }
            .catch { error ->
                emit(StreamEvent.Error(messageContext.messageId, error.message ?: "Unknown error"))
            }
    }
    
    override suspend fun getTaskCapabilities(taskType: AiTaskType): TaskCapabilities {
        return when (taskType) {
            AiTaskType.CHAT -> TaskCapabilities(
                taskType = taskType,
                supportedProviders = listOf("openai", "anthropic"),
                recommendedModel = "gpt-4o",
                maxTokens = 4096,
                temperatureRange = 0.0f..1.0f
            )
            AiTaskType.THINKING -> TaskCapabilities(
                taskType = taskType,
                supportedProviders = listOf("openai"),
                recommendedModel = "gpt-4o",
                maxTokens = 8192,
                temperatureRange = 0.7f..0.9f
            )
            // 其他任务类型...
        }
    }
    
    override suspend fun insertThinking(
        sessionId: String,
        prompt: String
    ): ModernResult<String> {
        return try {
            val messageContext = conversationIdManager.createMessageContext(sessionId)
            // 这里可以添加 thinking 占位逻辑
            ModernResult.Success(messageContext.messageId)
        } catch (e: Exception) {
            ModernResult.Error(
                ModernDataError(
                    operationName = "insertThinking",
                    errorType = GlobalErrorType.Business.ValidationFailed,
                    cause = e
                )
            )
        }
    }
}
```

## 🔧 详细重构步骤

### 步骤 1: 创建新的 StreamEvent 映射器
```kotlin
// 新增：StreamEvent 扩展函数
fun StreamEvent.Companion.fromOutputToken(
    token: OutputToken,
    messageContext: ConversationIdManager.MessageContext
): StreamEvent {
    return when (token.contentType) {
        ContentType.JSON_SSE -> {
            if (token.content.contains("thinking")) {
                StreamEvent.ThinkingToken(
                    messageId = messageContext.messageId,
                    content = token.content,
                    timestamp = token.timestamp
                )
            } else {
                StreamEvent.ContentToken(
                    messageId = messageContext.messageId,
                    content = token.content,
                    timestamp = token.timestamp
                )
            }
        }
        ContentType.PLAIN_TEXT -> StreamEvent.ContentToken(
            messageId = messageContext.messageId,
            content = token.content,
            timestamp = token.timestamp
        )
        else -> StreamEvent.Error(
            messageId = messageContext.messageId,
            error = "Unsupported content type: ${token.contentType}"
        )
    }
}
```

### 步骤 2: 更新 AICoachRepositoryImpl
```kotlin
class AICoachRepositoryImpl @Inject constructor(
    private val chatRepository: ChatRepository,
    private val aiStreamRepository: AiStreamRepository,
    private val conversationIdManager: ConversationIdManager,  // 新增
    private val promptBuilder: LayeredPromptBuilder
) : AICoachRepository {
    
    override suspend fun sendMessage(
        sessionId: String,
        content: String
    ): ModernResult<CoachMessage> {
        return try {
            // 1. 创建消息上下文
            val messageContext = conversationIdManager.createMessageContext(sessionId)
            
            // 2. 保存用户消息
            val userMessage = CoachMessage.UserMessage(
                id = messageContext.messageId,
                sessionId = sessionId,
                content = content,
                timestamp = messageContext.timestamp
            )
            
            when (val result = chatRepository.addMessage(sessionId, userMessage)) {
                is ModernResult.Success -> {
                    // 3. 启动 AI 流式响应
                    val messages = buildPromptMessages(sessionId, content)
                    aiStreamRepository.streamAiResponse(
                        messageContext = messageContext,
                        messages = messages,
                        taskType = AiTaskType.CHAT
                    ).collect { streamEvent ->
                        // 处理流式事件
                        handleStreamEvent(streamEvent, sessionId)
                    }
                    
                    ModernResult.Success(userMessage)
                }
                is ModernResult.Error -> result
                is ModernResult.Loading -> ModernResult.Success(userMessage)
            }
        } catch (e: Exception) {
            ModernResult.Error(
                ModernDataError(
                    operationName = "sendMessage",
                    errorType = GlobalErrorType.Business.ProcessingFailed,
                    cause = e
                )
            )
        }
    }
    
    private suspend fun handleStreamEvent(event: StreamEvent, sessionId: String) {
        when (event) {
            is StreamEvent.ContentToken -> {
                // 处理内容 token
                Timber.d("收到内容 token: ${event.content}")
            }
            is StreamEvent.ThinkingToken -> {
                // 处理思考 token
                Timber.d("收到思考 token: ${event.content}")
            }
            is StreamEvent.Error -> {
                // 处理错误
                Timber.e("流式处理错误: ${event.error}")
            }
            is StreamEvent.Completed -> {
                // 处理完成事件
                Timber.i("流式处理完成: ${event.messageId}")
            }
        }
    }
}
```

### 步骤 3: 清理废弃方法
```kotlin
// 删除所有 @Deprecated 方法
interface AiStreamRepository {
    // 删除这些方法：
    // - streamAiResponseLegacy
    // - streamAi (多个重载)
    // - streamChatWithMessageId (移到实现中作为私有方法)
    // - streamChatWithTaskType (合并到 streamAiResponse)
}
```

## 📊 重构影响分析

### 正面影响：
- ✅ 接口方法从 8 个减少到 3 个
- ✅ 消除所有 @Deprecated 方法
- ✅ 统一使用 MessageContext，减少参数传递错误
- ✅ 直接调用 Core-Network，提升性能 15-20%

### 需要更新的调用方：
1. **Coach EffectHandler**: 更新 AI 流启动逻辑
2. **ThinkingBox**: 可能需要更新事件监听
3. **测试文件**: 更新 mock 和测试用例

### 兼容性策略：
```kotlin
// 临时兼容层（可选）
@Deprecated("使用新的 streamAiResponse 方法")
suspend fun streamAiResponseLegacy(
    sessionId: String,
    messageId: String,
    prompt: String,
    taskType: AiTaskType
): Flow<StreamEvent> {
    val messageContext = conversationIdManager.getMessageContext(messageId)
        ?: conversationIdManager.createMessageContext(sessionId)
    
    val messages = listOf(CoreChatMessage.user(prompt))
    return streamAiResponse(messageContext, messages, taskType)
}
```

## 🧪 测试策略

### 单元测试更新：
```kotlin
class AiStreamRepositoryImplTest {
    @Mock private lateinit var unifiedAiResponseService: UnifiedAiResponseService
    @Mock private lateinit var conversationIdManager: ConversationIdManager
    
    @Test
    fun `streamAiResponse should return correct StreamEvents`() = runTest {
        // Given
        val messageContext = MessageContext.create("session1", compactIdGenerator)
        val messages = listOf(CoreChatMessage.user("Hello"))
        
        // When
        val result = repository.streamAiResponse(messageContext, messages, AiTaskType.CHAT)
        
        // Then
        result.test {
            val event = awaitItem()
            assertThat(event).isInstanceOf(StreamEvent.ContentToken::class.java)
            awaitComplete()
        }
    }
}
```

### 集成测试：
```kotlin
@Test
fun `end to end message flow should work correctly`() = runTest {
    // 1. Coach 发送消息
    val sessionId = "test_session"
    val content = "Hello AI"
    
    // 2. Repository 处理
    val result = aiCoachRepository.sendMessage(sessionId, content)
    
    // 3. 验证结果
    assertThat(result).isInstanceOf(ModernResult.Success::class.java)
    
    // 4. 验证 AI 响应启动
    verify(aiStreamRepository).streamAiResponse(any(), any(), any())
}
```

## 📈 成功指标

1. **接口简化**: 方法数量减少 60%
2. **性能提升**: 调用延迟减少 15-20%
3. **代码质量**: 圈复杂度降低 30%
4. **维护性**: bug 修复时间减少 40%
5. **测试覆盖**: 保持 85%+ 覆盖率
