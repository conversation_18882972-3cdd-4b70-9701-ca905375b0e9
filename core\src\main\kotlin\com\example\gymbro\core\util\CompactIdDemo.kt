package com.example.gymbro.core.util

/**
 * CompactId演示程序
 * 展示UUID压缩为字母数字混合格式的功能
 */
object CompactIdDemo {
    
    fun demonstrateCompactIds() {
        println("🎯 UUID压缩演示 - 字母数字混合格式")
        println("=" * 50)
        
        val testUuids = listOf(
            "db63dda7-2ea7-4186-b643-942b48ee99ef",
            "0cff5ba5-b11e-4d09-9766-cc494f3ba88a", 
            "f98fd322-b2c0-4739-bdad-72453b5d6fb7",
            "a1b2c3d4-e5f6-7890-abcd-1234567890ab",
            "10e653a0-7b9b-4a3b-96f7-b57748c3f89a"
        )
        
        println("📋 测试UUID列表:")
        testUuids.forEachIndexed { index, uuid ->
            val compactId = CompactIdGenerator.generateCompactId(uuid)
            println("${index + 1}. $uuid -> $compactId")
            
            // 验证格式
            val validChars = "23456789ABCDEFGHJKLMNPQRSTUVWXYZ"
            val isValid = compactId.length == 6 && compactId.all { it in validChars }
            println("   ✅ 格式验证: ${if (isValid) "通过" else "失败"}")
            
            // 测试反向查找
            val retrievedUuid = CompactIdGenerator.getOriginalUuid(compactId)
            val canReverse = retrievedUuid == uuid
            println("   🔄 反向查找: ${if (canReverse) "成功" else "失败"}")
            println()
        }
        
        // 展示日志优化效果
        println("📝 日志优化效果演示:")
        val sampleLogMessages = listOf(
            "模板保存成功: templateId=db63dda7-2ea7-4186-b643-942b48ee99ef",
            "开始处理训练会话: sessionId=0cff5ba5-b11e-4d09-9766-cc494f3ba88a",
            "AI响应生成完成: requestId=f98fd322-b2c0-4739-bdad-72453b5d6fb7"
        )
        
        sampleLogMessages.forEach { message ->
            val optimized = LogTagOptimizer.optimizeMessage(message)
            println("原始: $message")
            println("优化: $optimized")
            val saved = message.length - optimized.length
            println("节省: ${saved}字符 (${String.format("%.1f", saved.toDouble() / message.length * 100)}%)")
            println()
        }
        
        // 统计信息
        val stats = CompactIdGenerator.getStats()
        println("📊 压缩统计:")
        println(stats.toLogString())
    }
}

// 扩展运算符重载（用于字符串重复）
private operator fun String.times(n: Int): String = this.repeat(n)