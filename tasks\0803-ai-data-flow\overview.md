# AI对话数据流架构分析任务总览

## 任务背景
深度分析GymBro项目的AI对话数据流架构，评估统一事件流架构与现有架构优化的可行性。

## 核心需求
1. **架构现状梳理**：分析AI对话管理、网络统一响应、思考过程可视化模块
2. **ID管理机制分析**：梳理messageId、sessionId、conversationId的传递路径
3. **两套设想评估**：统一事件流 vs 现有架构优化

## 关键文件清单

### 模块文档
features/coach/README.md
core-network/README.md  
features/thinkingbox/README.md
docs/MODULE_TREE.md
docs/INTERFACE.md

### Coach模块核心实现
features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/AiCoachViewModel.kt
features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/contract/AiCoachContract.kt
features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/reducer/AiCoachReducer.kt
features/coach/src/main/kotlin/com/example/gymbro/features/coach/aicoach/handler/AiCoachEffectHandler.kt

### Core-Network统一响应层
core-network/src/main/kotlin/com/example/gymbro/core/network/service/UnifiedAiResponseService.kt
core-network/src/main/kotlin/com/example/gymbro/core/network/detector/ContentType.kt
core-network/src/main/kotlin/com/example/gymbro/core/network/buffer/PerformanceMonitor.kt

### Data层AI响应处理
data/src/main/kotlin/com/example/gymbro/data/coach/service/AiResponseReceiver.kt
data/src/main/kotlin/com/example/gymbro/data/coach/repository/AiStreamRepositoryImpl.kt
data/src/main/kotlin/com/example/gymbro/data/coach/repository/AICoachRepositoryImpl.kt

### Domain层流式事件
domain/src/main/kotlin/com/example/gymbro/domain/coach/model/StreamEvent.kt
domain/src/main/kotlin/com/example/gymbro/domain/coach/repository/AiStreamRepository.kt

### ThinkingBox可视化模块
features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/api/ThinkingBoxContext.kt
features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/api/ThinkingBoxRequest.kt
features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/api/ConversationTurn.kt
features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/reducer/ThinkingBoxReducer.kt

### 共享模型定义
shared-models/src/main/kotlin/com/example/gymbro/shared/models/workout/WorkoutTemplateDto.kt

## 分析重点
1. **现有ID传递链路**：多轮对话中ID的职责分工
2. **AI响应解析**：thinking段落与final文本的处理机制
3. **事件流设计**：当前Coach→Network→ThinkingBox的数据流向
4. **可行性评估**：两种架构设想的技术实现难度

## 预期产出
1. 现状分析报告
2. 两套初步规划方案
3. 技术可行性评估
4. 推荐实施路径

生成时间：2025-08-03T14:00:00+08:00