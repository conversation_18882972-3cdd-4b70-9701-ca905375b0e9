# Coach模块日志使用指南

## 📋 概述

本指南详细说明了Coach模块中日志系统的使用方法、标签规范和最佳实践。

🎯 **设计原则**: 参考ThinkingBox和Workout模块标准，使用COA前缀统一管理Coach模块日志。

## 🏗️ 架构组件

### CoachLogTree - 专用日志树
- **位置**: `features/coach/logging/CoachLogTree.kt`
- **功能**: 专门处理Coach模块日志，支持AI流聚合
- **特点**: 继承自Timber.DebugTree，自动过滤和聚合高频日志

### CoachLogUtils - 日志工具类
- **位置**: `features/coach/logging/CoachLogUtils.kt`
- **功能**: 提供统一的日志标签和快速日志方法
- **特点**: 静态工具类，配合CoachLogTree使用

## 🏷️ 标签规范

### 标准COA前缀标签

#### 核心业务标签
```kotlin
COA-CORE        // 核心业务流程
COA-ERROR       // 错误日志
COA-STATE       // 状态管理
COA-EFFECT      // 副作用处理
```

#### AI对话流标签
```kotlin
COA-AI-FLOW     // AI对话流程
COA-AI-REQUEST  // AI请求
COA-AI-RESPONSE // AI响应
COA-AI-STREAM   // AI流式处理
COA-AI-TOKEN    // Token处理（高频，会聚合）
```

#### MVI架构标签
```kotlin
COA-MVI         // MVI架构通用
COA-MVI-INTENT  // Intent处理
COA-MVI-REDUCER // Reducer逻辑
COA-MVI-EFFECT  // Effect处理
COA-MVI-STATE   // State更新
```

#### 数据层标签
```kotlin
COA-DATA        // 数据层通用
COA-DATA-REPO   // Repository操作
COA-DATA-USECASE // UseCase执行
COA-DATA-MAPPER // 数据映射
COA-DATA-CACHE  // 缓存操作
```

#### UI层标签
```kotlin
COA-UI          // UI层通用
COA-UI-COMPOSE  // Compose UI
COA-UI-INTERACTION // 用户交互
COA-UI-NAVIGATION  // 导航
COA-UI-ANIMATION   // 动画
```

#### 性能监控标签
```kotlin
COA-PERF        // 性能监控通用
COA-PERF-RENDER // 渲染性能
COA-PERF-NETWORK // 网络性能
COA-PERF-MEMORY // 内存使用
COA-PERF-CPU    // CPU使用
```

### 兼容旧标签（逐步迁移）
```kotlin
COACH-ERROR     // → COA-ERROR
COACH-NEW       // → COA-CORE
PROMPT-BUILDER  // → COA-AI-FLOW
COACH-USECASE   // → COA-DATA-USECASE
COACH-REPO      // → COA-DATA-REPO
```

## 🔧 使用方法

### 1. 基础日志使用
```kotlin
import timber.log.Timber
import com.example.gymbro.features.coach.logging.CoachLogUtils

// 直接使用Timber + 标签
Timber.tag(CoachLogUtils.TAG_CORE).i("AI对话开始")
Timber.tag(CoachLogUtils.TAG_AI_FLOW).d("处理用户请求: $request")
Timber.tag(CoachLogUtils.TAG_ERROR).e("AI请求失败", exception)
```

### 2. 快速日志方法
```kotlin
// AI相关日志
CoachLogUtils.Ai.info("开始AI对话")
CoachLogUtils.Ai.request("发送请求: $requestId")
CoachLogUtils.Ai.response("收到响应: $responseId")
CoachLogUtils.Ai.error("AI处理失败", exception)

// MVI相关日志
CoachLogUtils.Mvi.intent("处理Intent: LoadConversation")
CoachLogUtils.Mvi.reducer("状态转换: Loading -> Success")
CoachLogUtils.Mvi.effect("执行Effect: NavigateToChat")

// 数据层日志
CoachLogUtils.Data.repo("保存对话记录")
CoachLogUtils.Data.usecase("执行GetConversationUseCase")

// UI层日志
CoachLogUtils.Ui.info("渲染聊天界面")
CoachLogUtils.Ui.debug("用户点击发送按钮")
```

### 3. 统一流程跟踪
```kotlin
// AI对话流程跟踪
val requestId = CompactIdGenerator.generateId("req")
CoachLogUtils.logAiFlow("START", "开始AI对话", requestId)
CoachLogUtils.logAiFlow("PROCESS", "处理用户输入", requestId, "输入长度: ${input.length}")
CoachLogUtils.logAiFlow("COMPLETE", "AI响应完成", requestId)

// MVI流程跟踪
CoachLogUtils.logMviFlow("INTENT", "LoadConversation", "conversationId=$id")
CoachLogUtils.logMviFlow("REDUCER", "状态更新", "Loading -> Success")
CoachLogUtils.logMviFlow("EFFECT", "导航到聊天页面")

// 错误跟踪
CoachLogUtils.logAiError("NETWORK", "AI请求失败", error.message, requestId)
```

### 4. 性能监控
```kotlin
// 性能测量
CoachLogUtils.PerformanceTracker.measureTime("AI响应处理") {
    processAiResponse(response)
}

// 手动性能日志
CoachLogUtils.Performance.info("⏱️ AI响应时间: ${duration}ms")
CoachLogUtils.Performance.warn("⚠️ 响应时间过长: ${duration}ms")
```

## 🎯 日志聚合功能

### 高频日志自动聚合
以下标签的日志会自动聚合，避免刷屏：
- `COA-AI-TOKEN`: 100 token输出一次
- `COA-AI-STREAM`: 50 token输出一次  
- `TOKEN-FLOW`: 150 token输出一次

### 聚合配置
```kotlin
// 在CoachLogTree中自动处理
// 高频日志会缓存，达到阈值后批量输出
// 错误级别日志仍然立即输出
```

## 📝 最佳实践

### 1. 标签选择原则
- **核心业务**: 使用 `COA-CORE`
- **AI相关**: 使用 `COA-AI-*` 系列
- **MVI架构**: 使用 `COA-MVI-*` 系列
- **错误处理**: 使用 `COA-ERROR`
- **性能监控**: 使用 `COA-PERF-*` 系列

### 2. 日志级别规范
- **ERROR**: 错误和异常
- **WARN**: 警告和潜在问题
- **INFO**: 重要业务流程
- **DEBUG**: 调试信息
- **VERBOSE**: 详细跟踪（高频日志）

### 3. 消息格式规范
```kotlin
// ✅ 好的日志格式
CoachLogUtils.Ai.info("🔥 [AI-REQUEST] 发送请求: requestId=$requestId, prompt长度=${prompt.length}")
CoachLogUtils.Mvi.reducer("🔄 [STATE-UPDATE] Loading -> Success: conversationId=$id")

// ❌ 避免的格式
Timber.d("request sent") // 信息不足
Timber.i("状态更新") // 缺少具体信息
```

### 4. 错误处理规范
```kotlin
// ✅ 完整的错误日志
try {
    processAiRequest(request)
} catch (e: Exception) {
    CoachLogUtils.logAiError("PROCESS", "AI请求处理失败", e.message ?: "未知错误", requestId)
    throw e
}

// ✅ 使用快速方法
CoachLogUtils.Ai.error("AI响应解析失败: ${response}", exception)
```

## 🔗 相关文件

- `CoachLogTree.kt` - 专用日志树实现
- `CoachLogUtils.kt` - 日志工具类
- `core/logging/LoggingConfig.kt` - 全局日志配置
- `core/logging/TimberManager.kt` - 日志管理器

---

**更新日期**: 2025-01-26  
**版本**: v1.0 (COA前缀标准)  
**维护者**: GymBro开发团队
