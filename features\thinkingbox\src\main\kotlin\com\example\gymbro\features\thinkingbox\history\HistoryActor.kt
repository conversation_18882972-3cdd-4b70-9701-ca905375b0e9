package com.example.gymbro.features.thinkingbox.history

import com.example.gymbro.domain.thinkingbox.repository.HistoryRepository
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * HistoryActor - 独立的History写入中介者（729方案3.md 方案B）
 *
 * 🎯 核心职责：
 * - 监听ThinkingBox的DomainEvent Effect
 * - 专职处理History写入，与UI/业务逻辑解耦
 * - 实现方案B：TB → DomainEvent → HistoryActor → Repository
 *
 * 🔥 架构优势：
 * - 单一职责：只负责History存储
 * - 高解耦：TB不直接依赖HistoryRepository
 * - 独立性：可以独立测试和维护
 * - 扩展性：易于添加其他存储逻辑
 */
@Singleton
class HistoryActor @Inject constructor(
    private val historyRepository: HistoryRepository,
) {

    // 🔥 【729方案9优化】debounce机制，保证UI先行
    private val pendingThinkingWrites =
        ConcurrentHashMap<String, ThinkingBoxContract.Effect.NotifyHistoryThinking>()
    private val pendingFinalWrites =
        ConcurrentHashMap<String, ThinkingBoxContract.Effect.NotifyHistoryFinal>()
    private lateinit var actorScope: CoroutineScope

    /**
     * 🔥 【729方案9优化】启动Effect监听，支持debounce机制
     * 在应用启动时调用，开始监听ThinkingBox的Effect流
     *
     * @param effectFlow ThinkingBox的Effect流
     * @param scope 协程作用域
     */
    fun initialize(
        effectFlow: Flow<ThinkingBoxContract.Effect>,
        scope: CoroutineScope,
    ) {
        this.actorScope = scope
        Timber.i("TB-History: 🚀 [初始化] 开始监听ThinkingBox Effect流")

        effectFlow
            .onEach { effect ->
                handleEffect(effect)
            }
            .launchIn(scope)
    }

    /**
     * 🔥 【Effect处理】处理ThinkingBox发出的Effect
     * 只处理History相关的Effect，其他Effect忽略
     */
    private suspend fun handleEffect(effect: ThinkingBoxContract.Effect) {
        when (effect) {
            is ThinkingBoxContract.Effect.NotifyHistoryThinking -> {
                handleThinkingHistoryWrite(effect)
            }

            is ThinkingBoxContract.Effect.NotifyHistoryFinal -> {
                handleFinalHistoryWrite(effect)
            }

            else -> {
                // 忽略非History相关的Effect
            }
        }
    }

    /**
     * 🔥 【729方案9优化】处理思考过程的History写入，支持debounce机制
     * 对应</thinking>断点触发的写入，100ms debounce保证UI先行
     */
    private suspend fun handleThinkingHistoryWrite(
        effect: ThinkingBoxContract.Effect.NotifyHistoryThinking,
    ) {
        // 🔥 【729方案9优化】使用debounce机制，保证UI先行
        pendingThinkingWrites[effect.messageId] = effect

        actorScope.launch {
            delay(effect.debounceMs) // 默认100ms

            // 检查是否还是最新的写入请求
            val latestEffect = pendingThinkingWrites[effect.messageId]
            if (latestEffect == effect) {
                // 执行实际的写入操作
                performThinkingHistoryWrite(effect)
                pendingThinkingWrites.remove(effect.messageId)
            }
        }
    }

    /**
     * 🔥 【729方案9优化】执行实际的思考History写入 - 支持多轮对话
     */
    private suspend fun performThinkingHistoryWrite(
        effect: ThinkingBoxContract.Effect.NotifyHistoryThinking,
    ) {
        try {
            // 🔥 【Plan B重构】移除sessionId引用，通过ConversationIdManager获取
            Timber.i("TB-History: 💾 [思考History] 开始写入: messageId=${effect.messageId}")
            Timber.d("TB-History: 💾 [思考History] 内容长度: ${effect.thinkingMarkdown.length}")

            // 调用Repository保存思考过程
            try {
                historyRepository.insertThinkingFinal(
                    messageId = effect.messageId,
                    markdown = effect.thinkingMarkdown,
                )
                Timber.i("TB-History: ✅ [思考History] 写入成功: messageId=${effect.messageId}")
            } catch (e: Exception) {
                Timber.e("TB-History: ❌ [思考History] 写入失败: messageId=${effect.messageId}, error=${e.message}")
            }
        } catch (e: Exception) {
            Timber.e("TB-History: 💥 [思考History] 写入异常: messageId=${effect.messageId}, error=${e.message}")
        }
    }

    /**
     * 🔥 【729方案9优化】处理最终内容的History写入，支持debounce机制
     * 对应</final>断点触发的写入，100ms debounce保证UI先行
     */
    private suspend fun handleFinalHistoryWrite(
        effect: ThinkingBoxContract.Effect.NotifyHistoryFinal,
    ) {
        // 🔥 【729方案9优化】使用debounce机制，保证UI先行
        pendingFinalWrites[effect.messageId] = effect

        actorScope.launch {
            delay(100L) // 100ms debounce

            // 检查是否还是最新的写入请求
            val latestEffect = pendingFinalWrites[effect.messageId]
            if (latestEffect == effect) {
                // 执行实际的写入操作
                performFinalHistoryWrite(effect)
                pendingFinalWrites.remove(effect.messageId)
            }
        }
    }

    /**
     * 🔥 【729方案9优化】执行实际的最终History写入 - 支持多轮对话
     */
    private suspend fun performFinalHistoryWrite(
        effect: ThinkingBoxContract.Effect.NotifyHistoryFinal,
    ) {
        try {
            // 🔥 【Plan B重构】移除sessionId引用，通过ConversationIdManager获取
            Timber.i("TB-History: 💾 [最终History] 开始写入: messageId=${effect.messageId}")
            Timber.d("TB-History: 💾 [最终History] 内容长度: ${effect.finalMarkdown.length}")

            // 调用Repository保存最终内容
            try {
                historyRepository.insertThinkingFinal(
                    messageId = effect.messageId,
                    markdown = effect.finalMarkdown,
                )
                Timber.i("TB-History: ✅ [最终History] 写入成功: messageId=${effect.messageId}")
            } catch (e: Exception) {
                Timber.e("TB-History: ❌ [最终History] 写入失败: messageId=${effect.messageId}, error=${e.message}")
            }
        } catch (e: Exception) {
            Timber.e("TB-History: 💥 [最终History] 写入异常: messageId=${effect.messageId}, error=${e.message}")
        }
    }
}
