package com.example.gymbro.features.coach.history.logging

import timber.log.Timber
import com.example.gymbro.core.util.CompactIdGenerator

/**
 * Coach History模块专用日志工具类
 * 
 * 🎯 遵循COA-子包-*标签规范
 * - 使用COA-HISTORY-*前缀标识history子模块日志
 * - 统一接入core的timber log动态管理
 * - 支持对话历史记录的完整生命周期跟踪
 *
 * 🔧 使用示例：
 * ```kotlin
 * Timber.tag(HistoryLogUtils.TAG_CORE).i("历史记录加载开始")
 * Timber.tag(HistoryLogUtils.TAG_PAGING).d("分页加载: page=${page}")
 * HistoryLogUtils.logHistoryFlow("LOAD", "加载对话历史", sessionId)
 * ```
 */
object HistoryLogUtils {

    // === COA-HISTORY标签定义 ===

    /** 核心历史记录业务 */
    const val TAG_CORE = "COA-HISTORY-CORE"

    /** 分页加载相关 */
    const val TAG_PAGING = "COA-HISTORY-PAGING"

    /** 数据库操作 */
    const val TAG_DATABASE = "COA-HISTORY-DB"

    /** UI渲染相关 */
    const val TAG_UI = "COA-HISTORY-UI"

    /** 搜索功能 */
    const val TAG_SEARCH = "COA-HISTORY-SEARCH"

    /** 缓存管理 */
    const val TAG_CACHE = "COA-HISTORY-CACHE"

    /** 同步操作 */
    const val TAG_SYNC = "COA-HISTORY-SYNC"

    /** 错误处理 */
    const val TAG_ERROR = "COA-HISTORY-ERROR"

    /** 性能监控 */
    const val TAG_PERFORMANCE = "COA-HISTORY-PERF"

    /** 调试信息 */
    const val TAG_DEBUG = "COA-HISTORY-DEBUG"

    // === MVI架构标签 ===

    /** MVI Intent处理 */
    const val TAG_MVI_INTENT = "COA-HISTORY-MVI-INTENT"

    /** MVI Reducer逻辑 */
    const val TAG_MVI_REDUCER = "COA-HISTORY-MVI-REDUCER"

    /** MVI Effect处理 */
    const val TAG_MVI_EFFECT = "COA-HISTORY-MVI-EFFECT"

    /** MVI State更新 */
    const val TAG_MVI_STATE = "COA-HISTORY-MVI-STATE"

    // === 数据层标签 ===

    /** Repository操作 */
    const val TAG_DATA_REPO = "COA-HISTORY-DATA-REPO"

    /** UseCase执行 */
    const val TAG_DATA_USECASE = "COA-HISTORY-DATA-USECASE"

    /** 数据映射 */
    const val TAG_DATA_MAPPER = "COA-HISTORY-DATA-MAPPER"

    // === 向后兼容标签 ===
    /** @deprecated 使用 TAG_CORE 替代 */
    const val TAG_HISTORY_ACTOR = "HISTORY-ACTOR"

    // === 统一日志方法 ===

    /**
     * 🔥 历史记录流程跟踪
     */
    fun logHistoryFlow(step: String, description: String, sessionId: String = "", detail: String = "") {
        val message = buildString {
            append("🔥 [COA-HISTORY-$step] $description")
            if (sessionId.isNotEmpty()) append(" - sessionId=$sessionId")
            if (detail.isNotEmpty()) append(" - $detail")
        }
        Timber.tag(TAG_CORE).i(message)
    }

    /**
     * 🔥 历史记录错误跟踪
     */
    fun logHistoryError(step: String, description: String, error: String, sessionId: String = "") {
        val message = buildString {
            append("❌ [COA-HISTORY-$step] $description - ERROR: $error")
            if (sessionId.isNotEmpty()) append(" - sessionId=$sessionId")
        }
        Timber.tag(TAG_ERROR).e(message)
    }

    /**
     * 🔥 分页加载跟踪
     */
    fun logPagingFlow(action: String, page: Int, pageSize: Int, totalItems: Int = -1) {
        val message = buildString {
            append("📄 [COA-HISTORY-PAGING] $action - page=$page, size=$pageSize")
            if (totalItems >= 0) append(", total=$totalItems")
        }
        Timber.tag(TAG_PAGING).i(message)
    }

    /**
     * 🔥 数据库操作跟踪
     */
    fun logDatabaseOperation(operation: String, table: String, recordCount: Int, success: Boolean) {
        val status = if (success) "✅" else "❌"
        val message = "$status [COA-HISTORY-DB] $operation - table=$table, records=$recordCount"
        
        if (success) {
            Timber.tag(TAG_DATABASE).i(message)
        } else {
            Timber.tag(TAG_ERROR).e(message)
        }
    }

    /**
     * 🔥 搜索操作跟踪
     */
    fun logSearchOperation(query: String, resultCount: Int, duration: Long) {
        val message = "🔍 [COA-HISTORY-SEARCH] 搜索完成 - query='$query', results=$resultCount, duration=${duration}ms"
        Timber.tag(TAG_SEARCH).i(message)
    }

    /**
     * 🔥 缓存操作跟踪
     */
    fun logCacheOperation(operation: String, key: String, hit: Boolean = true) {
        val status = if (hit) "🎯" else "❌"
        val message = "$status [COA-HISTORY-CACHE] $operation - key=$key"
        Timber.tag(TAG_CACHE).d(message)
    }

    /**
     * 🔥 UI渲染跟踪
     */
    fun logUiRendering(component: String, itemCount: Int, renderTime: Long = -1) {
        val message = buildString {
            append("🎨 [COA-HISTORY-UI] $component 渲染 - items=$itemCount")
            if (renderTime >= 0) append(", time=${renderTime}ms")
        }
        Timber.tag(TAG_UI).d(message)
    }

    /**
     * 🔥 快速日志方法 - 核心业务
     */
    object Core {
        fun info(message: String) = Timber.tag(TAG_CORE).i(message)
        fun debug(message: String) = Timber.tag(TAG_CORE).d(message)
        fun error(message: String, throwable: Throwable? = null) = Timber.tag(TAG_ERROR).e(throwable, message)
        fun warn(message: String) = Timber.tag(TAG_CORE).w(message)
    }

    /**
     * 🔥 快速日志方法 - 分页相关
     */
    object Paging {
        fun info(message: String) = Timber.tag(TAG_PAGING).i(message)
        fun debug(message: String) = Timber.tag(TAG_PAGING).d(message)
        fun error(message: String, throwable: Throwable? = null) = Timber.tag(TAG_ERROR).e(throwable, message)
        fun warn(message: String) = Timber.tag(TAG_PAGING).w(message)
        
        fun loadStart(page: Int) = info("📄 开始加载第${page}页")
        fun loadSuccess(page: Int, itemCount: Int) = info("📄 第${page}页加载成功: ${itemCount}项")
        fun loadError(page: Int, error: String) = error("📄 第${page}页加载失败: $error")
    }

    /**
     * 🔥 快速日志方法 - 数据库相关
     */
    object Database {
        fun info(message: String) = Timber.tag(TAG_DATABASE).i(message)
        fun debug(message: String) = Timber.tag(TAG_DATABASE).d(message)
        fun error(message: String, throwable: Throwable? = null) = Timber.tag(TAG_ERROR).e(throwable, message)
        fun warn(message: String) = Timber.tag(TAG_DATABASE).w(message)
        
        fun query(sql: String, resultCount: Int) = debug("🗃️ 查询执行: $sql, 结果: ${resultCount}条")
        fun insert(table: String, recordCount: Int) = info("🗃️ 插入记录: $table, ${recordCount}条")
        fun update(table: String, recordCount: Int) = info("🗃️ 更新记录: $table, ${recordCount}条")
        fun delete(table: String, recordCount: Int) = info("🗃️ 删除记录: $table, ${recordCount}条")
    }

    /**
     * 🔥 快速日志方法 - UI相关
     */
    object Ui {
        fun info(message: String) = Timber.tag(TAG_UI).i(message)
        fun debug(message: String) = Timber.tag(TAG_UI).d(message)
        fun error(message: String, throwable: Throwable? = null) = Timber.tag(TAG_ERROR).e(throwable, message)
        fun warn(message: String) = Timber.tag(TAG_UI).w(message)
        
        fun render(component: String, itemCount: Int) = debug("🎨 渲染组件: $component, ${itemCount}项")
        fun interaction(action: String, target: String) = debug("👆 用户交互: $action -> $target")
        fun navigation(from: String, to: String) = info("🧭 导航: $from -> $to")
    }

    /**
     * 🔥 快速日志方法 - 搜索相关
     */
    object Search {
        fun info(message: String) = Timber.tag(TAG_SEARCH).i(message)
        fun debug(message: String) = Timber.tag(TAG_SEARCH).d(message)
        fun error(message: String, throwable: Throwable? = null) = Timber.tag(TAG_ERROR).e(throwable, message)
        fun warn(message: String) = Timber.tag(TAG_SEARCH).w(message)
        
        fun query(query: String) = debug("🔍 搜索查询: '$query'")
        fun result(query: String, count: Int) = info("🔍 搜索结果: '$query' -> ${count}条")
        fun filter(filter: String, count: Int) = debug("🔍 应用过滤器: $filter -> ${count}条")
    }

    /**
     * 🔥 快速日志方法 - MVI相关
     */
    object Mvi {
        fun info(message: String) = Timber.tag(TAG_MVI_INTENT).i(message)
        fun debug(message: String) = Timber.tag(TAG_MVI_INTENT).d(message)
        fun error(message: String, throwable: Throwable? = null) = Timber.tag(TAG_ERROR).e(throwable, message)
        fun warn(message: String) = Timber.tag(TAG_MVI_INTENT).w(message)
        
        fun intent(intent: String) = debug("🎯 Intent: $intent")
        fun reducer(from: String, to: String) = debug("🔄 Reducer: $from -> $to")
        fun effect(effect: String) = debug("⚡ Effect: $effect")
        fun state(state: String) = debug("📊 State: $state")
    }

    /**
     * 🔥 性能测量工具
     */
    object PerformanceTracker {
        fun measureTime(operation: String, block: () -> Unit) {
            val startTime = System.currentTimeMillis()
            try {
                block()
                val duration = System.currentTimeMillis() - startTime
                Timber.tag(TAG_PERFORMANCE).i("⏱️ [$operation] 执行时间: ${duration}ms")
            } catch (e: Exception) {
                val duration = System.currentTimeMillis() - startTime
                Timber.tag(TAG_ERROR).e("⏱️ [$operation] 执行失败: ${duration}ms", e)
                throw e
            }
        }
        
        fun <T> measureTimeWithResult(operation: String, block: () -> T): T {
            val startTime = System.currentTimeMillis()
            return try {
                val result = block()
                val duration = System.currentTimeMillis() - startTime
                Timber.tag(TAG_PERFORMANCE).i("⏱️ [$operation] 执行时间: ${duration}ms")
                result
            } catch (e: Exception) {
                val duration = System.currentTimeMillis() - startTime
                Timber.tag(TAG_ERROR).e("⏱️ [$operation] 执行失败: ${duration}ms", e)
                throw e
            }
        }
    }
}
