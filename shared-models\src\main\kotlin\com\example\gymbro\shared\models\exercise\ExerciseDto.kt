package com.example.gymbro.shared.models.exercise

import kotlinx.serialization.Serializable

/**
 * 健身动作数据传输对象 (纯DTO)
 *
 * 用于模块间数据传输，不包含业务逻辑或UI依赖
 * 支持统一JSON数据中心的动作库管理功能
 * ID规则：
 * - 官方动作：`"off_{hash(name)}"`
 * - 用户自定义：`"u_{userId}_{uuid}"`
 */
@Serializable
data class ExerciseDto(
    /**
     * 动作唯一标识符
     */
    val id: String,

    /**
     * 动作名称 (纯字符串)
     */
    val name: String,

    /**
     * 动作描述/说明 (纯字符串)
     */
    val description: String = "",

    /**
     * 主要训练肌肉群
     */
    val muscleGroup: MuscleGroup = MuscleGroup.OTHER,

    /**
     * 动作分类 (新增字段，统一JSON数据中心要求)
     */
    val category: ExerciseCategory = ExerciseCategory.OTHER,

    /**
     * 使用的器械类型列表
     */
    val equipment: List<Equipment> = emptyList(),

    /**
     * 动作图片URL
     */
    val imageUrl: String? = null,

    /**
     * 动作视频URL
     */
    val videoUrl: String? = null,

    /**
     * 默认组数
     */
    val defaultSets: Int = 3,

    /**
     * 默认次数
     */
    val defaultReps: Int = 10,

    /**
     * 默认重量
     */
    val defaultWeight: Float = 0f,

    /**
     * 动作步骤说明 (纯字符串列表)
     */
    val steps: List<String> = emptyList(),

    /**
     * 动作提示/技巧 (纯字符串列表)
     */
    val tips: List<String> = emptyList(),

    /**
     * 动作指导说明 (纯字符串列表)
     */
    val instructions: List<String> = emptyList(),

    /**
     * 是否为用户自定义动作
     */
    val isCustom: Boolean = false,

    /**
     * 用户ID (自定义动作专用)
     */
    val userId: String? = null,

    /**
     * 是否收藏
     */
    val isFavorite: Boolean = false,

    /**
     * 难度等级 (1-5) - 保持向后兼容
     * 1: 初学者  2: 入门  3: 中级  4: 高级  5: 专业
     */
    val difficultyLevel: Int = 1,

    /**
     * 难度等级枚举 (新增字段，统一JSON数据中心要求)
     */
    val difficulty: DifficultyLevel? = null,

    /**
     * 消耗卡路里
     */
    val calories: Int = 0,

    /**
     * 主要目标肌肉群
     */
    val targetMuscles: List<MuscleGroup> = emptyList(),

    /**
     * 次要训练肌肉群
     */
    val secondaryMuscles: List<MuscleGroup> = emptyList(),

    /**
     * 创建时间戳
     */
    val createdAt: Long = System.currentTimeMillis(),

    /**
     * 最后更新时间戳
     */
    val updatedAt: Long = System.currentTimeMillis(),

    /**
     * 创建者用户ID
     */
    val createdByUserId: String = "",

    /**
     * 创建者姓名 (新增字段，动作库功能)
     */
    val authorName: String? = null,

    /**
     * 是否为官方动作
     */
    val isOfficial: Boolean = true,

    /**
     * 动作来源 (新增字段，统一JSON数据中心要求)
     */
    val source: ExerciseSource = ExerciseSource.OFFICIAL,

    /**
     * 是否公开分享 (新增字段，动作库功能)
     */
    val isPublic: Boolean = false,

    /**
     * 使用次数/流行度 (新增字段，动作库功能)
     */
    val popularity: Int = 0,

    /**
     * 标签列表 (新增字段，动作库功能)
     */
    val tags: List<String> = emptyList(),

    /**
     * BGE向量表示 (128维)
     * 用于语义搜索和相似度匹配
     */
    val embedding: FloatArray? = null,

    /**
     * 动作媒体资源
     */
    val media: ExerciseMediaDto? = null,

    /**
     * 备注
     */
    val notes: String = "",

    /**
     * 向后兼容：目标组数列表
     * 用于旧代码兼容
     */
    val targetSets: List<ExerciseSetDto> = emptyList(),

    /**
     * 向后兼容：已完成组数列表
     * 用于旧代码兼容
     */
    val completedSets: List<ExerciseSetDto> = emptyList(),

    /**
     * 向后兼容：休息时间（秒）
     * 用于旧代码兼容
     */
    val restTimeSeconds: Int = 60,
) {
    /**
     * 向后兼容的构造函数
     * 支持旧代码的使用方式
     */
    constructor(
        id: String,
        name: String,
        imageUrl: String? = null,
        videoUrl: String? = null,
        targetSets: List<ExerciseSetDto> = emptyList(),
        completedSets: List<ExerciseSetDto> = emptyList(),
        restTimeSeconds: Int = 60,
        notes: String? = null,
    ) : this(
        id = id,
        name = name,
        imageUrl = imageUrl,
        videoUrl = videoUrl,
        notes = notes ?: "",
        targetSets = targetSets,
        completedSets = completedSets,
        restTimeSeconds = restTimeSeconds,
    )

    /**
     * 重写equals以处理FloatArray比较
     */
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ExerciseDto

        if (id != other.id) return false
        if (name != other.name) return false
        if (embedding != null) {
            if (other.embedding == null) return false
            if (!embedding.contentEquals(other.embedding)) return false
        } else if (other.embedding != null) return false

        return true
    }

    /**
     * 重写hashCode以处理FloatArray
     */
    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + name.hashCode()
        result = 31 * result + (embedding?.contentHashCode() ?: 0)
        return result
    }

    companion object {
        /**
         * 生成官方动作ID
         */
        fun generateOfficialId(name: String): String {
            return "off_${name.hashCode().toString(16)}"
        }

        /**
         * 生成用户自定义动作ID
         * 🎯 使用压缩ID提高可读性
         */
        fun generateUserCustomId(userId: String): String {
            return "u_${userId}_${java.util.UUID.randomUUID().toString().take(8)}"
        }

        /**
         * 判断是否为官方动作ID
         */
        fun isOfficialId(id: String): Boolean {
            return id.startsWith("off_")
        }

        /**
         * 判断是否为用户自定义动作ID
         */
        fun isUserCustomId(id: String): Boolean {
            return id.startsWith("u_")
        }

        /**
         * 创建用户自定义动作 (新增方法，动作库功能)
         */
        fun createUserCustomExercise(
            userId: String,
            name: String,
            description: String,
            muscleGroup: MuscleGroup,
            category: ExerciseCategory,
            authorName: String,
            isPublic: Boolean = false,
        ): ExerciseDto {
            return ExerciseDto(
                id = generateUserCustomId(userId),
                name = name,
                description = description,
                muscleGroup = muscleGroup,
                category = category,
                isCustom = true,
                userId = userId,
                createdByUserId = userId,
                authorName = authorName,
                isOfficial = false,
                source = ExerciseSource.USER_CUSTOM,
                isPublic = isPublic,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis(),
            )
        }

        /**
         * 从难度等级数字转换为枚举
         */
        fun difficultyLevelToEnum(level: Int): DifficultyLevel? {
            return when (level) {
                1 -> DifficultyLevel.BEGINNER
                2 -> DifficultyLevel.NOVICE
                3 -> DifficultyLevel.INTERMEDIATE
                4 -> DifficultyLevel.ADVANCED
                5 -> DifficultyLevel.EXPERT
                else -> null
            }
        }

        /**
         * 从难度等级枚举转换为数字
         */
        fun difficultyEnumToLevel(difficulty: DifficultyLevel): Int {
            return difficulty.level
        }

        /**
         * 从SessionExerciseDto转换为ExerciseDto
         * 迁移自workout包，保持兼容性
         */
        fun fromSessionExercise(
            sessionExerciseId: String,
            exerciseName: String,
            imageUrl: String? = null,
            videoUrl: String? = null,
            targetSets: List<ExerciseSetDto> = emptyList(),
            completedSets: List<ExerciseSetDto> = emptyList(),
            restTimeSeconds: Int = 60,
            notes: String? = null,
        ): ExerciseDto {
            return ExerciseDto(
                id = sessionExerciseId,
                name = exerciseName,
                description = notes ?: "",
                muscleGroup = MuscleGroup.OTHER, // 默认肌肉群，Session转换时无法确定具体肌肉群
                imageUrl = imageUrl,
                videoUrl = videoUrl,
                isCustom = false, // Session来源的动作默认不是自定义
                notes = notes ?: "",
            )
        }
    }

    /**
     * 转换为Session兼容的数据结构
     * 迁移自workout包，保持兼容性
     */
    fun toSessionExerciseData(): Map<String, Any> {
        return mapOf(
            "exerciseId" to id,
            "name" to name,
            "imageUrl" to (imageUrl ?: ""),
            "videoUrl" to (videoUrl ?: ""),
            "restTimeSeconds" to 60, // 默认休息时间
            "notes" to (description.takeIf { it.isNotBlank() } ?: ""),
        )
    }
}
