package com.example.gymbro.features.workout.template

import androidx.compose.runtime.Immutable
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import com.example.gymbro.core.arch.mvi.AppIntent
import com.example.gymbro.core.arch.mvi.UiEffect
import com.example.gymbro.core.arch.mvi.UiState
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.domain.workout.model.TemplateDraft
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.features.workout.shared.components.drag.DragState
import com.example.gymbro.features.workout.shared.components.drag.DragConfig
import com.example.gymbro.shared.models.workout.TemplateExerciseDto
import com.example.gymbro.shared.models.workout.WorkoutTemplateDto
import com.example.gymbro.shared.models.workout.TemplateState

/**
 * 训练模板Contract - P4阶段完整版
 *
 * 🎯 P4阶段新增功能:
 * - 搜索切换和状态管理
 * - 自动保存状态指示
 * - 滑动删除和拖动排序支持
 * - 崩溃恢复机制
 * - 完整的MVI 2.0架构支持
 *
 * 🏗️ 架构原则:
 * - Clean Architecture + MVI 2.0模式
 * - 四数据库架构集成
 * - designSystem主题令牌使用
 */
object TemplateContract {

    @Immutable
    data class State(
        // === 核心数据 ===
        val templates: List<WorkoutTemplateDto> = emptyList(),
        val drafts: List<TemplateDraft> = emptyList(),
        val currentTemplate: WorkoutTemplateDto? = null,
        val editingTemplate: WorkoutTemplateDto? = null,

        // 🎯 新增：草稿状态的模板（通过"保存草稿"创建的WorkoutTemplateDto）
        val draftTemplates: List<WorkoutTemplateDto> = emptyList(),

        // === UI状态 ===
        val currentTab: TemplateTab = TemplateTab.TEMPLATES,
        val isLoading: Boolean = false,
        val isLoadingDrafts: Boolean = false,
        val isSaving: Boolean = false,
        val isDeleting: Boolean = false,
        val isExporting: Boolean = false,

        // === 搜索和筛选 (P4新增) ===
        val searchQuery: String = "",
        val selectedCategory: String = "",
        val filteredTemplates: List<WorkoutTemplateDto> = emptyList(),
        val filteredDrafts: List<TemplateDraft> = emptyList(),
        val showSearchField: Boolean = false,
        val isSearching: Boolean = false,

        // === 自动保存机制 (P4增强) ===
        val cachedChanges: Map<String, WorkoutTemplateDto> = emptyMap(),
        val hasUnsavedChanges: Boolean = false,
        val lastSaveTime: Long? = null,
        val autoSaveEnabled: Boolean = true,
        val cacheAvailable: Boolean = false,
        val autoSaveState: AutoSaveState = AutoSaveState.Inactive,

        // === 错误处理 ===
        val error: UiText? = null,
        val saveError: UiText? = null,
        val networkError: UiText? = null,
        val lastFailedOperation: Intent? = null,

        // === 动作选择 ===
        val showExerciseSelector: Boolean = false,
        val selectedExercises: List<Exercise> = emptyList(),
        val exerciseSelectorMode: ExerciseSelectorMode = ExerciseSelectorMode.SINGLE,

        // === 手势和动画状态 (P4新增) ===
        val swipeStates: Map<String, SwipeState> = emptyMap(),
        val animatingItems: Set<String> = emptySet(),
        val reorderingMode: Boolean = false,

        // === 拖拽排序状态 - 统一拖拽组件集成 ===
        val templateDragState: DragState<WorkoutTemplateDto> = DragState(),
        val draftDragState: DragState<TemplateDraft> = DragState(),
        val dragConfig: DragConfig = DragConfig.Material3,

        // === 排序状态 ===
        val templateOrder: Map<String, Int> = emptyMap(),
        val draftOrder: Map<String, Int> = emptyMap(),

        // === 对话框状态 ===
        val showDeleteDialog: Boolean = false,
        val showSaveConfirmDialog: Boolean = false,
        val showCacheRestoreDialog: Boolean = false,
        val deleteTargetId: String? = null,

        // === 导航状态 ===
        val navigationPending: Boolean = false,
        val pendingNavigation: NavigationTarget? = null,
    ) : UiState

    sealed interface Intent : AppIntent {
        // === 模板管理 ===
        object LoadTemplates : Intent
        object RefreshTemplates : Intent
        data class CreateTemplate(val name: String = "新训练模板") : Intent
        data class EditTemplate(val templateId: String) : Intent

        // 🔥 修复：移除主界面的保存逻辑，避免与 TemplateEditScreen 冲突
        // data class UpdateTemplate(val template: WorkoutTemplateDto) : Intent // 已移除
        // data class SaveTemplate(val template: WorkoutTemplateDto) : Intent // 已移除
        data class DeleteTemplate(val templateId: String) : Intent
        data class DuplicateTemplate(val templateId: String) : Intent

        // === 草稿管理 ===
        object LoadDrafts : Intent
        object RefreshDrafts : Intent
        data class CreateNewDraft(val name: String = "新训练草稿") : Intent
        data class EditDraft(val draftId: String) : Intent
        data class SaveDraft(val draft: TemplateDraft) : Intent
        data class DeleteDraft(val draftId: String) : Intent
        data class PromoteDraft(val draftId: String) : Intent

        // === 动作管理 ===
        object ShowExerciseSelector : Intent
        data class SetExerciseSelectorMode(val mode: ExerciseSelectorMode) : Intent
        data class AddExercise(val exercise: Exercise) : Intent
        data class AddExercises(val exercises: List<Exercise>) : Intent
        data class RemoveExercise(val exerciseId: String) : Intent
        data class UpdateExercise(val exercise: TemplateExerciseDto) : Intent
        data class ReorderExercises(val fromIndex: Int, val toIndex: Int) : Intent
        object HideExerciseSelector : Intent

        // === 搜索和筛选 (P4新增) ===
        data class SearchTemplates(val query: String) : Intent
        data class FilterByCategory(val category: String) : Intent
        object ClearFilters : Intent
        object ToggleSearch : Intent
        object ShowSearch : Intent
        object HideSearch : Intent

        // === 手势操作 (P4新增) ===
        data class StartSwipe(val exerciseId: String) : Intent
        data class UpdateSwipe(val exerciseId: String, val offset: Float) : Intent
        data class CompleteSwipe(val exerciseId: String) : Intent
        data class CancelSwipe(val exerciseId: String) : Intent

        // === 拖拽排序操作 - 统一拖拽组件集成 ===
        data class StartTemplateDragUnified(
            val template: WorkoutTemplateDto,
            val startIndex: Int,
            val startPosition: Offset = Offset.Zero
        ) : Intent
        data class UpdateTemplateDragUnified(
            val currentPosition: Offset,
            val targetIndex: Int
        ) : Intent
        data class CompleteTemplateDragUnified(
            val fromIndex: Int,
            val toIndex: Int
        ) : Intent
        object CancelTemplateDragUnified : Intent

        data class StartDraftDragUnified(
            val draft: TemplateDraft,
            val startIndex: Int,
            val startPosition: Offset = Offset.Zero
        ) : Intent
        data class UpdateDraftDragUnified(
            val currentPosition: Offset,
            val targetIndex: Int
        ) : Intent
        data class CompleteDraftDragUnified(
            val fromIndex: Int,
            val toIndex: Int
        ) : Intent
        object CancelDraftDragUnified : Intent

        // === 兼容性拖拽操作 (向后兼容) ===
        data class StartDragTemplate(val templateId: String, val currentIndex: Int) : Intent
        data class UpdateDragTemplate(val fromIndex: Int, val toIndex: Int) : Intent
        data class EndDragTemplate(val templateId: String, val finalIndex: Int) : Intent
        object CancelDragTemplate : Intent
        data class StartDragDraft(val draftId: String, val currentIndex: Int) : Intent
        data class UpdateDragDraft(val fromIndex: Int, val toIndex: Int) : Intent
        data class EndDragDraft(val draftId: String, val finalIndex: Int) : Intent
        object CancelDragDraft : Intent

        // === 一键置顶操作 ===
        data class MoveTemplateToTop(val templateId: String) : Intent
        data class MoveDraftToTop(val draftId: String) : Intent

        // === 自动保存管理 (P4增强) ===
        object SaveToCache : Intent
        object RestoreFromCache : Intent
        data class ClearCache(val templateId: String) : Intent
        object EnableAutoSave : Intent
        object DisableAutoSave : Intent
        data class ShowCacheRestoreDialog(val cachedTemplates: List<WorkoutTemplateDto>) : Intent
        object HideCacheRestoreDialog : Intent
        data class RestoreSpecificCache(val templateId: String) : Intent
        data class AutoSaveStateChanged(val state: AutoSaveState) : Intent

        // === 错误处理 ===
        object ClearError : Intent

        // === 导航状态管理 ===
        object ResetNavigationState : Intent
        object ClearSaveError : Intent
        object ClearNetworkError : Intent
        object RetryLastOperation : Intent
        data class HandleError(val error: UiText, val operation: Intent? = null) : Intent

        // === 对话框管理 ===
        object ShowDeleteDialog : Intent
        object HideDeleteDialog : Intent
        data class ConfirmDelete(val templateId: String) : Intent
        object ShowSaveConfirmDialog : Intent
        object HideSaveConfirmDialog : Intent
        object ConfirmSave : Intent
        object DiscardChanges : Intent

        // === Tab切换 ===
        data class SwitchTab(val tab: TemplateTab) : Intent

        // === 导航 ===
        data class NavigateToTemplateDetail(val templateId: String) : Intent
        data class NavigateToEditTemplate(val templateId: String) : Intent
        object NavigateToNewTemplate : Intent
        data class NavigateToDraftEditor(val draftId: String) : Intent
        object NavigateToCreateDraft : Intent
        object NavigateBack : Intent

        // === 内部状态更新 ===
        data class TemplatesLoaded(val templates: List<WorkoutTemplateDto>) : Intent
        data class DraftsLoaded(val drafts: List<TemplateDraft>) : Intent
        data class TemplateSaved(val template: WorkoutTemplateDto) : Intent
        data class TemplateDeleted(val templateId: String) : Intent
        data class CacheRestored(val templates: List<WorkoutTemplateDto>) : Intent
        data class SaveError(val error: UiText) : Intent
        data class LoadError(val error: UiText) : Intent
        data class NetworkError(val error: UiText) : Intent
        object SaveSuccess : Intent
        object DeleteSuccess : Intent
        data class OnTemplatesLoaded(
            val result:
            ModernResult<List<WorkoutTemplate>>,
        ) : Intent

        // === 兼容性Intent ===
        data class LoadTemplate(val templateId: String) : Intent
        data class StartWorkout(val templateId: String) : Intent
        data class SelectTemplate(val templateId: String) : Intent
        data class ToggleTemplateFavorite(val templateId: String) : Intent
        data class StartWorkoutFromTemplate(val templateId: String) : Intent
        data class CreateNewTemplate(val name: String = "") : Intent

        // 🔥 修复：移除主界面的动作编辑逻辑，这些应该只在 TemplateEditScreen 中处理
        // data class AddExerciseToTemplate(val exerciseId: String) : Intent // 已移除
        // data class RemoveExerciseFromTemplate(val exerciseId: String) : Intent // 已移除
        // data class UpdateTemplateExercise(val exercise: TemplateExerciseDto) : Intent // 已移除
        data class ShowDeleteDraftDialog(val draftId: String) : Intent
    }

    sealed interface Effect : UiEffect {
        // === 导航副作用 ===
        data class NavigateToTemplateDetail(val templateId: String) : Effect
        data class NavigateToEditTemplate(val templateId: String) : Effect
        object NavigateToNewTemplate : Effect
        data class StartWorkout(val templateId: String) : Effect
        data class NavigateToDraftEditor(val draftId: String) : Effect
        object NavigateToCreateDraft : Effect
        data class NavigateToExerciseSelector(
            val multipleSelection: Boolean = false,
            val preSelectedIds: List<String> = emptyList(),
        ) : Effect
        object NavigateBack : Effect

        // === UI反馈副作用 ===
        data class ShowToast(val message: UiText) : Effect
        data class ShowSnackbar(
            val message: UiText,
            val actionLabel: String? = null,
            val action: (() -> Unit)? = null,
        ) : Effect
        data class TriggerHapticFeedback(val type: HapticFeedbackType) : Effect

        // === 系统副作用 ===
        data class ShareTemplate(val template: WorkoutTemplateDto) : Effect
        data class ExportToFile(val template: WorkoutTemplateDto) : Effect
        data class ImportFromFile(val filePath: String) : Effect

        // === 对话框副作用 ===
        object ShowUnsavedChangesDialog : Effect
        data class ShowDeleteConfirmDialog(val templateName: String) : Effect
        data class ShowPromoteDraftDialog(val draftName: String) : Effect
        object ShowCacheRestoreDialog : Effect

        // === 动画副作用 (P4新增) ===
        data class AnimateItemRemoval(val itemId: String) : Effect
        data class AnimateItemAddition(val itemId: String) : Effect
        data class AnimateItemReorder(val fromIndex: Int, val toIndex: Int) : Effect
        data class AnimateMoveToTop(val itemId: String) : Effect

        // === 排序持久化副作用 ===
        data class SaveTemplateOrder(val templateOrder: Map<String, Int>) : Effect
        data class SaveDraftOrder(val draftOrder: Map<String, Int>) : Effect

        // === 数据加载副作用 ===
        object LoadTemplatesData : Effect
        object LoadDraftsData : Effect
        object RefreshTemplatesData : Effect
        object RefreshDraftsData : Effect

        // === 网络副作用 ===
        object RefreshData : Effect
        object SyncWithServer : Effect
        data class UploadTemplate(val template: WorkoutTemplateDto) : Effect
        data class DownloadTemplate(val templateId: String) : Effect

        // === 删除操作Effects ===
        data class DeleteTemplateData(val templateId: String) : Effect
        data class DeleteDraftData(val draftId: String) : Effect
    }

    // === 辅助数据类 ===

    enum class TemplateTab(val displayName: String) {
        TEMPLATES("模板"),
        DRAFTS("草稿"),
    }

    enum class ExerciseSelectorMode {
        SINGLE,
        MULTIPLE,
    }

    /**
     * 自动保存状态 (P4新增)
     * 🔥 简化：移除过度复杂的状态，保持简单实用
     */
    enum class AutoSaveState {
        Inactive, // 未激活
        Saving, // 保存中
        Success, // 保存成功
        Failed, // 保存失败
    }

    sealed class SwipeState {
        object Idle : SwipeState()
        data class Swiping(val offset: Float) : SwipeState()
        object Deleting : SwipeState()
    }

    sealed class NavigationTarget {
        data class TemplateDetail(val templateId: String) : NavigationTarget()
        data class EditTemplate(val templateId: String) : NavigationTarget()
        object NewTemplate : NavigationTarget()
        data class DraftEditor(val draftId: String) : NavigationTarget()
        object CreateDraft : NavigationTarget()
        object ExerciseSelector : NavigationTarget()
        object Back : NavigationTarget()
    }

    data class CacheEntry(
        val templateId: String,
        val template: WorkoutTemplateDto,
        val timestamp: Long,
        val changeCount: Int,
    )

    data class OperationResult<T>(
        val success: Boolean,
        val data: T? = null,
        val error: UiText? = null,
        val retryable: Boolean = false,
    )

    // === 分类常量 ===
    object Categories {
        const val ALL = "全部"
        const val STRENGTH = "力量训练"
        const val CARDIO = "有氧训练"
        const val FLEXIBILITY = "柔韧性"
        const val SPORTS = "运动专项"
        const val REHABILITATION = "康复训练"

        val defaultCategories = listOf(ALL, STRENGTH, CARDIO, FLEXIBILITY, SPORTS, REHABILITATION)
    }
}

/**
 * TemplateContract.State的统一拖拽状态扩展方法
 * 为TemplateScreen提供与TemplateEdit一致的拖拽体验
 */

/**
 * 获取当前活跃的拖拽状态（模板或草稿）
 */
fun TemplateContract.State.getCurrentDragState(): DragState<*>? {
    return when (currentTab) {
        TemplateContract.TemplateTab.TEMPLATES -> templateDragState.takeIf { it.isDragInProgress }
        TemplateContract.TemplateTab.DRAFTS -> draftDragState.takeIf { it.isDragInProgress }
    }
}

/**
 * 检查是否有任何拖拽操作正在进行
 */
fun TemplateContract.State.isAnyDragInProgress(): Boolean {
    return templateDragState.isDragInProgress || draftDragState.isDragInProgress
}

/**
 * 获取统一的拖拽状态摘要信息
 */
fun TemplateContract.State.getDragStateSummary(): String {
    return when (currentTab) {
        TemplateContract.TemplateTab.TEMPLATES -> {
            if (templateDragState.isDragInProgress) {
                "模板拖拽中: ${templateDragState.draggedItemId} (${templateDragState.draggedItemIndex} → ${templateDragState.dragTargetIndex})"
            } else "无模板拖拽"
        }
        TemplateContract.TemplateTab.DRAFTS -> {
            if (draftDragState.isDragInProgress) {
                "草稿拖拽中: ${draftDragState.draggedItemId} (${draftDragState.draggedItemIndex} → ${draftDragState.dragTargetIndex})"
            } else "无草稿拖拽"
        }
    }
}

/**
 * 更新模板拖拽状态
 */
fun TemplateContract.State.updateTemplateDragState(
    newDragState: DragState<WorkoutTemplateDto>
): TemplateContract.State {
    return copy(templateDragState = newDragState)
}

/**
 * 更新草稿拖拽状态
 */
fun TemplateContract.State.updateDraftDragState(
    newDragState: DragState<TemplateDraft>
): TemplateContract.State {
    return copy(draftDragState = newDragState)
}
