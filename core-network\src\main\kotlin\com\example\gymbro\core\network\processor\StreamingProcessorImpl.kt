package com.example.gymbro.core.network.processor

import com.example.gymbro.core.logging.GymBroLogTags
import com.example.gymbro.core.network.detector.ContentType
import kotlinx.serialization.json.*
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚀 流式处理器实现 - 统一SSE解析版
 *
 * 新职责：
 * - 统一SSE响应解析（从AiResponseReceiver迁移）
 * - 协议检测和内容提取
 * - 直接处理和路由到消费者
 */
@Singleton
class StreamingProcessorImpl @Inject constructor(
    private val contentExtractor: ContentExtractor,
    private val outputSanitizer: OutputSanitizer,
) : StreamingProcessor {

    companion object {
        private val TAG = GymBroLogTags.CoreNetwork.PROCESSOR_STREAM

        // 🔥 【迁移自AiResponseReceiver】SSE解析配置
        private val JSON_PARSER = kotlinx.serialization.json.Json {
            ignoreUnknownKeys = true
            isLenient = true
        }
    }

    // 🏷️ 【日志统计】批量处理统计
    @Volatile
    private var totalTokensProcessed = 0L
    @Volatile
    private var totalParseErrors = 0L
    @Volatile
    private var lastStatsLogTime = 0L

    private val statsLogInterval = 30_000L // 30秒记录一次统计

    override fun processImmediate(
        token: String,
        contentType: ContentType,
        messageId: String,
    ): String {
        return try {
            totalTokensProcessed++

            // 🏷️ 【日志统计】定期记录批量处理统计
            logBatchStatsIfNeeded()

            // 简化处理：根据内容类型直接处理
            val result = when (contentType) {
                ContentType.JSON_SSE -> processJsonSse(token)
                ContentType.XML_THINKING -> {
                    Timber.tag("CNET-PROCESSOR-Fix").e("📋 [XML_THINKING处理] 直接返回")
                    token // 直接返回，让ThinkingBox处理
                }
                ContentType.PLAIN_TEXT -> {
                    Timber.tag("CNET-PROCESSOR-Fix").e("📋 [PLAIN_TEXT处理] 直接返回纯文本")
                    token // 🔥 【修复关键】纯文本直接返回，无需JSON处理
                }
                else -> {
                    Timber.tag("CNET-PROCESSOR-Fix").e("📋 [其他类型处理] 直接返回: $contentType")
                    token // 其他类型直接返回
                }
            }

            // 🔥 【调试追踪】记录处理结果 - 使用统一日志标签
            Timber.tag(GymBroLogTags.CoreNetwork.PROCESSOR_STREAM).d(
                "✅ [处理结果] 长度变化=${token.length}->${result.length}"
            )

            result
        } catch (e: Exception) {
            Timber.tag(GymBroLogTags.CoreNetwork.PROCESSOR_STREAM).w(e, "处理失败，返回原token")
            token // 失败时返回原token
        }
    }

    /**
     * 简化JSON SSE处理
     */
    private fun processJsonSse(token: String): String {
        return try {
            val content = contentExtractor.extractJsonSseContent(token)
            outputSanitizer.sanitizeForDirectOutput(content)
        } catch (e: Exception) {
            totalParseErrors++
            Timber.tag(GymBroLogTags.CoreNetwork.PROCESSOR_STREAM).w(e, "JSON SSE处理失败")
            ""
        }
    }

    /**
     * 🏷️ 【日志统计】定期记录批量处理统计
     */
    private fun logBatchStatsIfNeeded() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastStatsLogTime >= statsLogInterval) {
            val errorRate = if (totalTokensProcessed > 0) {
                (totalParseErrors * 100.0 / totalTokensProcessed).toInt()
            } else 0

            Timber.tag(GymBroLogTags.CoreNetwork.PROCESSOR_STREAM).i(
                "📊 [批量统计] 已处理tokens=$totalTokensProcessed, " +
                "解析错误=$totalParseErrors, 错误率=${errorRate}%"
            )

            lastStatsLogTime = currentTime
        }
    }
}

/**
 * 📄 内容提取器接口
 */
interface ContentExtractor {
    /**
     * 即时提取JSON SSE内容
     */
    fun extractJsonSseContent(token: String): String

    /**
     * 即时提取JSON流内容
     */
    fun extractJsonStreamContent(token: String): String

    /**
     * 提取WebSocket内容
     */
    fun extractWebSocketContent(token: String): String
}

/**
 * 🛡️ 输出净化器接口
 */
interface OutputSanitizer {
    /**
     * 为直接输出净化内容
     */
    fun sanitizeForDirectOutput(content: String): String
}

/**
 * 📄 内容提取器实现 - 从protocol目录移动过来
 *
 * 优化：移除缓冲机制，改为即时提取
 */
@Singleton
class ContentExtractorImpl @Inject constructor(
    private val json: kotlinx.serialization.json.Json,
) : ContentExtractor {

    companion object {
        private val TAG = GymBroLogTags.CoreNetwork.PROCESSOR_CONTENT

        // 🔥 【JSON解析器】统一的JSON解析配置
        private val JSON_PARSER = kotlinx.serialization.json.Json {
            ignoreUnknownKeys = true
            isLenient = true
        }
    }





    /**
     * 🔥 【架构重构】统一SSE解析入口 - 从AiResponseReceiver迁移
     *
     * **新数据流说明**：
     * 1. Core-Network成为SSE解析的唯一入口点
     * 2. 接收原始SSE响应：`data: {"choices":[{"delta":{"content":"太"}}]}`
     * 3. 解析并提取纯文本内容：`"太"`
     * 4. 直接路由到ThinkingBox等消费者
     */
    override fun extractJsonSseContent(token: String): String {
        // 🔥 【统一解析点】处理SSE格式的响应 - 移除token级别日志
        return if (token.startsWith("data: ")) {
            parseSseResponseLine(token)
        } else {
            // 已经是纯文本内容，直接传递
            token
        }
    }

    /**
     * 🔥 【迁移自AiResponseReceiver】解析单行SSE响应
     *
     * 从 AiResponseReceiver.parseSseResponse() 方法迁移而来
     */
    private fun parseSseResponseLine(sseToken: String): String {
        try {
            // 🔥 【修复】检查SSE格式并正确提取data部分
            if (!sseToken.startsWith("data: ")) {
                return ""
            }

            val data = sseToken.substring(6) // 移除"data: "前缀
            if (data.trim() == "[DONE]") {
                return ""
            }

            // 🔥 【修复】增强JSON解析错误处理
            val jsonElement = JSON_PARSER.parseToJsonElement(data)

            val choices = jsonElement.jsonObject["choices"]?.jsonArray
            if (choices == null || choices.isEmpty()) {
                return ""
            }

            val firstChoice = choices[0].jsonObject
            val delta = firstChoice["delta"]?.jsonObject
            if (delta == null) {
                return ""
            }

            val content = delta["content"]?.jsonPrimitive?.content
            if (!content.isNullOrEmpty()) {
                return content
            }

            return ""

        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "SSE JSON解析失败")
            return ""
        }
    }

    /**
     * 即时提取JSON流内容
     */
    override fun extractJsonStreamContent(token: String): String {
        return try {
            // 🔥 【增强JSON解析】支持更多JSON格式和容错处理
            val trimmedToken = token.trim()

            // 检查是否为空或无效JSON
            if (trimmedToken.isEmpty() || (!trimmedToken.startsWith("{") && !trimmedToken.startsWith("["))) {
                return ""
            }

            val jsonElement = json.parseToJsonElement(trimmedToken)

            val result = when {
                // 处理JSON对象
                jsonElement is JsonObject -> {
                    val jsonObj = jsonElement.jsonObject

                    // 尝试多种可能的内容字段
                    jsonObj["text"]?.jsonPrimitive?.content
                        ?: jsonObj["message"]?.jsonPrimitive?.content
                        ?: jsonObj["content"]?.jsonPrimitive?.content
                        ?: jsonObj["data"]?.jsonPrimitive?.content
                        ?: jsonObj["chunk"]?.jsonPrimitive?.content
                        // 🔥 【新增】支持嵌套结构
                        ?: jsonObj["response"]?.jsonObject?.get("text")?.jsonPrimitive?.content
                        ?: jsonObj["result"]?.jsonObject?.get("content")?.jsonPrimitive?.content
                        ?: ""
                }

                // 处理JSON数组（可能包含多个消息）
                jsonElement is JsonArray -> {
                    val jsonArray = jsonElement.jsonArray
                    val contents = mutableListOf<String>()

                    jsonArray.forEach { item ->
                        if (item is JsonObject) {
                            val obj = item.jsonObject
                            val content = obj["text"]?.jsonPrimitive?.content
                                ?: obj["content"]?.jsonPrimitive?.content
                                ?: obj["message"]?.jsonPrimitive?.content
                            if (!content.isNullOrEmpty()) {
                                contents.add(content)
                            }
                        }
                    }

                    contents.joinToString("")
                }

                else -> ""
            }

            result
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "JSON流解析失败")
            ""
        }
    }

    /**
     * 提取WebSocket内容
     */
    override fun extractWebSocketContent(token: String): String {
        // 简化的WebSocket帧解析
        return if (token.startsWith("WS:")) {
            token.removePrefix("WS:")
        } else {
            token
        }
    }
}

/**
 * 🛡️ 输出净化器实现 - 从security目录重命名
 *
 * 优化：简化为直接输出净化，移除XML转义（让ThinkingBox处理）
 */
@Singleton
class OutputSanitizerImpl @Inject constructor(
    private val piiSanitizer: com.example.gymbro.core.network.security.PiiSanitizer,
) : OutputSanitizer {

    /**
     * 为直接输出净化内容
     *
     * 🔥 关键优化：移除XML转义，让ThinkingBox自己处理
     * 减少20ms延迟
     */
    override fun sanitizeForDirectOutput(content: String): String {
        if (content.isEmpty()) return ""

        // 仅做PII过滤，移除XML转义
        return piiSanitizer.sanitizeContent(content)
    }
}
