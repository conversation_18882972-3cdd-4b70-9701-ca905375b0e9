package com.example.gymbro.core.conversation

import com.example.gymbro.core.util.CompactIdGenerator
import com.example.gymbro.core.util.Constants
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

/**
 * 对话ID管理器 - Plan B架构核心组件
 *
 * 🎯 核心职责：
 * - 统一管理messageId、sessionId的生成和关系
 * - 消除conversationId概念，简化ID传递
 * - 提供智能ID匹配和容错机制
 * - 集成CompactIdGenerator用于日志显示
 * - 支持多轮对话的会话管理
 *
 * 🏗️ 架构特点：
 * - 遵循Clean Architecture原则
 * - 线程安全设计，支持并发访问
 * - 提供完整的生命周期管理
 * - 支持过期清理和内存优化
 *
 * <AUTHOR> B架构重构
 * @since AI数据流ID统一化
 */
@Singleton
class ConversationIdManager @Inject constructor(
    private val compactIdGenerator: CompactIdGenerator
) {
    
    companion object {
        private const val TAG = "ConversationIdManager"
        private const val DEFAULT_SESSION_PREFIX = "session"
        private const val MAX_MESSAGE_CHAIN_SIZE = 1000
        private const val CLEANUP_INTERVAL_MS = 24 * 60 * 60 * 1000L // 24小时
    }
    
    // 线程安全的注册表
    private val messageRegistry = ConcurrentHashMap<String, MessageContext>()
    private val sessionRegistry = ConcurrentHashMap<String, SessionContext>()
    private val compactIdMapping = ConcurrentHashMap<String, String>() // compactId -> messageId
    
    // 清理锁
    private val cleanupMutex = Mutex()
    
    /**
     * 消息上下文 - 统一ID管理的核心数据结构
     */
    data class MessageContext(
        val messageId: String,           // 主要ID，全链路使用
        val sessionId: String,           // 会话ID，用于多轮对话分组
        val compactId: String,           // 6位压缩ID，用于日志和调试
        val timestamp: Long,             // 创建时间戳
        val metadata: Map<String, Any> = emptyMap()
    ) {
        /**
         * 获取用于日志显示的友好ID
         */
        fun getDisplayId(): String = compactId
        
        /**
         * 获取完整的调试信息
         */
        fun getDebugInfo(): String = "MessageContext(compactId=$compactId, messageId=${messageId.take(8)}..., sessionId=$sessionId)"
        
        companion object {
            /**
             * 创建新的消息上下文
             */
            fun create(sessionId: String, compactIdGenerator: CompactIdGenerator): MessageContext {
                val messageId = Constants.MessageId.generate()
                val compactId = compactIdGenerator.generateCompactId(messageId)
                return MessageContext(
                    messageId = messageId,
                    sessionId = sessionId,
                    compactId = compactId,
                    timestamp = System.currentTimeMillis()
                )
            }
        }
    }
    
    /**
     * 会话上下文 - 管理多轮对话
     */
    data class SessionContext(
        val sessionId: String,
        val userId: String,
        val messageChain: MutableList<String> = mutableListOf(),
        val createdAt: Long = System.currentTimeMillis(),
        var lastActiveAt: Long = System.currentTimeMillis()
    ) {
        /**
         * 添加消息到会话链
         */
        fun addMessage(messageId: String) {
            messageChain.add(messageId)
            lastActiveAt = System.currentTimeMillis()
            
            // 限制消息链长度，防止内存泄漏
            if (messageChain.size > MAX_MESSAGE_CHAIN_SIZE) {
                messageChain.removeAt(0)
            }
        }
        
        /**
         * 获取最近的N条消息
         */
        fun getRecentMessages(count: Int): List<String> {
            return messageChain.takeLast(count)
        }
        
        /**
         * 检查会话是否过期
         */
        fun isExpired(maxAgeMs: Long): Boolean {
            return System.currentTimeMillis() - lastActiveAt > maxAgeMs
        }
    }
    
    /**
     * ID验证结果
     */
    sealed class ValidationResult {
        object Valid : ValidationResult()
        data class Invalid(val reason: String) : ValidationResult()
        data class NotFound(val searchedId: String) : ValidationResult()
    }
    
    /**
     * 创建新的消息上下文
     */
    fun createMessageContext(sessionId: String): MessageContext {
        val context = MessageContext.create(sessionId, compactIdGenerator)
        
        // 注册消息上下文
        messageRegistry[context.messageId] = context
        compactIdMapping[context.compactId] = context.messageId
        
        // 更新会话的消息链
        sessionRegistry[sessionId]?.addMessage(context.messageId)
        
        Timber.tag(TAG).d("✅ 创建消息上下文: ${context.getDebugInfo()}")
        return context
    }
    
    /**
     * 创建新的会话上下文
     */
    fun createSessionContext(userId: String): SessionContext {
        val sessionId = generateSessionId()
        val context = SessionContext(sessionId = sessionId, userId = userId)
        sessionRegistry[sessionId] = context
        
        Timber.tag(TAG).d("✅ 创建会话上下文: sessionId=$sessionId, userId=$userId")
        return context
    }
    
    /**
     * 验证消息ID是否有效
     */
    fun validateMessageId(messageId: String): ValidationResult {
        return when {
            messageId.isBlank() -> ValidationResult.Invalid("消息ID不能为空")
            !Constants.MessageId.isValid(messageId) -> ValidationResult.Invalid("消息ID格式无效")
            messageRegistry.containsKey(messageId) -> ValidationResult.Valid
            else -> ValidationResult.NotFound(messageId)
        }
    }
    
    /**
     * 获取消息上下文
     */
    fun getMessageContext(messageId: String): MessageContext? {
        return messageRegistry[messageId]
    }
    
    /**
     * 获取会话上下文
     */
    fun getSessionContext(sessionId: String): SessionContext? {
        return sessionRegistry[sessionId]
    }
    
    /**
     * 智能ID匹配 - 容错机制
     * 支持多种ID格式的匹配，提高系统容错性
     */
    fun findBestMatchingMessage(partialId: String): MessageContext? {
        if (partialId.isBlank()) return null
        
        // 1. 精确匹配完整messageId
        messageRegistry[partialId]?.let { 
            Timber.tag(TAG).d("🎯 精确匹配: $partialId")
            return it 
        }
        
        // 2. 压缩ID匹配
        compactIdMapping[partialId]?.let { messageId ->
            messageRegistry[messageId]?.let { context ->
                Timber.tag(TAG).d("🎯 压缩ID匹配: $partialId -> ${context.getDisplayId()}")
                return context
            }
        }
        
        // 3. 前缀匹配（至少8位）
        if (partialId.length >= 8) {
            messageRegistry.values.find { it.messageId.startsWith(partialId) }?.let { context ->
                Timber.tag(TAG).d("🎯 前缀匹配: $partialId -> ${context.getDisplayId()}")
                return context
            }
        }
        
        Timber.tag(TAG).w("❌ 未找到匹配的消息: $partialId")
        return null
    }
    
    /**
     * 获取会话的所有消息上下文
     */
    fun getSessionMessages(sessionId: String): List<MessageContext> {
        val session = sessionRegistry[sessionId] ?: return emptyList()
        return session.messageChain.mapNotNull { messageRegistry[it] }
    }
    
    /**
     * 清理过期的消息和会话上下文
     */
    suspend fun cleanupExpiredContexts(maxAgeMs: Long = CLEANUP_INTERVAL_MS) {
        cleanupMutex.withLock {
            val cutoffTime = System.currentTimeMillis() - maxAgeMs
            var cleanedMessages = 0
            var cleanedSessions = 0
            
            // 清理过期消息
            val expiredMessages = messageRegistry.values.filter { it.timestamp < cutoffTime }
            expiredMessages.forEach { context ->
                messageRegistry.remove(context.messageId)
                compactIdMapping.remove(context.compactId)
                cleanedMessages++
            }
            
            // 清理过期会话
            val expiredSessions = sessionRegistry.values.filter { it.isExpired(maxAgeMs) }
            expiredSessions.forEach { context ->
                sessionRegistry.remove(context.sessionId)
                cleanedSessions++
            }
            
            if (cleanedMessages > 0 || cleanedSessions > 0) {
                Timber.tag(TAG).i("🧹 清理完成: 消息=$cleanedMessages, 会话=$cleanedSessions")
            }
        }
    }
    
    /**
     * 获取统计信息
     */
    fun getStatistics(): ConversationStatistics {
        return ConversationStatistics(
            totalMessages = messageRegistry.size,
            totalSessions = sessionRegistry.size,
            compactIdMappings = compactIdMapping.size,
            memoryUsageEstimate = estimateMemoryUsage()
        )
    }
    
    /**
     * 生成会话ID
     */
    private fun generateSessionId(): String {
        return "${DEFAULT_SESSION_PREFIX}_${System.currentTimeMillis()}_${Random.nextInt(1000, 9999)}"
    }
    
    /**
     * 估算内存使用量
     */
    private fun estimateMemoryUsage(): Long {
        // 粗略估算：每个MessageContext约200字节，每个SessionContext约150字节
        return (messageRegistry.size * 200L) + (sessionRegistry.size * 150L) + (compactIdMapping.size * 50L)
    }
    
    /**
     * 统计信息数据类
     */
    data class ConversationStatistics(
        val totalMessages: Int,
        val totalSessions: Int,
        val compactIdMappings: Int,
        val memoryUsageEstimate: Long
    ) {
        fun toLogString(): String {
            return "ConversationStats(messages=$totalMessages, sessions=$totalSessions, memory=${memoryUsageEstimate}B)"
        }
    }
}
