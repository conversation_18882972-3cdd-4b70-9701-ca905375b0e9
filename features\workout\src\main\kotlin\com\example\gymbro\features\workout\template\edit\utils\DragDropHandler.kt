package com.example.gymbro.features.workout.template.edit.utils

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import com.example.gymbro.features.workout.shared.components.drag.DragAnimations
import com.example.gymbro.features.workout.shared.components.drag.DragConfig
import com.example.gymbro.features.workout.shared.components.drag.DragResult
import com.example.gymbro.features.workout.shared.components.drag.DragState
import com.example.gymbro.features.workout.shared.components.drag.UnifiedDragHandler
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import com.example.gymbro.shared.models.workout.TemplateExerciseDto
import timber.log.Timber

/**
 * 拖拽排序处理工具类 (统一拖拽适配器)
 *
 * 完全基于UnifiedDragHandler重构，保持现有API完全兼容，
 * 增强Material3动画效果、触觉反馈和性能优化。
 *
 * ✨ 迁移完成特性：
 * - 🎯 100%兼容现有TemplateEditContract API
 * - ⚡ UnifiedDragHandler核心引擎
 * - 🎨 Material3标准动画效果
 * - 📱 智能触觉反馈系统
 * - 🚀 高性能列表重排序算法
 * - 🔧 泛型类型安全设计
 *
 * 设计原则：
 * - 向后兼容：所有现有函数签名保持不变
 * - 增强功能：集成Material3动画和触觉反馈
 * - 纯函数式：无副作用，状态不可变
 * - 性能优先：优化列表操作和动画性能
 */
object DragDropHandler {

    /**
     * 创建统一拖拽状态实例 - 完全兼容TemplateEditContract
     *
     * 直接使用TemplateEditContract.State中的createDragState()方法，
     * 确保状态转换的一致性和可维护性。
     */
    private fun createInternalDragState(
        state: TemplateEditContract.State
    ): DragState<TemplateExerciseDto> {
        return state.createDragState()
    }

    /**
     * 将拖拽状态应用回TemplateEditContract.State
     *
     * 直接使用TemplateEditContract.State中的updateFromDragState()方法，
     * 确保状态更新的一致性。
     */
    private fun applyDragStateToContract(
        state: TemplateEditContract.State,
        dragState: DragState<TemplateExerciseDto>
    ): TemplateEditContract.State {
        return state.updateFromDragState(dragState)
    }

    /**
     * 检查是否可以开始拖拽
     */
    fun canStartDrag(state: TemplateEditContract.State, exerciseId: String): Boolean {
        val dragState = createInternalDragState(state)
        return dragState.canStartDrag(exerciseId) && state.reorderingEnabled
    }

    /**
     * 处理拖拽开始事件 - 兼容现有API
     */
    fun handleDragStart(
        state: TemplateEditContract.State,
        exerciseId: String,
        startIndex: Int,
        startPosition: Offset = Offset.Zero
    ): TemplateEditContract.State {
        Timber.d("DragDropHandler: 开始拖拽 exerciseId=$exerciseId, startIndex=$startIndex")

        val exercise = state.exercises.find { it.id == exerciseId }
        if (exercise == null) {
            Timber.w("DragDropHandler: 未找到动作 exerciseId=$exerciseId")
            return state
        }

        val dragState = createInternalDragState(state)
        val newDragState = UnifiedDragHandler.handleDragStart(
            dragState = dragState,
            item = exercise,
            itemId = exerciseId,
            startIndex = startIndex,
            startPosition = startPosition
        )

        return applyDragStateToContract(state, newDragState)
    }

    /**
     * 处理拖拽移动事件
     */
    fun handleDragMove(
        state: TemplateEditContract.State,
        targetIndex: Int,
        currentPosition: Offset = Offset.Zero
    ): TemplateEditContract.State {
        val dragState = createInternalDragState(state)
        val newDragState = UnifiedDragHandler.handleDragMove(
            dragState = dragState,
            currentPosition = currentPosition,
            targetIndex = targetIndex,
            itemCount = state.exercises.size
        )

        return applyDragStateToContract(state, newDragState)
    }

    /**
     * 处理拖拽完成事件
     */
    fun handleDragComplete(
        state: TemplateEditContract.State,
        fromIndex: Int,
        toIndex: Int
    ): TemplateEditContract.State {
        Timber.d("DragDropHandler: 完成拖拽 fromIndex=$fromIndex, toIndex=$toIndex")

        val dragState = createInternalDragState(state)
        val dragCompleteResult = UnifiedDragHandler.handleDragComplete(
            dragState = dragState,
            items = state.exercises,
            fromIndex = fromIndex,
            toIndex = toIndex
        )

        // 更新动作列表和拖拽状态
        val newState = applyDragStateToContract(state, dragCompleteResult.newState)
        return newState.copy(
            exercises = dragCompleteResult.reorderedItems,
            hasUnsavedChanges = true
        )
    }

    /**
     * 处理拖拽取消事件
     */
    fun handleDragCancel(
        state: TemplateEditContract.State,
        reason: String = "User cancelled"
    ): TemplateEditContract.State {
        Timber.d("DragDropHandler: 取消拖拽 reason=$reason")

        val dragState = createInternalDragState(state)
        val newDragState = UnifiedDragHandler.handleDragCancel(dragState, reason)

        return applyDragStateToContract(state, newDragState)
    }

    /**
     * 重排序动作列表 - 兼容现有API
     */
    fun reorderExerciseList(
        exercises: List<TemplateExerciseDto>,
        fromIndex: Int,
        toIndex: Int
    ): List<TemplateExerciseDto> {
        return UnifiedDragHandler.reorderList(exercises, fromIndex, toIndex)
    }

    /**
     * 创建Material3拖拽修饰符
     */
    @Composable
    fun createDragModifier(
        state: TemplateEditContract.State,
        exerciseId: String,
        config: DragConfig = DragConfig.Material3
    ): Modifier {
        val dragState = createInternalDragState(state)

        return with(DragAnimations) {
            Modifier.dragAnimated(dragState, config)
        }
    }

    /**
     * 检查是否正在拖拽指定动作
     */
    fun isDragging(state: TemplateEditContract.State, exerciseId: String): Boolean {
        val dragState = createInternalDragState(state)
        return dragState.isDragging(exerciseId)
    }

    /**
     * 检查是否为有效的拖拽目标
     */
    fun isValidDropTarget(state: TemplateEditContract.State, index: Int): Boolean {
        val dragState = createInternalDragState(state)
        return dragState.isValidDropTarget(index)
    }

    /**
     * 获取当前拖拽状态摘要（用于调试）
     */
    fun getDragStateSummary(state: TemplateEditContract.State): String {
        val dragState = createInternalDragState(state)
        return dragState.getDebugSummary()
    }
}
