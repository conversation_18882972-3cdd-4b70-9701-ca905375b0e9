package com.example.gymbro.features.thinkingbox.api

/**
 * ThinkingBoxDisplay - ThinkingBox显示控制接口
 *
 * 🎯 职责分离架构设计：
 * - Coach专注对话管理，不再处理AI思考过程显示
 * - ThinkingBox自主处理AI响应显示和用户交互
 * - 通过完成回调将结果返回给Coach
 *
 * 🔥 核心功能：
 * - 启动AI思考过程显示
 * - 控制思考过程的开始和停止
 * - 管理思考过程的生命周期
 * - 处理思考完成后的回调通知
 *
 * 架构原则：
 * - 接口抽象：定义清晰的显示控制契约
 * - 异步回调：支持思考完成后的异步通知
 * - 错误处理：完整的错误处理和恢复机制
 * - 资源管理：proper资源清理和生命周期管理
 *
 * @since Coach-ThinkingBox重构v2.0
 */
interface ThinkingBoxDisplay {

    /**
     * 启动AI思考过程显示
     *
     * @param messageId 消息唯一标识符，用于关联对话上下文
     * @param sessionId 会话唯一标识符，用于关联到具体的对话会话
     * @param completionListener 完成回调监听器，用于接收思考完成通知
     *
     * 行为说明：
     * - 初始化ThinkingBox UI组件
     * - 启动token流监听和解析
     * - 开始实时显示AI思考过程
     * - 注册完成回调监听器
     *
     * 错误处理：
     * - 如果messageId已存在，则停止之前的会话并启动新会话
     * - 如果启动失败，通过completionListener回调错误信息
     * - 网络异常或解析错误将通过回调通知
     */
    fun startDisplaying(
        messageId: String,
        sessionId: String, // 🔥 【ID命名统一】添加 sessionId 参数
        completionListener: ThinkingBoxCompletionListener,
    )

    /**
     * 停止AI思考过程显示
     *
     * @param messageId 要停止的消息标识符
     *
     * 行为说明：
     * - 停止对应messageId的token流监听
     * - 清理UI状态和资源
     * - 取消未完成的渲染任务
     * - 不会触发完成回调（主动停止）
     *
     * 应用场景：
     * - 用户主动取消思考过程
     * - 页面导航离开时清理资源
     * - 异常情况下的强制停止
     */
    fun stopDisplaying(messageId: String)

    /**
     * 检查指定消息是否正在显示
     *
     * @param messageId 消息标识符
     * @return true如果正在显示，false否则
     *
     * 用途：
     * - 避免重复启动同一消息的思考显示
     * - UI状态检查和调试
     * - 资源管理和清理判断
     */
    fun isDisplaying(messageId: String): Boolean

    /**
     * 获取当前正在显示的消息列表
     *
     * @return 当前活跃的消息ID列表
     *
     * 用途：
     * - 资源监控和管理
     * - 调试和诊断
     * - 批量清理操作
     */
    fun getActiveDisplays(): List<String>

    /**
     * 清理所有活跃的显示会话
     *
     * 行为说明：
     * - 停止所有正在进行的思考显示
     * - 清理所有相关资源
     * - 不触发完成回调
     *
     * 应用场景：
     * - 应用退出时的资源清理
     * - 内存压力下的资源释放
     * - 全局重置操作
     */
    fun clearAllDisplays()
}
