package com.example.gymbro.core.network.logging

import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.concurrent.atomic.AtomicLong

/**
 * 🔄 Token批量缓冲器
 *
 * 高性能的Ring Buffer实现，用于批量收集RAW TOKEN：
 * - 固定容量，避免无限增长
 * - 线程安全的并发操作
 * - 零拷贝的数据结构
 * - 自动覆盖最旧数据
 *
 * 性能目标：
 * - 单次add操作 <0.1ms
 * - 单次flush操作 <2ms
 * - 内存占用 <1MB per buffer
 */
class TokenBuffer(
    private val capacity: Int = 100,
    private val maxSizeBytes: Long = 1024 * 1024, // 1MB
) {

    companion object {
        private const val TAG = "TokenBuffer"
    }

    // Ring Buffer数据结构
    private val buffer = Array<TokenEntry?>(capacity) { null }
    private var writeIndex = 0
    private var readIndex = 0
    private var size = 0

    // 线程安全保护
    private val mutex = Mutex()

    // 统计信息
    private val totalAdded = AtomicLong(0)
    private val totalFlushed = AtomicLong(0)
    private val totalDropped = AtomicLong(0)

    // 当前缓冲区大小（字节）
    @Volatile
    private var currentSizeBytes = 0L

    /**
     * 添加token到缓冲区
     * 🔥 【Plan B重构】使用messageId参数
     *
     * @param token token内容
     * @param source 来源标识
     * @param messageId 消息ID（原conversationId）
     * @param type token类型（RECEIVED/OUTPUT）
     * @return 是否成功添加
     */
    suspend fun add(
        token: String,
        source: String,
        messageId: String,
        type: TokenType,
    ): Boolean {
        if (token.isEmpty()) return false

        val entry = TokenEntry(
            content = token,
            source = source,
            conversationId = messageId, // 🔥 【Plan B重构】传递messageId
            type = type,
            timestamp = System.currentTimeMillis(),
            sizeBytes = token.length.toLong() * 2, // UTF-16估算
        )

        return mutex.withLock {
            // 检查内存限制
            if (currentSizeBytes + entry.sizeBytes > maxSizeBytes) {
                // 强制flush以释放内存
                return@withLock false
            }

            // 如果缓冲区满了，覆盖最旧的数据
            if (size == capacity) {
                val oldEntry = buffer[writeIndex]
                if (oldEntry != null) {
                    currentSizeBytes -= oldEntry.sizeBytes
                    totalDropped.incrementAndGet()
                }
                readIndex = (readIndex + 1) % capacity
            } else {
                size++
            }

            // 写入新数据
            buffer[writeIndex] = entry
            writeIndex = (writeIndex + 1) % capacity
            currentSizeBytes += entry.sizeBytes
            totalAdded.incrementAndGet()

            true
        }
    }

    /**
     * 批量添加tokens
     * 🔥 【Plan B重构】使用messageId参数
     *
     * @param tokens token列表
     * @param source 来源标识
     * @param messageId 消息ID（原conversationId）
     * @param type token类型
     * @return 成功添加的数量
     */
    suspend fun addBatch(
        tokens: List<String>,
        source: String,
        messageId: String,
        type: TokenType,
    ): Int {
        if (tokens.isEmpty()) return 0

        var addedCount = 0
        for (token in tokens) {
            if (add(token, source, messageId, type)) {
                addedCount++
            } else {
                // 缓冲区满了，停止添加
                break
            }
        }

        return addedCount
    }

    /**
     * 刷新缓冲区，返回所有数据并清空
     *
     * @return 缓冲的token条目列表
     */
    suspend fun flush(): List<TokenEntry> {
        return mutex.withLock {
            if (size == 0) {
                return@withLock emptyList()
            }

            val result = mutableListOf<TokenEntry>()

            // 按顺序读取所有数据
            var currentRead = readIndex
            repeat(size) {
                val entry = buffer[currentRead]
                if (entry != null) {
                    result.add(entry)
                    buffer[currentRead] = null // 清空引用，帮助GC
                }
                currentRead = (currentRead + 1) % capacity
            }

            // 重置缓冲区状态
            readIndex = 0
            writeIndex = 0
            size = 0
            currentSizeBytes = 0L
            totalFlushed.addAndGet(result.size.toLong())

            result
        }
    }

    /**
     * 检查是否需要刷新
     *
     * @param maxAgeMs 最大保留时间
     * @return 是否需要刷新
     */
    suspend fun shouldFlush(maxAgeMs: Long = 50): Boolean { // 🔥 【性能优化】减少默认年龄阈值
        return mutex.withLock {
            if (size == 0) return@withLock false

            // 🔥 【性能优化】更激进的刷新策略，实现近实时处理
            if (size >= capacity * 0.5) return@withLock true // 减少从0.8到0.5

            // 检查内存使用
            if (currentSizeBytes >= maxSizeBytes * 0.5) return@withLock true // 减少从0.8到0.5

            // 检查最旧数据的年龄
            val oldestEntry = buffer[readIndex]
            if (oldestEntry != null) {
                val age = System.currentTimeMillis() - oldestEntry.timestamp
                if (age >= maxAgeMs) return@withLock true
            }

            false
        }
    }

    /**
     * 获取缓冲区状态
     */
    suspend fun getStatus(): TokenBufferStatus {
        return mutex.withLock {
            TokenBufferStatus(
                capacity = capacity,
                currentSize = size,
                sizeBytes = currentSizeBytes,
                maxSizeBytes = maxSizeBytes,
                usagePercentage = (size.toFloat() / capacity * 100).toInt(),
                memoryUsagePercentage = (currentSizeBytes.toFloat() / maxSizeBytes * 100).toInt(),
                totalAdded = totalAdded.get(),
                totalFlushed = totalFlushed.get(),
                totalDropped = totalDropped.get(),
            )
        }
    }

    /**
     * 清空缓冲区
     */
    suspend fun clear() {
        mutex.withLock {
            // 清空所有引用
            for (i in buffer.indices) {
                buffer[i] = null
            }

            readIndex = 0
            writeIndex = 0
            size = 0
            currentSizeBytes = 0L
        }
    }
}

/**
 * 📝 Token条目数据结构
 * 🔥 【Plan B重构】保持conversationId字段名以确保序列化兼容性
 */
data class TokenEntry(
    val content: String,
    val source: String,
    val conversationId: String, // 🔥 【Plan B重构】保持字段名，但值为messageId
    val type: TokenType,
    val timestamp: Long,
    val sizeBytes: Long,
) {
    /**
     * 🔥 【Plan B重构】提供messageId访问器
     */
    val messageId: String get() = conversationId
}

/**
 * 🏷️ Token类型枚举
 */
enum class TokenType {
    RECEIVED, // 接收的token
    OUTPUT, // 输出的token
}

/**
 * 📊 Token缓冲区状态
 */
data class TokenBufferStatus(
    val capacity: Int,
    val currentSize: Int,
    val sizeBytes: Long,
    val maxSizeBytes: Long,
    val usagePercentage: Int,
    val memoryUsagePercentage: Int,
    val totalAdded: Long,
    val totalFlushed: Long,
    val totalDropped: Long,
)
