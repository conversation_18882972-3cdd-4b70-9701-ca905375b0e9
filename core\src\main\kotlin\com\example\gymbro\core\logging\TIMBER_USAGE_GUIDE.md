# GymBro项目 Timber 日志使用指南

## 📋 概述

本指南详细说明了GymBro项目中Timber日志系统的使用方法、最佳实践和架构规范。

🔄 **重构更新**: 集成CompactIdGenerator统一ID生成策略，移除UUID压缩功能，提高日志可读性。

## 🏗️ 三层架构概览

### Layer 1 - Core层 (TimberManager)
- **职责**: 统一的日志管理中心和全局配置入口
- **位置**: `core/src/main/kotlin/com/example/gymbro/core/logging/TimberManager.kt`
- **功能**: 模块级别开关控制、运行时动态调整、环境感知的Tree选择

### Layer 2 - Domain层 (Logger接口)
- **职责**: 保持Clean Architecture原则，domain层不依赖Android框架
- **位置**: `core/src/main/kotlin/com/example/gymbro/core/logging/Logger.kt`
- **实现**: `TimberLogger.kt` 作为Logger接口的Timber实现

### Layer 3 - Feature层 (Timber直接使用)
- **职责**: 统一使用Timber进行日志记录
- **控制**: 通过TimberManager的模块配置进行控制
- **ID生成**: 统一使用CompactIdGenerator生成6位压缩ID，提高日志可读性

## 🚀 快速开始

### 1. 应用层初始化 (GymBroApp)

```kotlin
class GymBroApp : Application() {
    @Inject
    lateinit var timberManager: TimberManager

    override fun onCreate() {
        super.onCreate()
        // 🔥 使用TimberManager统一初始化，支持ThinkingBox模式
        timberManager.initializeWithThinkingBoxSupport(BuildConfig.DEBUG)
    }
}
```

### 2. Domain层使用 (推荐)

```kotlin
class GetUserProfileUseCase @Inject constructor(
    private val repository: UserRepository,
    private val logger: Logger // 注入Logger接口
) {
    suspend operator fun invoke(userId: String): ModernResult<UserProfile, DomainError> {
        // 🔄 使用CompactIdGenerator生成请求ID，提高日志可读性
        val requestId = CompactIdGenerator.generateId("req")
        logger.d("开始获取用户资料: userId=$userId, requestId=$requestId")

        return repository.getUser(userId)
            .onSuccess { logger.i("用户资料获取成功: requestId=$requestId") }
            .onError { error -> logger.e("用户资料获取失败: requestId=$requestId", error) }
            .mapError { DomainError.UserNotFound }
    }
}
```

### 3. Feature层使用

```kotlin
class ProfileViewModel @Inject constructor() : BaseMviViewModel<Intent, State, Effect>() {

    private fun handleUserAction(action: UserAction) {
        // 🔥 使用标准化标签
        Timber.tag("PROFILE-UI").d("处理用户操作: ${action::class.simpleName}")

        when (action) {
            is UserAction.LoadProfile -> {
                Timber.tag("PROFILE-DATA").i("开始加载用户资料")
                // 处理逻辑...
            }
            is UserAction.SaveProfile -> {
                Timber.tag("PROFILE-SAVE").i("保存用户资料: ${action.profile.name}")
                // 处理逻辑...
            }
        }
    }

    private fun handleError(error: Throwable) {
        Timber.tag("PROFILE-ERROR").e(error, "Profile操作失败")
    }
}
```

## 🏷️ 标准化标签规范

### 模块级标签前缀
- **PROFILE-**: Profile功能模块
- **COACH-**: AI Coach功能模块
- **WORKOUT-**: 训练功能模块
- **TB-**: ThinkingBox功能模块
- **CORE-**: 核心功能模块

### 功能级标签后缀
- **-UI**: 用户界面相关
- **-DATA**: 数据操作相关
- **-ERROR**: 错误处理相关
- **-SAVE**: 数据保存相关
- **-LOAD**: 数据加载相关
- **-DEBUG**: 调试信息相关

### 示例标签组合
```kotlin
Timber.tag("PROFILE-UI").d("用户点击编辑按钮")
Timber.tag("PROFILE-DATA").i("开始加载用户数据")
Timber.tag("PROFILE-SAVE").i("保存用户资料成功")
Timber.tag("PROFILE-ERROR").e("用户资料加载失败", exception)
Timber.tag("WORKOUT-DEBUG").v("训练状态变更: $oldState -> $newState")
```

## 📊 日志级别使用指南

### VERBOSE (v) - 详细信息
```kotlin
Timber.tag("MODULE-DEBUG").v("详细的调试信息，仅在开发时使用")
```
- **用途**: 非常详细的调试信息
- **场景**: 状态变更、数据流追踪
- **生产环境**: 不输出

### DEBUG (d) - 调试信息
```kotlin
Timber.tag("MODULE-DEBUG").d("调试信息: 用户ID=$userId")
```
- **用途**: 一般调试信息
- **场景**: 方法调用、参数值、流程追踪
- **生产环境**: 不输出

### INFO (i) - 信息
```kotlin
Timber.tag("MODULE-DATA").i("数据加载完成，共${items.size}条记录")
```
- **用途**: 重要的业务信息
- **场景**: 操作成功、重要状态变更
- **生产环境**: 可选输出

### WARN (w) - 警告
```kotlin
Timber.tag("MODULE-ERROR").w("网络连接不稳定，正在重试")
```
- **用途**: 潜在问题警告
- **场景**: 可恢复的错误、性能问题
- **生产环境**: 输出

### ERROR (e) - 错误
```kotlin
Timber.tag("MODULE-ERROR").e(exception, "用户资料保存失败")
```
- **用途**: 错误和异常
- **场景**: 操作失败、异常捕获
- **生产环境**: 输出

## ⚙️ 模块级别控制

### 运行时控制示例
```kotlin
class DebugSettingsViewModel @Inject constructor(
    private val timberManager: TimberManager
) {
    fun enableProfileDebugLogs() {
        timberManager.loggingConfig.updateModuleConfig(
            "profile",
            LoggingConfig.ModuleLogConfig(
                enabled = true,
                minLevel = Log.DEBUG,
                tags = setOf("PROFILE-UI", "PROFILE-DATA", "PROFILE-ERROR"),
                sampleRate = 1
            )
        )
    }

    fun enableThinkingBoxDebugLogs() {
        timberManager.enableThinkingBoxDebugLogs()
    }
}
```

## 🆔 CompactIdGenerator 统一ID生成

### 基本使用
```kotlin
import com.example.gymbro.core.util.CompactIdGenerator

// 生成6位压缩ID
val id = CompactIdGenerator.generateId() // 输出: K7M9P2

// 生成带前缀的ID
val messageId = CompactIdGenerator.generateId("msg") // 输出: msg_K7M9P2
val sessionId = CompactIdGenerator.generateId("session") // 输出: session_K7M9P2
```

### 在日志中使用
```kotlin
class WorkoutService {
    fun startWorkout(templateId: String) {
        val workoutId = CompactIdGenerator.generateId("workout")
        Timber.tag("WORKOUT-START").i("开始训练: templateId=$templateId, workoutId=$workoutId")

        // 后续日志都使用相同的workoutId进行追踪
        Timber.tag("WORKOUT-PROGRESS").d("训练进度更新: workoutId=$workoutId")
    }
}
```

### ID格式规范
- **消息ID**: `msg_K7M9P2`
- **会话ID**: `session_user123_1704067200000_K7M9P2`
- **模板ID**: `tmpl_user123_K7M9P2`
- **训练ID**: `workout_K7M9P2`
- **请求ID**: `req_K7M9P2`

## 🎯 ThinkingBox专用模式

### 自动启用
```kotlin
// GymBroApp中自动启用ThinkingBox模式
timberManager.initializeWithThinkingBoxSupport(BuildConfig.DEBUG)
```

### 专用标签
```kotlin
// 🔥 【标签规范】使用TB模块简写前缀
Timber.tag("TB-CONTENT").d("ThinkingBox内容更新")
Timber.tag("TB-UI").d("ThinkingBox UI状态变更")
Timber.tag("TB-ERROR").e("ThinkingBox处理错误", exception)
Timber.tag("TB-RAW-COLLECTOR").d("Token收集状态")
```

## 🌐 Core-Network专用模式

### 网络日志标签
```kotlin
// 🔥 【网络模块】使用CNET前缀标签
Timber.tag("CNET-ERROR").e("网络连接失败", exception)
Timber.tag("CNET-STREAM").d("开始流式传输")
Timber.tag("CNET-CHECK").i("网络连接检查")
Timber.tag("CNET-SSE").d("SSE连接状态更新")
Timber.tag("CNET-MONITOR").i("网络状态监控")
Timber.tag("CNET-RETRY").w("网络重试机制触发")
Timber.tag("CNET-LIFECYCLE").d("网络组件生命周期")
Timber.tag("CNET-SECURITY").w("网络安全检查")
Timber.tag("CNET-PERF").i("网络性能监控")
```

### 测试网络日志
```kotlin
// 🔥 【测试工具】验证CNET标签是否正常工作
NetworkLogTree.testNetworkLogging()
```

## 🚫 禁止事项

### ❌ 错误用法
```kotlin
// 不要使用硬编码字符串
Timber.d("用户登录")

// 不要使用System.out.println
System.out.println("调试信息")

// 不要在生产环境输出敏感信息
Timber.i("用户密码: $password")

// 不要使用过长的日志消息
Timber.d("非常长的日志消息..." + data.toString())
```

### ✅ 正确用法
```kotlin
// 使用标准化标签
Timber.tag("AUTH-UI").d("用户登录")

// 使用Timber统一日志
Timber.tag("AUTH-DEBUG").d("调试信息")

// 过滤敏感信息
Timber.tag("AUTH-DATA").i("用户认证成功: userId=$userId")

// 控制日志长度
Timber.tag("DATA-DEBUG").d("数据加载: ${data.summary()}")
```

## 🔧 高级配置

### 自定义Tree
```kotlin
class CustomDebugTree : Timber.DebugTree() {
    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        // 自定义日志处理逻辑
        super.log(priority, tag, "[${Thread.currentThread().name}] $message", t)
    }
}
```

### 环境切换
```kotlin
timberManager.switchEnvironment(LoggingConfig.Environment.PRODUCTION)
```

## 📝 最佳实践总结

1. **统一使用Timber**: 所有日志都通过Timber输出
2. **标准化标签**: 使用模块-功能的标签命名规范
3. **合理分级**: 根据重要性选择合适的日志级别
4. **保护隐私**: 避免输出敏感信息
5. **性能考虑**: 避免在热点路径中输出过多日志
6. **模块控制**: 利用TimberManager进行模块级别的日志控制
7. **Clean Architecture**: Domain层使用Logger接口，其他层使用Timber
8. **🔄 统一ID生成**: 使用CompactIdGenerator生成压缩ID，提高日志可读性
9. **🔄 ID追踪**: 在相关日志中使用相同ID进行操作追踪

## 🔗 相关文件

- `TimberManager.kt` - 核心日志管理器
- `Logger.kt` - Domain层日志接口
- `TimberLogger.kt` - Logger接口实现
- `LoggingConfig.kt` - 日志配置管理
- `TimberTrees.kt` - 各种Tree实现
- `SensitiveDataFilter.kt` - 敏感数据过滤
- `CompactIdGenerator.kt` - 🔄 统一ID生成器
- `LogTagOptimizer.kt` - 🔄 日志标签优化器
- `OptimizedLogger.kt` - 🔄 优化日志工具

---

**更新日期**: 2025-01-26
**版本**: v2.0 (集成CompactIdGenerator)
**维护者**: GymBro开发团队
