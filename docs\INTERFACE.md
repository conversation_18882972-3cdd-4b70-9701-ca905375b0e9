# GymBro 模块接口文档

## Coach模块接口

### 核心参数
- **messageId**: String (UUID格式) - 统一消息标识
- **sessionId**: String (UUID格式) - 会话标识

### 主要Effect
```kotlin
// AI请求Effect
data class StartAiStream(
    val prompt: String,
    val sessionId: String,
    val messageId: String, // 统一ID
) : Effect

// ThinkingBox启动Effect  
data class LaunchThinkingBoxDisplay(
    val messageId: String, // 与StartAiStream使用相同ID
) : Effect

// 消息保存Effect
data class SaveAiMessage(
    val sessionId: String,
    val messageId: String, // 统一ID
    val content: String,
) : Effect
```

### 数据流接口
```kotlin
// 输入: 用户消息
SendMessage(content: String) 
→ MessagingReducerHandler.handle()
→ 生成统一messageId
→ StartAiStream(messageId) + LaunchThinkingBoxDisplay(messageId)
```

## Core-Network模块接口

### 核心参数
- **conversationId**: String - 消息路由标识 (等于Coach的messageId)
- **OUTPUT_TOKEN_BATCH_SIZE**: Int = 1 - 实时处理，无批量

### 主要服务
```kotlin
// 统一AI响应处理
suspend fun processAiStreamingResponse(
    request: ChatRequest,
    messageId: String // 作为conversationId使用
): Flow<String>

// DirectOutputChannel token分发
fun sendToken(
    token: String,
    conversationId: String, // 等于messageId
    contentType: ContentType,
    metadata: Map<String, Any>
)

// 订阅接口
fun subscribeToConversation(
    conversationId: String // 等于messageId
): Flow<OutputToken>
```

### 数据流接口
```kotlin
// 处理流程
Coach.StartAiStream(messageId) 
→ UnifiedAiResponseService.processAiStreamingResponse(request, messageId)
→ DirectOutputChannel.sendToken(token, conversationId=messageId)
→ ThinkingBox.subscribeToConversation(messageId)
```

## ThinkingBox模块接口

### 核心参数
- **messageId**: String - 订阅标识 (等于Coach的messageId)
- **四条铁律**: 性能优化参数

### 主要组件
```kotlin
// 流适配器
class ThinkingBoxStreamAdapter {
    fun startDirectOutputProcessing(messageId: String)
    // 订阅DirectOutputChannel(messageId)
}

// 启动器
interface ThinkingBoxLauncher {
    fun startThinking(
        messageId: String, // 订阅标识
        context: ThinkingContext
    )
}
```

### 数据流接口
```kotlin
// 订阅流程
Coach.LaunchThinkingBoxDisplay(messageId)
→ ThinkingBoxLauncher.startThinking(messageId)
→ ThinkingBoxStreamAdapter.startDirectOutputProcessing(messageId)
→ DirectOutputChannel.subscribeToConversation(messageId)
→ 实时接收和显示token
```

## 模块间通信协议

### ID统一协议
1. **Coach生成messageId**: UUID格式，全局唯一
2. **Core-Network使用conversationId**: 等于Coach的messageId
3. **ThinkingBox订阅messageId**: 等于Coach的messageId
4. **确保ID一致性**: 整个数据流使用相同标识

### 数据流协议
```
用户输入 → Coach(生成messageId) 
       → StartAiStream(messageId) + LaunchThinkingBoxDisplay(messageId)
       → Core-Network(conversationId=messageId) 
       → DirectOutputChannel(conversationId=messageId)
       → ThinkingBox(订阅messageId)
       → 实时显示AI响应
```

### 错误处理协议
- **ID不匹配**: ThinkingBox收不到响应 → 检查messageId一致性
- **订阅失败**: 检查DirectOutputChannel状态
- **token丢失**: 检查OUTPUT_TOKEN_BATCH_SIZE=1设置

## 性能参数

### Coach模块
- **messageId生成**: UUID.randomUUID().toString()
- **Effect处理**: 同步生成，确保ID一致

### Core-Network模块  
- **OUTPUT_TOKEN_BATCH_SIZE**: 1 (实时处理)
- **TokenLogCollector刷新**: 50ms
- **DirectOutputChannel缓冲**: 无缓冲，实时分发

### ThinkingBox模块
- **订阅延迟**: <10ms
- **UI渲染**: 四条铁律优化
- **打字机效果**: 33ms/字符

## 调试接口

### 关键日志标签
```kotlin
// Coach模块
Timber.tag("TOKEN-FLOW").d("StartAiStream Effect: messageId=$messageId")
Timber.tag("TOKEN-FLOW").d("LaunchThinkingBoxDisplay Effect: messageId=$messageId")

// Core-Network模块  
Timber.tag("UNIFIED-AI").d("处理AI流式响应: messageId=$messageId")
Timber.tag("DIRECT-OUTPUT").d("发送token: conversationId=$messageId")

// ThinkingBox模块
Timber.tag("TB-ADAPTER").d("开始订阅: messageId=$messageId")
Timber.tag("TB-SUCCESS").d("接收到数据: messageId=$messageId")
```

### 验证命令
```bash
# 检查ID一致性
adb logcat | grep "messageId=" | head -20

# 检查token流
adb logcat | grep -E "(sendToken|subscribeToConversation)"

# 检查ThinkingBox响应
adb logcat | grep -E "(TB-ADAPTER|TB-SUCCESS)"
```
